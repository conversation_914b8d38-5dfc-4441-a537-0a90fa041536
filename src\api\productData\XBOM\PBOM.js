import request from '@/utils/request'

// P-BOM --- 列表查询
export function queryPBomList(params) {
  return request({
    url: `/api/ProductPbomController/searchAll`,
    method: 'GET',
    data: params
  })
}

// E-bom --- 列表查询(用来做P-BOM 新增)
export function queryEBomList(params) {
  return request({
    url: `/api/ProductEbomController/searchAll`,
    method: 'GET',
    data: params
  })
}

// P-BOM --- 列表修改
export function editPBomList(data) {
  return request({
    url: `/api/ProductPbomController/update`,
    method: 'POST',
    data
  })
}


// P-BOM --- 列表删除
export function deletePBomList(id) {
  return request({
    url: `/api/ProductPbomController/${id}`,
    method: 'DELETE',
  })
}

// P-BOM --- 列表单挑数据详情
export function queryPBomDetails(id) {
  return request({
    url: `/api/ProductPbomController/getById/${id}`,
    method: 'GET',

  })
}


// P-BOM --- 获取物料编码
export function getMaterialCode(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataHisController/queryByLike`,
    method: 'POST',
    data
  })
}


// P-BOM 结构 表格数据新增
export function addStructureList(data) {
  return request({
    url: `/api/ProductPbomConstructController/sava`,
    method: 'POST',
    data
  })
}

// P-BOM 结构 表格数据查询
export function queryStructureList(data) {
  return request({
    url: `/api/ProductPbomConstructController/search`,
    method: 'POST',
    data
  })
}

export function queryStructureListVersion(data) {
  return request({
    url: `/api/ProductPbomConstructController/searchHis`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构
export function searchTree(data) {

  return request({
    url: `/api/ProductPbomController/searchTree`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构 点击子级展示关联子级
export function searchTreeSon(data) {
  return request({
    url: `/api/ProductPbomController/searchTreeSon`,
    method: 'POST',
    data
  })
}


// E-BOM 查看 左侧的树形结构 反查
export function peggingTree(data) {
  return request({
    url: `/api/ProductPbomController/peggingTree`,
    method: 'POST',
    data
  })
}

// BOM 比较
// BOM 比较 获取 BOM 的版本号
export function bomVersion(code) {
  return request({
    url: `/api/ProductPbomConstructController/getVersion/${code}`,
    method: 'GET',
  })
}


// BOM 比较 比较 BOM
export function bomCompare(data) {
  return request({
    url: `/api/ProductPbomConstructController/contrast/${data.codeOne}/${data.versionOne}/${data.codeTwo}/${data.versionTwo}`,
    method: 'GET',

  })
}


// P-BOM 图纸 表格数据查询
export function queryDrawList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/getInfoByPbom`,
    method: 'POST',
    data
  })
}

/**
 * @name pbom关联ebom回显
 * @param {*} id 
 * @returns 
 */
export const provideBom = (id) => request({
  url: `/api/ProductEbomController/provideBom/${id}`,
  method: 'GET',
})




/**
 * @name P-bom列表新增 
 * @param {*} param0.bomName bom名称
 * @param {*} param0.materialCode 物料编号
 * @param {*} param0.materialVersion 物料版本
 * @param {*} param0.businessType 业务类型
 * @param {*} param0.remark 备注
 * @param {*} param0.pconstructs 结构数据
 * @returns 
 */
export function addPBomList({
  bomName,
  materialCode,
  materialVersion,
  businessType,
  remark,
  category,
  materialName,
  pconstructs
}) {
  return request({
    url: `/api/ProductPbomController/save`,
    method: 'POST',
    data: {
      bomName,
      materialCode,
      materialVersion,
      businessType,
      remark,
      category,
      materialName,
      pconstructs
    }
  })
}



/**
 * @name P-bom变更
 * @param {*} bomcode  bom 编码
 * @param {*} version  bom 版本
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function getBomInfo(data) {
  return request({
    url: `/api/ProductPbomController/getInfoByCode`,
    method: 'POST',
    data
  })
}



/**
 * @name P-bom变更
 * @param {*} constructs  修订的表格数据
 * @param {*} id  主键ID
 * @param {*} code  bom编码
 * @param {*} version  bom版本
 * @param {*} materialCode  物料编码
 * @param {*} materialVersion  物料版本
 * @param {*} materialName  物料名称
 * @param {*} category  归属目录
 * @param {*} materialSpecs  物料规格型号
 * @param {*} preChangeContent  变更前内容
 * @param {*} revisedContent  变更后内容
 * @param {*} specificReasons  具体内容
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function saveRevision({
  constructs,
  id,
  code,
  version,
  materialCode,
  materialVersion,
  materialName,
  category,
  materialSpecs,
  preChangeContent,
  revisedContent,
  specificReasons
}) {
  return request({
    url: `/api/ProductPbomController/updateInfoByCodeAndVersion`,
    method: 'POST',
    data: {
      constructs,
      id,
      code,
      version,
      materialCode,
      materialVersion,
      materialName,
      category,
      materialSpecs,
      preChangeContent,
      revisedContent,
      specificReasons
    }
  })
}


// BOM 比较 查询 PBOM  归档数据
export function queryComparePBomList(data) {
  return request({
    url: `/api/ProductPbomController/searchHisPage`,
    method: 'POST',
    data
  })
}

export function downloadTemplateForAll() {
  return request({
    method: 'get',
    url: `/api/ProductPbomController/downloadTemplateForAll`,
    responseType: 'blob',
  })
}

export function importData(data) {
  return request({
    url: `/api/ProductPbomController/importData`,
    method: 'POST',
    data
  })
}