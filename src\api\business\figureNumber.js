import request from '@/utils/request'

// 获取物料图号 、文档图号 
export function getTreeList(innerName) {
  return request({
    url: `/api/ProductDrawNumberController/treeList?innerName=${innerName}`,
    method: 'GET'
  })
}


/**
 * @name 修改物料和文档的图号接口
 * @param entityId 业务关联id t_entity_type
 * @param drawNumber 图号
 * @param drawNumber 起始编号
 * @param id 主键
 * @param remark 备注
 * @returns 
 */
// 修改物料编图号、文档图号
export function editTreeList(
  {
    entityId,
    drawNumber,
    startNumber,
    id,
    remark,
  }
) {
  return request({
    url: `/api/ProductDrawNumberController/update`,
    method: 'POST',
    data: {
      entityId,
      drawNumber,
      startNumber,
      id,
      remark,
    }
  })
}