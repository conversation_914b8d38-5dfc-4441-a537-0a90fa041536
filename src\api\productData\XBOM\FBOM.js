import request from '@/utils/request'

// f-bom --- 分页查询
export function queryFBomList(params) {
  return request({
    url: `/api/ProductFbomController/queryByLike`,
    method: 'GET',
    data: params
  })
}

// f-bom --- 新增
export function addFBomList(data) {
  return request({
    url: `/api/ProductFbomController/save`,
    method: 'POST',
    data
  })
}

// f-bom --- 删除
export function deleteFBomList(id) {
  return request({
    url: `/api/ProductFbomController/${id}`,
    method: 'DELETE',
  })
}

// f-bom --- 修改
export function editFBomList(data) {
  return request({
    url: `/api/ProductFbomController/update`,
    method: 'PUT',
    data
  })
}

// f-bom --- 查看详情
export function checkFBomList(id) {
  return request({
    url: `/api/ProductFbomController/detail/${id}`,
    method: 'GET',
  })
}


// F-BOM 目录


// 新增目录
export function addDirectory(data) {
  return request({
    url: `/api/ProductFbomLibraryController/save`,
    method: 'POST',
    data
  })
}

// 删除目录
export function deleteDirectory(id) {
  return request({
    url: `/api/ProductFbomLibraryController/${id}`,
    method: 'DELETE',
  })
}

// 修改目录
export function modifyDirectory(data) {
  return request({
    url: `/api/ProductFbomLibraryController/update`,
    method: 'PUT',
    data
  })
}

// 目录查询
export function queryDirectory(data) {
  return request({
    url: `/api/ProductFbomLibraryController/queryAll?code=${data.code}&version=${data.version}&id=${data.id}`,
    method: 'GET',
  })
}




// F-BOM BOM 数据

// BOM 列表分页查询
export function queryBOMList(data) {
  return request({
    url: `/api/ProductFbomLibraryDataController/queryByLike`,
    method: 'POST',
    data
  })
}
// BOM 列表 新增
export function addBOMList(data) {
  return request({
    url: `/api/ProductFbomLibraryDataController/save`,
    method: 'POST',
    data
  })
}
// BOM 列表 修改
export function editBOMList(data) {
  return request({
    url: `/api/ProductFbomLibraryDataController/update`,
    method: 'PUT',
    data
  })
}

// BOM 列表 查询
export function queryBOMListDetails(id) {
  return request({
    url: `/api/ProductFbomLibraryDataController/${id}`,
    method: 'GET',
  })
}

// BOM 列表 删除
export function deleteBOMList(id) {
  return request({
    url: `/api/ProductFbomLibraryDataController/${id}`,
    method: 'DELETE',
  })
}


// 文档文件管理

// 文档文件批量新增
export function documenBatchAddt(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/addBatch`,
    method: 'POST',
    data
  })
}

// 文档文件查询列表
export function queryDocumen(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/getInfoByFunctionCode`,
    method: 'POST',
    data
  })
}
