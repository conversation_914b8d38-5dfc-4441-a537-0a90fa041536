import request from '@/utils/request'
// 分页查询工作负荷列表
export function getWorkLoadList(data) {
  return request({
    url: `/api/work/load`,
    method: 'GET',
    data
  })
}

// 获取项目列表（无分页）
export function getPorjectList(data) {
  return request({
    url: `/api/project/list/not/page`,
    method: 'GET',
    data
  })
}
// 项目角色列表：无分页
export function getPorjectUserList(projectCode) {
  return request({
    url: `/api/project/role/power/list/not/page/${projectCode}`,
    method: 'GET',
  })
}
// 查单个任务负责人的工时饱和度
export function getLoadUser(data) {
  return request({
    url: `/api/work/load/user`,
    method: 'GET',
    data
  })
}