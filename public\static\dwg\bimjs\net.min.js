﻿'use strict';
var ID_TOKEN = "id_token", USER_ID = "user_id", TOKEN_TIME = "TOKEN_TIME", IS_DEBUG = 0, ontToken = null,
  NetHandle = function (a) {
    this.option = new QmodelOption;
    this.CURRENT_MODEL_PATH = this.CURRENT_MODEL_ID = this._0xC9 = null;
    this.MDV = !1;
    var c = localStorage.getItem(TOKEN_TIME);
    c ? 12 < (new Date((new Date).getTime() - (new Date(c)).getTime())).getHours() && this.clear_TOKEN() : this.clear_TOKEN();
    a ? this.WEB_TOKEN = a : "null" != localStorage.getItem(ID_TOKEN) && (this.WEB_TOKEN = localStorage.getItem(ID_TOKEN), this.WEB_USER_ID = localStorage.getItem(USER_ID))
  };
NetHandle.prototype = {
  _0xA07: function () {
    return "Bearer " + this.WEB_TOKEN
  }, get_VUE_TOKEN_HEAD: function () {
    return { headers: { Authorization: "Bearer " + this.WEB_TOKEN, Accept: "application/json" } }
  }, get_TOKEN: function () {
    return this.WEB_TOKEN
  }, clear_TOKEN: function () {
    localStorage.removeItem(ID_TOKEN);
    localStorage.removeItem(USER_ID);
    $.cookie(ID_TOKEN, null, { path: "/" });
    this.WEB_TOKEN = null;
    $.cookie(USER_ID, null, { path: "/" });
    this.WEB_USER_ID = null
  }, _0xA08: function () {
    return this._0xC9
  },
  // getPdfPath: function (a, c) {
  //   c = c.split(",");
  //   var e = [];
  //   a = this.option.FILE_SERVE_URL2 + a;
  //   for (var f = 0; f < c.length; f++) e.push(a);
  //   return e
  // },
  getPdfPath: function (a, c) {
    // c = c.split(",");
    // var e = [];
    let b = this.option.FILE_SERVE_URL2 + a + '&fileDownloadUrl=' + c;
    // for (var f = 0; f < c.length; f++) e.push(a);
    return b
  },
  getGltfPath2: function (a) {
    return this.option.FILE_SERVE_URL2 + a
  }, getGltfPath: function (a) {
    return this.option.FILE_SERVE_URL2 + a.substring(0, a.length - 4)
  }, getModels: function (a) {
    var c = this, e = c.option.API_URL + "/rvt-files-v2?page\x3d0\x26size\x3d" + (a ? a : 100);
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          console.log(b);
          for (var d = [], g = 0; g < b.length; g++) 0 === b[g].isDelete && d.push(b[g]);
          a(d)
        }, error: function (b, a, g) {
          d(a)
        }
      })
    })
  }, getDwgs: function (a) {
    var c = this, e = c.option.API_URL + "/dwgfiles?page\x3d0\x26size\x3d" + (a ? a : 100);
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          console.log(b);
          for (var d = [], g = 0; g < b.length; g++) 1 == b[g].mark && d.push(b[g]);
          a(d)
        }, error: function (b, a, g) {
          d(a)
        }
      })
    })
  }, getGltfs: function (a) {
    var c = this, e = this.option.API_URL + "/gltffiles?page\x3d0\x26size\x3d" + (a ? a : 100);
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          console.log(b);
          for (var d = [], g = 0; g < b.length; g++) 1 == b[g].mark && d.push(b[g]);
          a(d)
        }, error: function (b, a, g) {
          d(a)
        }
      })
    })
  }, getToken: function (a, c) {
    var e = this;
    return new Promise(function (f, d) {
      if (null != e.WEB_TOKEN) null == localStorage.getItem(USER_ID) && localStorage.setItem(USER_ID, a), f(1); else {
        var b = { username: a ? a : e.option.DemoUser, password: a ? c : e.option.DemoPwd, rememberMe: null };
        $.ajax({
          cache: !1,
          url: e.option.API_URL + "/authenticate",
          type: "POST",
          contentType: "application/json;charset\x3dUTF-8",
          data: JSON.stringify(b),
          success: function (a) {
            console.log(a);
            localStorage.setItem(ID_TOKEN, a.id_token);
            e.WEB_TOKEN = a.id_token;
            localStorage.setItem(USER_ID, b.username);
            e.WEB_USER_ID = b.username;
            localStorage.setItem(TOKEN_TIME, new Date);
            f(1)
          },
          error: function (b, a, c) {
            401 == a && alert("\u5bc6\u7801\u9519\u8bef\u3002");
            d(0)
          }
        })
      }
    })
  }, postFileInfo: function (a, c, e, f, d) {
    var b = { expType: 1, fileName: a, filePath: c, fileSize: e, fileId: d, id: 0, isDelete: 0, userId: f }, h = this,
      g = this.option.API_URL + "/rvt-src-files";
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1,
        url: g,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(b),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", h._0xA07())
        },
        success: function (b) {
          a(1)
        },
        error: function (b, a, g) {
          d(a)
        }
      })
    })
  }, uploadFile: function (a, c) {
    var e = this;
    return new Promise(function (f, d) {
      d = new FormData;
      d.append("file", a);
      d.append("service", "App.Passion.UploadFile");
      var b = "/" + localStorage.getItem(USER_ID) + "/rvt/", h = { name: "file" };
      h.filename = a.filename;
      $.ajax({
        url: e.option.FILE_SERVE_URL_UP + b + "\x26needTimeStamp\x3d1",
        type: "post",
        data: d,
        contentType: !1,
        processData: !1,
        headers: h,
        xhr: function () {
          var b = new XMLHttpRequest;
          b.upload.addEventListener("progress", c);
          return b
        },
        success: function (b) {
          console.log(b);
          "success" == b.status ? e.postFileInfo(b.result[0].fileName, b.result[0].filePath, b.result[0].fileSize, 0, b.result[0].id).then(function (b) {
            f(1)
          }) : f(0)
        }
      })
    })
  }, postDwgFileInfo: function (a, c, e, f, d) {
    var b = { dwgName: a, filePath: c, fileSize: e, fileId: d, id: 0 }, h = this, g = this.option.API_URL + "/dwgfiles";
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1,
        url: g,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(b),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", h._0xA07())
        },
        success: function (b) {
          a(1)
        },
        error: function (b, a, g) {
          d(a)
        }
      })
    })
  }, postGltfFileInfo: function (a, c, e, f, d) {
    var b = { gltfName: a, filePath: c, fileSize: e, fileId: d, mark: 1, id: 0 }, h = this,
      g = this.option.API_URL + "/gltffiles";
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1,
        url: g,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(b),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", h._0xA07())
        },
        success: function (b) {
          a(1)
        },
        error: function (b, a, g) {
          d(a)
        }
      })
    })
  }, uploadGltfFile: function (a, c) {
    var e = this;
    return new Promise(function (f, d) {
      d = new FormData;
      d.append("file1", a);
      d.append("service", "App.Passion.UploadFile");
      var b = a.name.substring(0, a.name - 4),
        b = "/" + localStorage.getItem(USER_ID) + "/gltf" + (new Date).getTime().toString() + "/" + b,
        h = { name: "file" };
      h.filename = a.filename;
      $.ajax({
        url: e.option.FILE_SERVE_URL_UP + b + "\x26needTimeStamp\x3d2",
        type: "post",
        data: d,
        contentType: !1,
        processData: !1,
        headers: h,
        xhr: function () {
          var b = new XMLHttpRequest;
          b.upload.addEventListener("progress", c);
          return b
        },
        success: function (b) {
          console.log(b);
          "success" == b.status ? e.postGltfFileInfo(b.result[0].fileName, b.result[0].filePath, b.result[0].fileSize, b.result[0].id).then(function (b) {
            f(1)
          }) : f(0)
        }
      })
    })
  }, uploadDwgFile: function (a, c) {
    var e = this;
    return new Promise(function (f, d) {
      d = new FormData;
      d.append("file", a);
      d.append("service", "App.Passion.UploadFile");
      var b = "/" + localStorage.getItem(USER_ID) + "/dwg/", h = { name: "file" };
      h.filename = a.filename;
      $.ajax({
        url: e.option.FILE_SERVE_URL_UP + b + "\x26needTimeStamp\x3d2",
        type: "post",
        data: d,
        contentType: !1,
        processData: !1,
        headers: h,
        xhr: function () {
          var b = new XMLHttpRequest;
          b.upload.addEventListener("progress", c);
          return b
        },
        success: function (b) {
          console.log(b);
          "success" == b.status ? e.postDwgFileInfo(b.result[0].fileName, b.result[0].filePath, b.result[0].fileSize, b.result[0].id).then(function (b) {
            f(1)
          }) : f(0)
        }
      })
    })
  }, _0xA09_new: function (a) {
    return 1 == IS_DEBUG ? this.option.FILE_SERVE_URL2 + "/" + this.WEB_USER_ID + "/" + this._0xC9 + "/" + a : this.option.FILE_SERVE_URL + this.CURRENT_MODEL_PATH + "/" + a
  }, _0x3B1: function (a, c) {
    var e = { docId: a, fileId: this._0xC9, uniqueId: c }, f = this, d = this.option.API_URL + "/pty-saves-getone";
    return new Promise(function (b, a) {
      $.ajax({
        cache: !1,
        url: d,
        type: "POST",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(e),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", f._0xA07())
        },
        success: function (a) {
          b(a.prmgs)
        },
        error: function (b, d, c) {
          a(d)
        }
      })
    })
  }, getModel: function (a) {
    var c = this, e = this.option.API_URL + "/rvt-files-byname/" + a;
    return new Promise(function (f, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          console.log(b);
          c._0xC9 = b.fileId;
          c.CURRENT_MODEL_ID = c.WEB_USER_ID + "-\x3e" + a;
          c.MDV = "1" == b.ex1;
          c.CURRENT_MODEL_PATH = b.filePath;
          f(c.option.FILE_SERVE_URL + b.filePath)
        }, error: function (b, a, c) {
          d(a)
        }
      })
    })
  }, saveSche: function (a, c) {
    var e = {
      actPlanDate: c,
      actfinishDate: c,
      finishDate: c,
      planDate: c,
      rvtId: this.CURRENT_MODEL_ID,
      taskName: a,
      id: 0
    }, f = this, d = this.option.API_URL + "/task-mgrs";
    return new Promise(function (b, a) {
      $.ajax({
        cache: !1,
        url: d,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(e),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", f._0xA07())
        },
        success: function (a) {
          b(1)
        },
        error: function (b, d, c) {
          a(0)
        }
      })
    })
  }, getSche: function () {
    var a = this, c = this.option.API_URL + "/task-mgrs-by-fileid?fileid\x3d" + this.CURRENT_MODEL_ID;
    return new Promise(function (e, f) {
      $.ajax({
        cache: !1, url: c, type: "GET", beforeSend: function (d) {
          d.setRequestHeader("Authorization", a._0xA07())
        }, success: function (a) {
          console.log(a);
          e(a)
        }, error: function (a, b, c) {
          f(b)
        }
      })
    })
  }, DelSche: function (a) {
    var c = this, e = this.option.API_URL + "/task-mgrs/" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1,
        url: e,
        type: "DELETE",
        contentType: "application/json;charset\x3dUTF-8",
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        },
        success: function (b) {
          a(1)
        },
        error: function (b, a, c) {
          d(0)
        }
      })
    })
  }, BangdingSche: function (a, c) {
    var e = [];
    c.forEach(function (b) {
      e.push({ id: 0, compId: b, taskId: a, vtype: 0 })
    });
    var f = this, d = this.option.API_URL + "/task-and-comps-group";
    return new Promise(function (b, a) {
      $.ajax({
        cache: !1,
        url: d,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(e),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", f._0xA07())
        },
        success: function (a) {
          b(1)
        },
        error: function (b, d, c) {
          a(0)
        }
      })
    })
  }, getBangdingSche: function (a) {
    var c = this, e = this.option.API_URL + "/task-and-comps-group?taskId\x3d" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          console.log(b);
          a(b)
        }, error: function (b, a, c) {
          d(a)
        }
      })
    })
  }, saveViewAndImg: function (a, c, e, f) {
    var d = this;
    return new Promise(function (b, h) {
      h = new FormData;
      var g = (new Date).getTime() + ".jpg", g = new File([f], g, { type: "image/jpg" });
      h.append("file", g);
      h.append("service", "App.Passion.UploadFile2");
      var k = "/" + d.WEB_USER_ID + "/" + d._0xC9 + "/img/", l = { name: "file" };
      l.filename = g.filename;
      $.ajax({
        url: d.option.FILE_SERVE_URL_UP + k + "\x26needTimeStamp\x3d1",
        type: "post",
        data: h,
        contentType: !1,
        processData: !1,
        headers: l,
        success: function (f) {
          console.log(f);
          "success" == f.status ? d.saveView(a, c, e, f.result[0].filePath).then(function (a) {
            b(a)
          }) : b(0)
        }
      })
    })
  }, saveView: function (a, c, e, f) {
    var d = {
      fileId: this.CURRENT_MODEL_ID,
      viewName: a,
      viewInfo2: c,
      markInfo: e,
      imageFilePath: f,
      updateDate: new Date,
      id: 0
    }, b = this, h = this.option.API_URL + "/rvt-views";
    return new Promise(function (a, c) {
      $.ajax({
        cache: !1,
        url: h,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(d),
        beforeSend: function (a) {
          a.setRequestHeader("Authorization", b._0xA07())
        },
        success: function (b) {
          console.log(b.id);
          a(b)
        },
        error: function (b, a, d) {
          c(0)
        }
      })
    })
  }, getView: function (a) {
    var c = this;
    a = a ? c.WEB_USER_ID + "-\x3e" + a : this.CURRENT_MODEL_ID;
    var e = this.option.API_URL + "/rvt-views-byfileid?fileid\x3d" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          b = b.reverse().slice(0, 10);
          console.log(b);
          a(b)
        }, error: function (b, a, c) {
          d(a)
        }
      })
    })
  }, getImg_v2: function (a) {
    return this.option.FILE_SERVE_URL + a
  }, getImg: function (a) {
    var c = this;
    a = a ? c.WEB_USER_ID + "-\x3e" + a : this.CURRENT_MODEL_ID;
    var e = this.option.API_URL + "/rvt-firstviews-byfileid?fileid\x3d" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          console.log(b);
          "0" !== b && (b = c.option.FILE_SERVE_URL + b);
          a(b)
        }, error: function (b, a, c) {
          d(a)
        }
      })
    })
  }, getDefaultView: function (a) {
    var c = this;
    a = a ? c.WEB_USER_ID + "-\x3e" + a : this.CURRENT_MODEL_ID;
    var e = this.option.API_URL + "/rvt-defaultviews-byfileid?fileid\x3d" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          b ? (console.log(b), a(b)) : d(b)
        }, error: function (b, a, c) {
          d(a)
        }
      })
    })
  }, getViewById: function (a) {
    var c = this, e = this.option.API_URL + "/rvt-views/" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1, url: e, type: "GET", beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        }, success: function (b) {
          a(b)
        }, error: function (b, a, c) {
          d(a)
        }
      })
    })
  }, DelView: function (a) {
    var c = this, e = this.option.API_URL + "/rvt-views/" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1,
        url: e,
        type: "DELETE",
        contentType: "application/json;charset\x3dUTF-8",
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", c._0xA07())
        },
        success: function (b) {
          a(1)
        },
        error: function (b, a, c) {
          d(0)
        }
      })
    })
  }, saveExtModel: function (a, c, e, f, d, b) {
    var h = {
      equipmentId: c,
      equipmentName: e,
      fileId: this.CURRENT_MODEL_ID,
      equipmentTransform: f,
      viewInfo: d,
      markInfo: b,
      updateDate: new Date,
      visibled: 1,
      id: a
    }, g = this, k = this.option.API_URL + "/equipment-comps";
    return new Promise(function (b, a) {
      $.ajax({
        cache: !1,
        url: k,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(h),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", g._0xA07())
        },
        success: function (a) {
          console.log(a.id);
          b(a)
        },
        error: function (b, d, c) {
          a(0)
        }
      })
    })
  }, getExtModel: function () {
    var a = this, c = this.option.API_URL + "/equipment-comps-byfileid?fileId\x3d" + this.CURRENT_MODEL_ID;
    return new Promise(function (e, f) {
      $.ajax({
        cache: !1, url: c, type: "GET", beforeSend: function (d) {
          d.setRequestHeader("Authorization", a._0xA07())
        }, success: function (a) {
          console.log(a);
          e(a)
        }, error: function (a, b, c) {
          f(b)
        }
      })
    })
  }, setExtModelColor: function (a, c) {
    var e = {
      equipmentId: 0,
      equipmentName: "",
      fileId: "",
      equipmentTransform: "",
      viewInfo: "",
      markInfo: c,
      updateDate: new Date,
      visibled: 1,
      id: a
    }, f = this, d = this.option.API_URL + "/equipment-comps-color";
    return new Promise(function (b, a) {
      $.ajax({
        cache: !1,
        url: d,
        type: "POST",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(e),
        beforeSend: function (b) {
          b.setRequestHeader("Authorization", f._0xA07())
        },
        success: function (a) {
          console.log(a.id);
          b(a)
        },
        error: function (b, c, d) {
          a(0)
        }
      })
    })
  }, delExtModel: function (a) {
    var c = this, e = this.option.API_URL + "/equipment-comps/" + a;
    return new Promise(function (a, d) {
      $.ajax({
        cache: !1,
        url: e,
        type: "DELETE",
        contentType: "application/json;charset\x3dUTF-8",
        beforeSend: function (a) {
          a.setRequestHeader("Authorization", c._0xA07())
        },
        success: function (b) {
          a(1)
        },
        error: function (a, c, e) {
          d(0)
        }
      })
    })
  }, saveEquipmentFile: function (a, c) {
    var e = this;
    return new Promise(function (f, d) {
      d = new FormData;
      var b = (new Date).getTime() + ".json", h = new Blob([c], { type: "text/plain;charset\x3dutf-8" }),
        b = new File([h], b);
      d.append("file", b);
      d.append("service", "App.Passion.UploadFile2");
      var h = "/" + e.WEB_USER_ID + "/" + e._0xC9 + "/equipment/", g = { name: "file" };
      g.filename = b.filename;
      $.ajax({
        url: e.option.FILE_SERVE_URL_UP + h + "\x26needTimeStamp\x3d1",
        type: "post",
        data: d,
        contentType: !1,
        processData: !1,
        headers: g,
        success: function (b) {
          console.log(b);
          "success" == b.status ? e.saveEquipment(a, b.result[0].filePath).then(function (a) {
            f(a)
          }) : f(0)
        }
      })
    })
  }, saveEquipment: function (a, c) {
    var e = {
      fileId: this.CURRENT_MODEL_ID,
      userId: this.WEB_USER_ID,
      equipmentName: a,
      _0x4D: a,
      equipmentImage: "",
      equipmentFile: c,
      updateDate: new Date,
      id: 0
    }, f = this, d = this.option.API_URL + "/equipments";
    return new Promise(function (a, c) {
      $.ajax({
        cache: !1,
        url: d,
        type: "PUT",
        contentType: "application/json;charset\x3dUTF-8",
        data: JSON.stringify(e),
        beforeSend: function (a) {
          a.setRequestHeader("Authorization", f._0xA07())
        },
        success: function (b) {
          console.log(b.id);
          a(b)
        },
        error: function (a, b, d) {
          c(0)
        }
      })
    })
  }
};
