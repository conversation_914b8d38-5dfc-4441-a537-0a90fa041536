<template>
  <div v-if="visible">
    <div v-show="!dataInfo.link">
      <FullPage v-if="dataInfo.type == 'bom'" ref="rBomFullPageRef">
        <template #title>{{ dataInfo.name }}</template>
        <template #content>
          <TabsView ref="tabsViewRef" />
        </template>
      </FullPage>
      <ComponentSelection :visible.sync="selectionVisible" v-if="dataInfo.type == 'ComponentSelection'"
        ref="selection" />
    </div>

  </div>
</template>

<script>
import objData from './obj'
import FullPage from "@/components/FullPage";
import TabsView from "@/views/productData/xBom/components/TabsView.vue";
import ComponentSelection from "@/views/demand/pool/componentSelection/index.vue"
export default {
  components: { FullPage, TabsView, ComponentSelection },
  data() {
    return {
      dataInfo: {},
      visible: false,
      selectionVisible: false,
    };
  },
  provide() {
    return {
      tabsName: this.dataInfo
    }
  },
  mounted() { },
  methods: {
    initData(code, data) {
      console.log("code,data", code, data, this.dataInfo)
      this.visible = true
      this.dataInfo = objData.find(value => value.code == code)
      if (this.dataInfo.link) {
        if (this.dataInfo.type == 'bom') return this.$router.push(`${this.dataInfo.url}`)
        let routeData = this.$router.resolve(({
          path: this.dataInfo.url,
        }))
        window.open(routeData.href, '_blank')
      } else {
        if (this.dataInfo.type == 'bom') {
          /**XBOM */
          // this.$nextTick(() => {
          //   this.$refs.rBomFullPageRef.acceptParams();
          //   this.$nextTick(() => {
          //     this.$refs.tabsViewRef.activeName = this.dataInfo.value
          //   })
          // })
          this.$router.push(`${this.dataInfo.url}`)
        } else if (this.dataInfo.type == 'ComponentSelection') {
          this.selectionVisible = true
          this.$nextTick(() => {
            this.$refs['selection'].initData(data)
          })
        }

      }

    }
  },
};
</script>
<style lang="scss" scoped>
>>>.el-tabs__content {
  display: block !important;
}
</style>