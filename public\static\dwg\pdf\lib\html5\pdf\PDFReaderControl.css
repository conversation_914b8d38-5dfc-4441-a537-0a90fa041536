#pdf-progress-bar {
    opacity: 0.9;
    font-family: <PERSON><PERSON><PERSON>;
    font-size: 17px;
    color: #21578a;
    width: 300px;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -150px;
    margin-top: -30px;
}
#pdf-progress-bar .progress-bar{
    background: #f5f5f5;
    height: 0.75em;
    margin-top: 5px;
}
#pdf-progress-bar .progress-bar div{
    height: 0.75em;
    float: left;
    background: #f0ad4e;
    background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
    background-size: 40px 40px;
    animation: progress-bar-stripes 2s linear infinite;
}

@keyframes progress-bar-stripes {
    from {
        background-position: 40px 0
    }

    to {
        background-position: 0 0
    }
}

#pdf-progress-bar.document-failed .progress-bar div{
    background: #d9534f;
    width: 100% !important;
}

#downloadButton.rotate-icon{
    -webkit-animation:spin 4s linear infinite;
    -moz-animation:spin 4s linear infinite;
    animation:spin 4s linear infinite;
    margin: 0 6px;
	padding: 0;
    height: 24px;
    width: 24px;
}

#downloadButton.rotate-icon:before{
	padding: 0;
	font-size: 24px;
}

#printButton.rotate-icon{
    -webkit-animation:spin 4s linear infinite;
    -moz-animation:spin 4s linear infinite;
    animation:spin 4s linear infinite;
    margin: 0 6px;
    padding: 0;
}

#printButton.rotate-icon:before{
    padding: 0;
    font-size: 24px;
}

.download-drop {
    margin-top: -0.8em;
    margin-bottom: -0.6em;
    margin-left: -0.5em;
    margin-right: -0.5em;
}

.labelled-button-row{
    display: table;
    margin-top: 0.3em;
    border-bottom: 2px solid transparent;
    cursor: pointer;
}

.labelled-button-row:hover {
    color: #27a8e0;
    border-bottom: 2px solid rgb(39, 168, 224);
    cursor: pointer;
}

.labelled-button {
    display: table-cell;
    vertical-align: middle;
    padding-left: 36px;
}

.labelled-button-row:hover span{
    color: rgb(39, 168, 224);
}

.labelled-button-row:hover .glyphicons:before{
    color: rgb(39, 168, 224) !important;
}

.labelled-button-text{
    display: table-cell;
    vertical-align: middle;
    text-align: left;
}


@-moz-keyframes spin { 100% { -moz-transform: rotate(360deg);} }
@-webkit-keyframes spin { 100% { -webkit-transform: rotate(360deg);} }
@keyframes spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }