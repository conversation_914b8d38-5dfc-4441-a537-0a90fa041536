import request from '@/utils/request'

/**
 * @name 知识库目录
 * @returns 
 */
export const catalog = () => request({
  url: '/api/repository/catalog',
  method: 'GET',
})

/**
 * @name 新增目录
 * @param {*} param0.id id
 * @param {*} param0.pid 父id
 * @param {*} param0.category 目录编号
 * @param {*} param0.categoryName 目录名称
 * @returns 
 */
export const add = ({
  id,
  pid,
  category,
  categoryName
}) => request({
  url: '/api/repository/catalog',
  method: 'POST',
  data: {
    id,
    pid,
    category,
    categoryName
  }
})

/**
 * @name 修改目录
 * @param {*} param0.id id
 * @param {*} param0.pid 父id
 * @param {*} param0.category 目录编号
 * @param {*} param0.categoryName 目录名称
 * @returns 
 */
export const update = ({
  id,
  pid,
  category,
  categoryName
}) => request({
  url: '/api/repository/catalog',
  method: 'PUT',
  data: {
    id,
    pid,
    category,
    categoryName
  }
})

/**
 * @name 删除
 * @param {*} id 
 * @returns 
 */
export const deleleById = (id) => request({
  url: `/api/repository/catalog/${id}`,
  method: 'DELETE'
})

/**
 * @name 根据知识库code获取列表
 * @param {*} param0.category 对象所属配置目录（关联目录code）
 * @param {*} param0.objName 对象名称
 * @param {*} param0.objCode 对象编码
 * @param {*} param0.objType 业务对象类型
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @returns 
 */
export const kbowledgeList = ({
  category,
  objName,
  objCode,
  objType,
  pageSize,
  currentPage
}) => request({
  url: '/api/repository/obj/list',
  method: 'GET',
  data: {
    category,
    objName,
    objCode,
    objType,
    pageSize,
    currentPage
  }
})

/**
 * @name 对象类型字典
 * @returns 
 */
export const objTypeOptions = () => request({
  url: '/api/repository/obj/dict',
  method: 'GET'
})