import request from '@/utils/request'

/*根据ID获取详情*/
export function getDetail(id) {
  return request({
    method: 'get',
    url: `/apm/processRoute/${id}`,
  })
}
/*工艺路线启用、禁用*/
export function enable(id) {
  return request({
    method: 'get',
    url: `/apm/processRoute/enable/${id}`,
  })
}

/*获取列表*/
export function list(query) {
  return request({
    method: 'get',
    url: `/apm/processRoute/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function listPage(query) {
  return request({
    method: 'get',
    url: `/api/processRoute/pageList`,
    data: query,
  })
}
/*工艺路线删除*/
export function remove(id) {
  return request({
    method: 'get',
    url: `/apm/processRoute/remove/${id}`,
  })
}

// 工艺路线版本
export function getVersionByRouteCode(processRouteCode) {
  return request({
    method: 'get',
    url: `/api/processRoute/getListOfHistoricalVersions/${processRouteCode}`,
  })
}

/*新增*/
export function save(query) {
  return request({
    method: 'post',
    url: `/api/processRoute/add`,
    data: query,
  })
}
/*修改*/
export function update(query) {
  return request({
    method: 'put',
    url: `/api/processRoute/edit`,
    data: query,
  })
}

/*添加工艺路线嘻嘻*/
export function saveRouteOperation(query) {
  return request({
    method: 'post',
    url: `/api/processRouteOperation/saveRouteRelationship`,
    data: query,
  })
}
/*获取工艺路线信息*/
export function listTreeRouteOperation(processRouteId) {
  return request({
    method: 'get',
    url: `/api/processRouteOperation/getRouteRelationship/${processRouteId}`,
  })
}

/*
 * 工序-物料
 * */
/*根据id获取详情*/
export function getRouteOpDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationItem/${id}`,
  })
}
/*获取列表*/
export function opeartionList(query) {
  return request({
    method: 'get',
    url: `/apm/operationItem/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function opeartionListPage(query) {
  return request({
    method: 'get',
    url: `/api/processRouteOperationMaterial/getPageList`,
    data: query,
  })
}
/*删除*/
export function opeartionRemove(id) {
  return request({
    method: 'delete',
    url: `/api/processRouteOperationMaterial/batchDelete/${id}`,
  })
}
/*新增*/
export function opeartionSave(query) {
  return request({
    method: 'post',
    url: `/api/processRouteOperationMaterial/saveMaterial`,
    data: query,
  })
}
/*修改*/
export function opeartionUpdate(query) {
  return request({
    method: 'put',
    url: `/api/processRouteOperationMaterial/updateMaterial`,
    data: query,
  })
}
// 查询该工序下的物料列表
export function getMaterialListByRouteId(id) {
  return request({
    method: 'get',
    url: `/api/processRoute/getChildMaterialDetail/${id}`,
  })
}

/*
 * 工序-缺陷
 * */
/*根据id获取详情*/
export function getRouteOpBugDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationBug/${id}`,
  })
}
/*获取列表*/
export function opeartionBugList(query) {
  return request({
    method: 'get',
    url: `/apm/operationBug/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function opeartionBugListPage(query) {
  return request({
    method: 'get',
    url: `/apm/operationBug/listPage`,
    data: query,
  })
}
/*删除*/
export function opeartionBugRemove(id) {
  return request({
    method: 'get',
    url: `/apm/operationBug/remove/${id}`,
  })
}
/*新增*/
export function opeartionBugSave(query) {
  return request({
    method: 'post',
    url: `/apm/operationBug/save`,
    data: query,
  })
}
/*修改*/
export function opeartionBugUpdate(query) {
  return request({
    method: 'post',
    url: `/apm/operationBug/update`,
    data: query,
  })
}

/*
 * 工序-用例
 * */
/*根据id获取详情*/
export function getRouteOpInsDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationBug/${id}`,
  })
}
/*获取列表*/
export function opeartionInsList(query) {
  return request({
    method: 'get',
    url: `apm/baseAppt/listPage`,
    data: query,
  })
}
/*获取列表-分页*/
export function opeartionInsListPage(query) {
  return request({
    method: 'get',
    url: `/operation/operationAppt/page`,
    data: query,
  })
}
/*删除*/
export function opeartionInsRemove(id) {
  return request({
    method: 'delete',
    url: `/operation/operationAppt/${id}`,
  })
}
/*新增*/
export function opeartionInsSave(query) {
  return request({
    method: 'post',
    url: `/operation/operationAppt/add`,
    data: query,
  })
}
/*修改*/
export function opeartionInsUpdate(query) {
  return request({
    method: 'post',
    url: `/operation/operationAppt/update`,
    data: query,
  })
}




/*
 * 工序-检验项
 * */
/*根据id获取详情*/
export function getRouteOpCheckDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationCheck/${id}`,
  })
}
/*获取列表*/
export function operationCheckList(query) {
  return request({
    method: 'get',
    url: `/apm/operationCheck/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function operationCheckListPage(query) {
  return request({
    method: 'get',
    url: `/apm/operationCheck/listPage`,
    data: query,
  })
}
/*删除*/
export function operationCheckRemove(id) {
  return request({
    method: 'get',
    url: `/apm/operationCheck/remove/${id}`,
  })
}
/*新增*/
export function operationCheckSave(query) {
  return request({
    method: 'post',
    url: `/apm/operationCheck/save`,
    data: query,
  })
}
/*修改*/
export function operationCheckUpdate(query) {
  return request({
    method: 'post',
    url: `/apm/operationCheck/update`,
    data: query,
  })
}

/*
 * 工序-岗位
 * */
/*根据id获取详情*/
export function getRouteOpPostDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationPost/${id}`,
  })
}
/*获取列表*/
export function operationPostList(query) {
  return request({
    method: 'get',
    url: `/apm/operationPost/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function operationPostListPage(query) {
  return request({
    method: 'get',
    url: `/apm/operationPost/listPage`,
    data: query,
  })
}
/*删除*/
export function operationPostRemove(id) {
  return request({
    method: 'get',
    url: `/apm/operationPost/remove/${id}`,
  })
}
/*新增*/
export function operationPostSave(query) {
  return request({
    method: 'post',
    url: `/apm/operationPost/save`,
    data: query,
  })
}
/*修改*/
export function operationPostUpdate(query) {
  return request({
    method: 'post',
    url: `/apm/operationPost/update`,
    data: query,
  })
}
/*获取部门树*/
export function getDeptList(query) {
  return request({
    method: 'get',
    url: '/system/dept/allList',
    data: query,
  })
}
// 查询岗位列表
export function optionselect(query) {
  return request({
    url: '/system/post/list',
    method: 'get',
    data: query,
  })
}

/*
 * 工序-记录项
 * */
/*根据id获取详情*/
export function getRouteOpRecordDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationRecord/${id}`,
  })
}
/*获取列表*/
export function operationRecordList(query) {
  return request({
    method: 'get',
    url: `/apm/operationRecord/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function operationRecordListPage(query) {
  return request({
    method: 'get',
    url: `/apm/operationRecord/listPage`,
    data: query,
  })
}
/*删除*/
export function operationRecordRemove(id) {
  return request({
    method: 'get',
    url: `/apm/operationRecord/remove/${id}`,
  })
}
/*新增*/
export function operationRecordSave(query) {
  return request({
    method: 'post',
    url: `/apm/operationRecord/save`,
    data: query,
  })
}
/*修改*/
export function operationRecordUpdate(query) {
  return request({
    method: 'post',
    url: `/apm/operationRecord/update`,
    data: query,
  })
}

/*
 * 工序-工装
 * */
/*根据id获取详情*/
export function getRouteOpFrockDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationFrock/${id}`,
  })
}
/*获取列表*/
export function opeartionFrockList(query) {
  return request({
    method: 'get',
    url: `/apm/operationFrock/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function opeartionFrockListPage(query) {
  return request({
    method: 'get',
    url: `/apm/operationFrock/listPage`,
    data: query,
  })
}
/*删除*/
export function opeartionFrockRemove(id) {
  return request({
    method: 'get',
    url: `/apm/operationFrock/remove/${id}`,
  })
}
/*新增*/
export function opeartionFrockSave(query) {
  return request({
    method: 'post',
    url: `/apm/operationFrock/save`,
    data: query,
  })
}
/*修改*/
export function opeartionFrockUpdate(query) {
  return request({
    method: 'post',
    url: `/apm/operationFrock/update`,
    data: query,
  })
}
/*复制*/ //
export function saveProcessRouteCopy(processRouteId, prodtId, processRouteName) {
  return request({
    method: 'get',
    url: `/apm/processRoute/saveProcessRouteCopy/${processRouteId}/${prodtId}/${processRouteName}`,
  })
}

/*
 * 工序-文件
 * */
/*根据id获取详情*/
export function getRouteOpFileDetial(id) {
  return request({
    method: 'get',
    url: `/apm/operationFile/${id}`,
  })
}
/*获取列表*/
export function operationFileList(query) {
  return request({
    method: 'get',
    url: `/apm/operationFile/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function operationFileListPage(query) {
  return request({
    method: 'get',
    url: `/apm/operationFile/listPage`,
    data: query,
  })
}
/*删除*/
export function operationFileRemove(id) {
  return request({
    method: 'get',
    url: `/apm/operationFile/remove/${id}`,
  })
}
/*新增*/
export function operationFileSave(query) {
  return request({
    method: 'post',
    url: `/apm/operationFile/save`,
    data: query,
  })
}
/*修改*/
export function operationFileUpdate(query) {
  return request({
    method: 'post',
    url: `/apm/operationFile/update`,
    data: query,
  })
}
//获取绑定设备资源信息
export function querySource(query) {
  return request({
    method: 'get',
    url: `/api/processRouteOperationResource/getPageList`,
    data: query,
  })
}

//删除绑定设备资源信息
export function delSource(id) {
  return request({
    method: 'delete',
    url: `/api/processRouteOperationResource/batchDelete/${id}`,
  })
}

//新增绑定资源信息
export function saveSourceData(query) {
  return request({
    method: 'post',
    url: `/api/processRouteOperationResource/saveResource`,
    data: query,
  })
}

export function bindSource(query) {
  return request({
    method: 'post',
    url: `/apm/operationEquit/bindSource`,
    data: query,
  })
}
