<template>
  <div class="categorySelect-container">
    <div class="el-select" @click="openDialog">
      <el-input :placeholder="placeholder" v-model="innerValue" readonly :validate-event="false"
        @mouseenter.native="inputHovering = true" @mouseleave.native="inputHovering = false">
        <template slot="suffix">
          <i v-show="!showClose"
            :class="['el-select__caret', 'el-input__icon', 'el-icon-arrow-up']"></i>
          <i v-if="showClose" class="el-select__caret el-input__icon el-icon-circle-close"
            @click="clear"></i>
        </template>
      </el-input>
    </div>
    
    <el-dialog title="分类号选择" :close-on-click-modal="false" :visible.sync="visible"
      v-if="visible" class="JNPF-dialog JNPF-dialog_center" lock-scroll append-to-body
      width="800px">
      <el-row class="JNPF-common-search-box" :gutter="16">
        <el-form @submit.native.prevent>
          <el-col :span="10">
            <el-form-item label="分类号/名称">
              <el-input v-model="searchKeyword" placeholder="请输入分类号或名称" clearable
                @keyup.enter.native="search()" class="search-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="search()">
                查询
              </el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()">
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      
      <div class="category-table-container">
        <JNPF-table v-loading="listLoading" :data="filteredList" :border="false"
          @row-dblclick="handleRowDblClick" :hasNO="false" :hasC="false"
          :row-class-name="getRowClassName">
          <el-table-column prop="categoryCode" label="分类号" width="120" align="center">
            <template slot-scope="scope">
              <span class="category-code">{{ scope.row.categoryCode }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="categoryName" label="名称" show-overflow-tooltip>
            <template slot-scope="scope">
              <span :style="{ paddingLeft: (scope.row.level * 20) + 'px' }" class="category-name">
                <i v-if="!scope.row.isLeaf" class="el-icon-folder" style="margin-right: 5px; color: #E6A23C;"></i>
                <i v-else class="el-icon-document" style="margin-right: 5px; color: #909399;"></i>
                {{ scope.row.categoryName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="remark">{{ scope.row.remark || '-' }}</span>
            </template>
          </el-table-column>
        </JNPF-table>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="small">取消</el-button>
        <el-button type="primary" @click="confirm()" size="small">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import emitter from 'element-ui/src/mixins/emitter'
let { methods: { dispatch } } = emitter

export default {
  name: 'CategorySelect',
  props: {
    value: {
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择分类号'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    // 分类数据
    categoryData: {
      type: Array,
      default: () => []
    }
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  data() {
    return {
      innerValue: '',
      visible: false,
      inputHovering: false,
      listLoading: false,
      searchKeyword: '',
      selectedRow: null,
      // 模拟分类数据
      categoryList: [
        {
          id: '0',
          categoryCode: '0',
          categoryName: '文件',
          remark: '',
          children: [
            {
              id: '1',
              categoryCode: '1',
              categoryName: '成套设备',
              remark: '',
              children: [
                {
                  id: '1-1',
                  categoryCode: '1-1',
                  categoryName: '子设备1',
                  remark: '',
                  children: []
                },
                {
                  id: '1-2',
                  categoryCode: '1-2',
                  categoryName: '子设备2',
                  remark: '',
                  children: []
                }
              ]
            }
          ]
        },
        {
          id: '2',
          categoryCode: '2',
          categoryName: '整件',
          remark: '',
          children: []
        },
        {
          id: '3',
          categoryCode: '3',
          categoryName: '整件',
          remark: '',
          children: []
        },
        {
          id: '4',
          categoryCode: '4',
          categoryName: '整件',
          remark: '',
          children: []
        },
        {
          id: '5',
          categoryCode: '5',
          categoryName: '部件',
          remark: '',
          children: []
        },
        {
          id: '6',
          categoryCode: '6',
          categoryName: '部件',
          remark: '',
          children: []
        },
        {
          id: '7',
          categoryCode: '7',
          categoryName: '零件',
          remark: '',
          children: []
        },
        {
          id: '8',
          categoryCode: '8',
          categoryName: '零件',
          remark: '',
          children: []
        }
      ]
    }
  },
  computed: {
    showClose() {
      let hasValue = this.value !== undefined && this.value !== null && this.value !== '';
      let criteria = this.clearable &&
        !this.disabled &&
        this.inputHovering &&
        hasValue;
      return criteria;
    },
    // 扁平化的分类列表，用于表格显示
    flattenedList() {
      const result = []
      const flatten = (items, level = 0) => {
        items.forEach(item => {
          result.push({
            ...item,
            level,
            isLeaf: !item.children || item.children.length === 0
          })
          if (item.children && item.children.length > 0) {
            flatten(item.children, level + 1)
          }
        })
      }
      const dataSource = this.categoryData.length > 0 ? this.categoryData : this.categoryList
      flatten(dataSource)
      return result
    },
    // 根据搜索关键词过滤的列表
    filteredList() {
      if (!this.searchKeyword) {
        return this.flattenedList
      }
      return this.flattenedList.filter(item => 
        item.categoryCode.includes(this.searchKeyword) || 
        item.categoryName.includes(this.searchKeyword)
      )
    }
  },
  watch: {
    value(val) {
      this.setDefault()
    }
  },
  created() {
    this.setDefault()
  },
  methods: {
    openDialog() {
      if (this.disabled) return
      this.visible = true
      this.searchKeyword = ''
      this.selectedRow = null
    },
    clear(event) {
      this.innerValue = ''
      this.selectedRow = null
      this.$emit('input', '')
      this.$emit('change', '', null)
      dispatch.call(this, 'ElFormItem', 'el.form.change', '')
      event.stopPropagation()
    },
    search() {
      // 搜索逻辑已在computed中实现
    },
    reset() {
      this.searchKeyword = ''
    },
    // 双击行事件
    handleRowDblClick(row) {
      // 只有最后一层（叶子节点）才能双击选择
      if (row.isLeaf) {
        this.selectedRow = row
        this.confirm()
      } else {
        this.$message.warning('请选择最后一层分类')
      }
    },
    // 获取行样式类名
    getRowClassName({ row }) {
      let className = ''
      if (!row.isLeaf) {
        className += 'category-parent-row '
      } else {
        className += 'category-leaf-row '
      }
      if (row.level > 0) {
        className += `category-level-${row.level} `
      }
      return className.trim()
    },
    confirm() {
      if (!this.selectedRow) {
        this.$message.warning('请选择一个分类')
        return
      }
      
      this.innerValue = `${this.selectedRow.categoryCode} - ${this.selectedRow.categoryName}`
      this.$emit('input', this.selectedRow.categoryCode)
      this.$emit('change', this.selectedRow.categoryCode, this.selectedRow)
      dispatch.call(this, 'ElFormItem', 'el.form.change', this.selectedRow.categoryCode)
      this.visible = false
    },
    setDefault() {
      if (this.value) {
        const item = this.flattenedList.find(item => item.categoryCode === this.value)
        if (item) {
          this.innerValue = `${item.categoryCode} - ${item.categoryName}`
          this.selectedRow = item
        }
      } else {
        this.innerValue = ''
        this.selectedRow = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.categorySelect-container {
  width: 100%;
  
  .el-select {
    width: 100%;
    cursor: pointer;
  }
}

.category-table-container {
  height: 400px;
  overflow: auto;
}

>>> .el-dialog__body {
  height: 70vh;
  padding: 0 0 10px !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .JNPF-common-search-box {
    margin-bottom: 10px;
    flex-shrink: 0;
  }
  
  .category-table-container {
    flex: 1;
    overflow: auto;
  }
}
</style>
