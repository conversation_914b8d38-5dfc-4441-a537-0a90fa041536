import request from '@/utils/request'


// 工艺资源库 查询表格数据
export function processResourceList(data) {
  return request({
    url: `/api/ProductTechnologyResourcesController/queryByLike`,
    method: 'POST',
    data
  })
}

// 工艺资源库 新增
export function addProcessResource(data) {
  return request({
    url: `/api/ProductTechnologyResourcesController/add`,
    method: 'POST',
    data
  })
}


// 工艺资源库 删除表格中的数据
export function deleteProcessResource(id) {
  return request({
    url: `/api/ProductTechnologyResourcesController/${id}`,
    method: 'DELETE',
  })
}

// 工艺资源库 查询
export function queryProcessResource(id) {
  return request({
    url: `/api/ProductTechnologyResourcesController/detail/${id}`,
    method: 'GET',
  })
}


// 工艺资源库 修改
export function updateProcessResource(data) {
  return request({
    url: `/api/ProductTechnologyResourcesController/update`,
    method: 'POST',
    data
  })
}

