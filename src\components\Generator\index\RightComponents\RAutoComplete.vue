<template>
  <el-row>
    <el-form-item label="控件标题">
      <el-input v-model="activeData.__config__.label" placeholder="请输入控件标题" />
    </el-form-item>
    <el-form-item label="显示标题">
      <el-switch v-model="activeData.__config__.showLabel" />
    </el-form-item>
    <el-form-item label="标题提示">
      <el-input v-model="activeData.__config__.tipLabel" placeholder="请输入标题提示" />
    </el-form-item>
    <el-form-item label="占位提示">
      <el-input v-model="activeData.placeholder" placeholder="请输入占位提示" />
    </el-form-item>
    <el-form-item label="远端数据">
      <interface-dialog :value="activeData.interfaceId" :title="activeData.interfaceName"
        popupTitle="远端数据" @change="onInterfaceIdChange" />
    </el-form-item>
    <el-form-item label="显示字段">
      <el-autocomplete class="inline-input" v-model="activeData.relationField"
        :fetch-suggestions="querySearch" placeholder="请输入内容" style="width:100%"
        @select="handleSelect($event,'relationField')">
        <template slot-scope="{ item }">
          <div class="sale-order-popper-item">
            <span>{{ item.defaultValue}}</span>
          </div>
        </template>
      </el-autocomplete>
    </el-form-item>
    <el-form-item label="参数设置" v-if="activeData.templateJson.length">
      <SelectInterfaceBtn :templateJson="activeData.templateJson" :fieldOptions="formFieldsOptions"
        :type="3" @fieldChange="onRelationFieldChange" />
    </el-form-item>
    <jnpf-form-tip-item label="显示条数" tipLabel="最大值只能设置为50">
      <el-input-number v-model="activeData.total" placeholder="请输入显示条数" :min="1" :max="50"
        :precision="activeData.precision" controls-position="right" />
    </jnpf-form-tip-item>
    <el-form-item label="能否清空">
      <el-switch v-model="activeData.clearable" />
    </el-form-item>
    <el-form-item label="是否禁用">
      <el-switch v-model="activeData.disabled" />
    </el-form-item>
    <el-form-item label="是否隐藏">
      <el-switch v-model="activeData.__config__.noShow" />
    </el-form-item>
    <el-form-item label="是否必填">
      <el-switch v-model="activeData.__config__.required" />
    </el-form-item>
  </el-row>
</template>
<script>
import comMixin from './mixin';
import handelFlidMixin from './handelFlidMixin';
import draggable from 'vuedraggable'
import InterfaceDialog from '@/components/Process/PropPanel/InterfaceDialog'
import { noAllowRelationList } from '@/components/Generator/generator/comConfig'
import { getDrawingList } from '@/components/Generator/utils/db'
import SelectInterfaceBtn from '@/components/SelectInterfaceBtn'
export default {
  props: ['activeData'],
  mixins: [comMixin, handelFlidMixin],
  components: { draggable, InterfaceDialog, SelectInterfaceBtn },
  data() {
    return {
    }
  },
  computed: {
    formFieldsOptions() {
      let list = []
      list.push({ realVModel: '@keyword', realLabel: '@keyword', jnpfKey: '' })
      const loop = (data, parent) => {
        if (!data) return
        if (data.__config__ && this.isIncludesTable(data) && data.__config__.children && Array.isArray(data.__config__.children)) {
          loop(data.__config__.children, data)
        }
        if (Array.isArray(data)) data.forEach(d => loop(d, parent))
        if (data.__vModel__ && !noAllowRelationList.includes(data.__config__.jnpfKey) && data.__vModel__ !== this.activeData.__vModel__) {
          const isTableChild = parent && parent.__config__ && parent.__config__.jnpfKey === 'table'
          list.push({
            realVModel: isTableChild ? parent.__vModel__ + '-' + data.__vModel__ : data.__vModel__,
            realLabel: isTableChild ? parent.__config__.label + '-' + data.__config__.label : data.__config__.label,
            ...data
          })
        }
      }
      loop(getDrawingList())
      return list
    }
  },
  mounted() {
    this.getDataInterfaceInfo()
  },
  methods: {
    isIncludesTable(data) {
      if ((!data.__config__.layout || data.__config__.layout === 'rowFormItem') && data.__config__.jnpfKey !== 'table') return true
      if (this.activeData.__config__.isSubTable) return this.activeData.__config__.parentVModel === data.__vModel__
      return data.__config__.jnpfKey !== 'table'
    },
    onRelationFieldChange(val, row) {
      if (!val) return row.jnpfKey = ''
      if (val === '@keyword') return row.jnpfKey = ''
      let list = this.formFieldsOptions.filter(o => o.realVModel === val)
      if (!list.length) return row.jnpfKey = ''
      let item = list[0]
      row.jnpfKey = item.__config__.jnpfKey
    },
  }
}
</script>
<style lang="scss" scoped>
.auto-table {
  margin-bottom: 20px;
}
</style>