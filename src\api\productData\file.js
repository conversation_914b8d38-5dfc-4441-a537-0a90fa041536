import request from '@/utils/request'

// 获取文档目录
export function getFileCatalogue() {
  return request({
    url: `/api/system/serviceConfiguration/type/select/tree/jnpf.product.entity.document.ProductDocumentLibraryFile`,
    method: 'GET',
  })
}
// 文档文件查询
export function queryFileId(id) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/detail/${id}`,
    method: 'GET',
  })
}

// 文档管理分页查询
export function queryFileList(params) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/queryByLike`,
    method: 'GET',
    data: params
  })
}

// 文档管理 新增文档
export function addFileList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/add`,
    method: 'POST',
    data
  })
}

// 文档管理 新增文档
export function editFileList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/update`,
    method: 'POST',
    data
  })
}


// 文档管理 查看单条数据详情
export function fileDetails(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/detail`,
    method: 'GET',
    data
  })
}

// 文档管理 删除单条数据
export function deleteFileList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/delete`,
    method: 'POST',
    data
  })
}


// 文档管理 限制类型
export function addLimitType(data) {
  return request({
    url: `/api/ProductDocumentSuffixRelationController/add`,
    method: 'POST',
    data
  })
}

// 文档管理 根据 typeCode 获取类型数据
export function limitTypeList(data) {
  return request({
    url: `/api/ProductDocumentSuffixRelationController/queryByTypeCode?extendCode=${data.extendCode}&categoryPadCharacter=${data.categoryPadCharacter}`,
    method: 'GET',

  })
}

// 文档管理新增和编辑 根据 ypeCode 获取类型数据
export function getLimitTypeList(data) {
  return request({
    url: `/api/ProductDocumentSuffixRelationController/queryDataByTypeCode?typeCode=${data.typeCode}`,
    method: 'GET',

  })
}