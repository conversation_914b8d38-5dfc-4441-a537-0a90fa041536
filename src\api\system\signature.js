import request from '@/utils/request'

// 获取签章列表(带分页)
export function getSignatureList(data) {
  return request({
    url: '/api/system/Signature',
    method: 'get',
    data
  })
}
// 获取签章下拉框列表
export function getSignatureSelector() {
  return request({
    url: '/api/system/Signature/Selector',
    method: 'get'
  })
}

// 新建签章
export function create(data) {
  return request({
    url: `/api/system/Signature`,
    method: 'post',
    data
  })
}
// 修改签章
export function update(data) {
  return request({
    url: `/api/system/Signature/${data.id}`,
    method: 'put',
    data
  })
}
// 获取签章
export function getInfo(id) {
  return request({
    url: `/api/system/Signature/${id}`,
    method: 'get'
  })
}
// 删除签章
export function delSignature(id) {
  return request({
    url: `/api/system/Signature/${id}`,
    method: 'delete'
  })
}
// 通过id获取签章下拉框列表
export function getListByIds(data) {
  return request({
    url: `/api/system/Signature/ListByIds`,
    method: 'POST',
    data
  })
}
