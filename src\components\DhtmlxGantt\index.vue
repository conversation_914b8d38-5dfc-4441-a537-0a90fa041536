<template>
  <div class="dhtmlxGantt">
    <div class="gantt-header">
      <div class="grid-tips">
        <span class="tip-wrap add-tip" v-if="showChangeTip"><span>变更单新增</span></span>
        <span class="tip-wrap edit-tip" v-if="showChangeTip"><span>变更单编辑</span></span>
        <span class="tip-wrap stop-tip" v-if="showChangeTip"><span>终止任务</span></span>
      </div>
      <div class="link-tips">
        <div title="目标任务直到源任务启动后才能启动" class="circle-with-lines task-ss">
          <span class="label">ss</span>
        </div>
        <div title="目标任务不能在源任务结束之前启动" class="circle-with-lines task-fs">
          <span class="label">fs</span>
        </div>
        <div title="目标任务不能在源任务结束之前结束" class="circle-with-lines task-ff">
          <span class="label">ff</span>
        </div>
        <div title="目标任务不能在源任务开始之前结束" class="circle-with-lines task-sf">
          <span class="label">sf</span>
        </div>
      </div>
      <div class="date-change">
        <el-radio-group v-model="timeState" @change="changeTime">
          <el-radio-button v-for="(time, t_index) in timeList" :key="t_index" :label="time.type" size="default" border>{{ time.name }}</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div id="gantt-container" ref="gantt" class="gantt-container"></div>
  </div>
</template>
<script>
import { gantt } from "dhtmlx-gantt"
// import Vue from 'vue'
// import TaskHeader from './taskHeader.vue'
const zoomConfig = {
  levels: [
    {
      name: "day",
      scale_height: 60,
      min_column_width: 30,
      scales: [{ unit: "month", step: 1, format: "%Y-%m" }, { unit: "day", format: "%d" }]
    },
    {
      name: "month",
      scale_height: 60,
      scales: [{ unit: "year", step: 1, format: "%Y年" }, { unit: "month", format: "%m月" }]
    },
    {
      name: "year",
      scale_height: 50,
      min_column_width: 150,
      scales: [{ unit: "year", step: 1, format: "%Y年" }]
    }
  ]
}

export default {
  name: "dhtmlxGantt",
  props: {
    ganttColumnMap: {
      //任务字段映射对象
      type: Object,
      default() {
        return {}
      }
    },
    ganttTasks: {
      type: Array,
      default() {
        return []
      }
    },
    taskLeaderDict: {
      type: Array,
      default() {
        return []
      }
    },
    links: {
      type: Array,
      default() {
        return []
      }
    },
    showChangeTip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timeList: [{ name: "日", type: "day" }, { name: "月", type: "month" }, { name: "年", type: "year" }],
      timeState: "month",
      isInitialLoad: true,
      taskLeaderList: [],
      curSelectTaskId: null,
      curSelectTask: null,

      //鼠标移动事件节流
      target: null,
      debounceTimer: null, //防抖计时器
      debounceDelay: 300 //防抖延迟时间
    }
  },
  mounted() {},
  watch: {
    ganttTasks: {
      handler(newV, oldV) {
        this.initGantt()
        this.reload()
      },
      deep: true
    }
  },
  methods: {
    initGantt() {
      const that = this
      gantt.i18n.setLocale("cn") //汉化
      gantt.config.autofit = true
      gantt.config.drag_mode = "resize"
      gantt.config.drag_move = false //禁止拖拽
      gantt.config.drag_links = false //禁止连接线拖拽
      gantt.config.drag_progress = false //禁止进度拖拽
      gantt.config.drag_resize = false //禁止拖拽调整大小
      gantt.config.show_grid = true //显示网格
      gantt.config.bar_height = 20 //任务高度
      gantt.config.xml_date = "%Y-%m-%d" //日期格式化
      gantt.config.fit_tasks = true
      gantt.config.show_tasks_outside_timescale = true //超出时间范围的任务
      gantt.config.show_task_cells = true //显示任务单元格
      gantt.config.open_tree_initially = true //展开tree
      gantt.config.buttons_right = ["gantt_save_btn", "gantt_cancel_btn"] //添加保存取消按钮
      gantt.config.buttons_left = []
      gantt.config.columns = [
        {
          name: "start_date",
          label: "计划开始时间",
          width: 90,
          align: "center",
          template: function(obj) {
            return obj.planStartTime ? obj.start_date : ""
          }
        },
        {
          name: "end_date",
          label: "计划结束时间",
          width: 90,
          align: "center",
          template: function(obj) {
            return obj.planEndTime ? obj.end_date : ""
          }
        },
        {
          name: "progress",
          label: "完成度",
          width: 50,
          align: "center",
          template: function(obj) {
            return obj.progress != 0 ? (obj.progress * 100).toFixed(2) + "%" : 0
          }
        },
        { name: "actualStartTime", label: "实际开始时间", width: 90, align: "center" },
        { name: "actualEndTime", label: "实际完成时间", width: 90, align: "center" },
        { name: "taskTypeName", label: "任务类型", width: 70, align: "center" },
        { name: "taskStatusName", label: "任务状态", width: 70, align: "center" },
        { name: "taskPriorityName", label: "优先级", width: "*", align: "center" },
        { name: "manHour", label: "核定工时(H)", width: 70, align: "center" },
        {
          name: "userId",
          label: "负责人",
          width: 100,
          align: "center",
          template: function(item) {
            return item.taskLeader
          }
        }
      ]
      const fixedColumn = {
        columns: [{ name: "text", label: "任务名称", tree: true, width: 180, align: "left", resize: true }]
      }
      gantt.config.layout = {
        css: "gantt_container",
        cols: [
          {
            width: 1000,
            min_width: 280,
            rows: [
              {
                group: "gantt",
                cols: [
                  {
                    width: 200,
                    rows: [{ view: "grid", config: fixedColumn, bind: "task", scrollY: "scrollVer", scrollX: false }]
                  },
                  {
                    rows: [{ view: "grid", bind: "task", scrollX: "gridScrollX", scrollable: true, scrollY: "scrollVer" }, { view: "scrollbar", id: "gridScrollX" }]
                  }
                ]
              }
              // {
              // 	view: "grid",
              // 	scrollX: "gridScroll",
              // 	scrollable: true,
              // 	scrollY: "scrollVer"
              // },
              // {
              // 	view: "scrollbar",
              // 	id: "gridScrollY"
              // 	// id: "gridScroll",
              // 	// group: "horizontal"
              // }
            ]
          },
          {
            resizer: true,
            width: 1
          },
          {
            rows: [
              {
                group: "gantt",
                cols: [
                  {
                    rows: [{ view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer" }, { view: "scrollbar", id: "scrollHor" }]
                  },
                  { view: "scrollbar", id: "scrollVer", css: "right_scroll" }
                ]
              }
            ]
          }
          // {
          // 	rows: [{
          // 		view: "timeline",
          // 		scrollX: "scrollHor",
          // 		scrollY: "scrollVer"
          // 	},
          // 	{
          // 		view: "scrollbar",
          // 		id: "scrollHor",
          // 		group: "horizontal"
          // 	}
          // 	]
          // },
          // {
          // 	view: "scrollbar",
          // 	id: "scrollVer"
          // }
        ]
      }
      gantt.locale.labels.section_text = "任务名称"
      gantt.locale.labels.section_manHour = "核定工时(H)"
      gantt.locale.labels.section_userId = "负责人"
      gantt.config.lightbox.sections = [
        { name: "text", height: 50, map_to: "text", type: "textarea" },
        { name: "manHour", height: 38, map_to: "manHour", type: "textarea" },
        { name: "time", height: 38, map_to: "auto", type: "duration" },
        {
          name: "userId",
          height: 50,
          type: "select",
          map_to: "userId",
          options: that.taskLeaderDict
        }
      ]
      const style = document.createElement("style")
      style.innerHTML = `
			.timeline_cell .gantt_layout_content .gantt_data_area{
				padding-bottom: 20px;
			}
			.gantt_cal_light .gantt_cal_lsection, .gantt_cal_light input{
				font-size: 14px;
			}
			.gantt_cal_light .gantt_duration{
				font-size: 14px;
				line-height: 24px;
			}	
			.gantt_cal_light .gantt_cal_lsection,.gantt_cal_light .gantt_cal_ltext >*{
				font-size: 14px;
			}
			.gantt_cal_light .gantt_cal_ltext select {
				padding: 6px 4px;
				font-size: 14px;
			}
			.gantt_cal_light .gantt_cal_ltext textarea{
				padding: 4px;
			}
			.gantt_cal_light .gantt_section_time .gantt_time_selects select{
				padding: 2px 4px;
				font-size: 14px;
			}
			`
      document.head.appendChild(style)

      gantt.plugins({
        tooltip: true
      })
      gantt.templates.tooltip_text = function(start, end, task) {
        let tooltip = document.querySelector(".gantt_tooltip")

        tooltip && (tooltip.style.display = "block")
        return "<b>" + task.text + "</b>"
      }
      gantt.templates.grid_row_class = function(start_date, end_date, item) {
        if (item.colorFlag == "new") return "taskRow-new"
        if (item.colorFlag == "modify") return "taskRow-modify"
        if (item.colorFlag == "stop") return "taskRow-stop"
      }
      // 复选框
      // gantt.templates.grid_blank=function(task){
      // 	return "<input id='ch1' type='checkbox' onClick='someFunc()'></input>"
      // };
      gantt.init(that.$refs.gantt)
      gantt.templates.link_class = function(link) {
        var types = gantt.config.links
        switch (link.type) {
          case types.finish_to_start:
            return "fs"
          case types.start_to_start:
            return "ss"
          case types.finish_to_finish:
            return "ff"
          case types.start_to_finish:
            return "sf"
        }
      }
      // let vueInstance = null;
      // gantt.form_blocks["my_editor"] = {
      // 	render: function (sns) {
      // 		return "<div style='min-height:50px' id='vue-component-container'></div>";
      // 	},
      // 	set_value: (node, value, task) => {
      // 		if (!vueInstance) {
      // 			vueInstance = new Vue({
      // 				el: node,
      // 				render(h) {
      // 					return h(TaskHeader, {
      // 						props: {
      // 							task: this.taskData
      // 						},
      // 						on: {
      // 							'update-task-prop': (newVal) => {
      // 								this.taskData.label = newVal.fullName || ''
      // 								this.taskData.value = newVal.id || ''
      // 							}
      // 						}
      // 					})
      // 				},
      // 				data: {
      // 					taskData: { label: task.taskLeader, value: task.userId }
      // 				}
      // 			})
      // 		} else {
      // 			vueInstance.taskData = { label: task.taskLeader, value: task.userId }
      // 		}
      // 	},
      // 	get_value: function (node, task) {
      // 		return vueInstance.taskData
      // 	},
      // 	focus: function (node) {
      // 	},
      // }
      gantt.ext.zoom.init(zoomConfig) //配置初始化扩展
      gantt.ext.zoom.setLevel("month") //切换到指定的缩放级别
      gantt.attachEvent("onGanttRender", function() {
        const rightScroll = document.querySelector(".right_scroll")
        if (rightScroll) {
          const scrollEle = rightScroll.querySelector(".gantt_ver_scroll")
          if (scrollEle) {
            const calcHeight = rightScroll.offsetHeight - scrollEle.offsetHeight
            calcHeight === 0 && (scrollEle.style.height = `${scrollEle.offsetHeight - 20}px`)
          }
        }
      })
      gantt.attachEvent("onTaskDblClick", function(id, e) {
        return false
        // const task = gantt.getTask(id)
        // let flag = false
        // if (Array.isArray(task.handlerFlagDTOList)) {
        // 	flag = task.handlerFlagDTOList.findIndex(o => o.handleFlag == '1' && o.showFlag) > -1
        // }
        // return flag
      })
      // 双击弹窗
      // gantt.attachEvent("onLightboxSave", (id, task, is_new) => {
      // 	// const newTaskData = { ...task, taskLeader: task.taskLeader.label, userId: task.taskLeader.value }
      // 	that.$emit('onLightboxSave', id, task, function callback() {
      // 		gantt.updateTask(id, task);
      // 		gantt.hideLightbox();
      // 	})
      // })
      document.getElementById("gantt-container").addEventListener("contextmenu", (e) => {
        let taskElement = e.target.closest(".gantt_row_task")
        if (taskElement && !e.target.closest(".gantt_tree_icon")) {
          e.preventDefault()
          let taskId = taskElement.getAttribute("task_id")
          if (taskId) {
            // 获取任务数据
            let task = gantt.getTask(taskId)
            // 显示自定义右键菜单
            that.showCustomContextMenu(e.pageX, e.pageY, task)
            const selectedTasks = document.querySelectorAll(".gantt_selected")
            selectedTasks.forEach((task) => {
              task.classList.remove("gantt_selected")
            })
          }
        }
      })
      document.getElementById("gantt-container").addEventListener("click", this.ganttClick)
      gantt.attachEvent("onLinkDblClick", function(id, e) {
        return false
      })

      // 隐藏自定义右键菜单
      document.addEventListener("click", this.hidenMenu)

      const ganttGrid = document.querySelector(".gantt-container .gantt_grid_data")
      ganttGrid.addEventListener("mouseleave", function() {
        setTimeout(() => {
          let tooltip = document.querySelector(".gantt_tooltip")
          tooltip && (tooltip.style.display = "none")
        }, 300)
      })

      this.target = document.querySelector("#gantt-container")
      document.addEventListener("mousemove", this.leaveDom)
    },

    showCustomContextMenu(x, y, task) {
      let menu = document.getElementById("custom-context-menu")
      if (!menu) {
        menu = document.createElement("div")
        menu.id = "custom-context-menu"
        menu.style.position = "absolute"
        menu.style.zIndex = 9999
        menu.style.backgroundColor = "#fff"
        menu.style.border = "1px solid #ccc"
        menu.style.boxShadow = "0px 0px 5px rgba(0,0,0,0.2)"
        menu.style.display = "none"
        document.body.appendChild(menu)
        menu.addEventListener("mousemove", (e) => {
          //销毁tootip
          let tooltip = document.querySelector(".gantt_tooltip")
          tooltip && (tooltip.style.display = "none")
        })
      }
      let div = '<div class="rc-options" style="min-width:120px">'
      if (Array.isArray(task.handlerFlagDTOList)) {
        for (let optionMark of task.handlerFlagDTOList) {
          if (optionMark.showFlag) {
            div += `<button data-type="${optionMark.handleFlag}" class="db-task-item">${optionMark.fieldName}</button>`
          } else {
            div += `<button disabled data-type="${optionMark.handleFlag}" class="db-task-item">${optionMark.fieldName}</button>`
          }
        }
      }
      div += `</div>`
      menu.innerHTML = div

      //显示菜单
      menu.style.display = "block"
      // 下一次dom更新之后再获取节点并计算弹窗位置
      this.$nextTick(() => {
        menu = document.getElementById("custom-context-menu")
        const viewportHeight = window.innerHeight //窗口高度
        const height = menu.offsetHeight //弹窗高度
        // 检查是否会超出屏幕底部，并调整位置
        if (y + height > viewportHeight) {
          y = viewportHeight - height // 调整到屏幕底部上方
        }

        // 设置菜单位置
        menu.style.left = `${x}px`
        menu.style.top = `${y}px`
      })
      let taskItems = document.getElementsByClassName("db-task-item")
      for (let item of taskItems) {
        //菜单点击事件
        item.addEventListener("click", (e) => {
          let tooltip = document.querySelector(".gantt_tooltip")
          tooltip && (tooltip.style.display = "none")
          this.$emit("contextMenuClick", task, e.target.dataset.type)
        })
      }
    },
    reload() {
      gantt.clearAll()
      const tasks = this.ganttTasks.map((item) => {
        for (const key in this.ganttColumnMap) {
          // item[key] = item[this.ganttColumnMap[key]]

          if (key === "start_date" || key === "end_date") {
            if (item[this.ganttColumnMap[key]]) {
              item[key] = item[this.ganttColumnMap[key]] // 或者设置一个默认日期
            } else {
              item[key] = ""
            }
          } else {
            item[key] = item[this.ganttColumnMap[key]]
          }
        }
        return item
      })

      // 数据解析
      gantt.parse({ data: tasks, links: this.links })
    },
    changeTime() {
      gantt.ext.zoom.setLevel(this.timeState)
    },
    ganttClick(e) {
      let taskElement = e.target.closest(".gantt_row_task")
      var selectedTaskId = gantt.getSelectedId() // 获取当前选中的任务ID
      if (!taskElement || (selectedTaskId && selectedTaskId == this.curSelectTaskId)) {
        gantt.unselectTask(selectedTaskId) // 取消选中任务
        this.curSelectTaskId = null
        this.curSelectTask = null
      } else {
        this.curSelectTaskId = selectedTaskId
        this.curSelectTask = selectedTaskId ? gantt.getTask(selectedTaskId) : null
      }
    },
    hidenMenu(e) {
      let menu = document.getElementById("custom-context-menu")
      if (menu) {
        menu.style.display = "none"
      }
    },
    removeGanttClick() {
      document.getElementById("gantt-container").removeEventListener("click", this.ganttClick)
      this.curSelectTaskId = null
      this.curSelectTask = null
    },
    leaveDom(e) {
      // 清除之前的计时器
      if (this.debounceTimer) clearTimeout(this.debounceTimer)
      // 设置新的计时器，延迟执行检测逻辑
      this.debounceTimer = setTimeout(() => {
        const rect = this.target.getBoundingClientRect()
        const isInside = e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom

        //离开元素（防抖后）
        if (!isInside) {
          let tooltip = document.querySelector(".gantt_tooltip")
          tooltip && (tooltip.style.display = "none")
        }
      }, this.debounceDelay)
    }
  },
  beforeDestroy() {
    //销毁tootip
    let tooltip = document.querySelector(".gantt_tooltip")
    tooltip && (tooltip.style.display = "none")
    this.hidenMenu()
    //解除全局鼠标键点击事件
    document.removeEventListener("click", this.hidenMenu)
    this.removeGanttClick()
    this.removeEventListener("mousemove", this.leaveDom)
    this.debounceTimer = null
  }
}
</script>
<style lang="scss" scoped>
@import "~dhtmlx-gantt/codebase/dhtmlxgantt.css";
.dhtmlxGantt {
  height: 100%;
  display: flex;
  flex-direction: column;

  .gantt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .grid-tips {
      min-width: 140px;
      flex: 1;

      .tip-wrap {
        font-size: 13px;

        &::before {
          content: "";
          display: inline-block;
          vertical-align: middle;
          width: 26px;
          height: 14px;
          margin-top: -2px;
          margin-right: 4px;
          border-radius: 4px;
        }
      }

      span {
        display: inline-block;
        vertical-align: middle;
      }

      .add-tip {
        margin-right: 20px;

        &::before {
          background-color: #00d96d;
        }
      }

      .edit-tip {
        margin-right: 20px;

        &::before {
          background-color: #1890ff;
        }
      }

      .stop-tip {
        &::before {
          background-color: #ccc;
        }
      }
    }

    .link-tips {
      min-width: 280px;
      margin-right: 100px;

      .circle-with-lines {
        margin-left: 100px;
        display: inline-block;
        position: relative;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        border: 2px solid;

        .label {
          color: #606266;
          margin-left: 40px;
        }

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 16px;
          transform: translateY(-50%);
          width: 20px;
          height: 2px;
        }

        &::after {
          content: "";
          position: absolute;
          top: 50%;
          right: 16px;
          transform: translateY(-50%);
          width: 20px;
          height: 2px;
        }
      }

      .circle-with-lines.task-ss {
        border-color: #dd5640;

        &::before,
        &::after {
          background-color: #dd5640;
        }
      }

      .circle-with-lines.task-fs {
        border-color: #000;

        &::before,
        &::after {
          background-color: #000;
        }
      }

      .circle-with-lines.task-ff {
        border-color: #55d822;

        &::before,
        &::after {
          background-color: #55d822;
        }
      }

      .circle-with-lines.task-sf {
        border-color: #0055e2;

        &::before,
        &::after {
          background-color: #0055e2;
        }
      }
    }

    .date-change {
      text-align: right;
    }
  }

  .gantt-container {
    flex: 1;

    ::v-deep .gantt_grid {
      // .gantt_grid_scale {
      // 	.gantt_grid_head_cell[data-column-index="0"] {
      // 		position: sticky;
      // 		left: 0;
      // 		background: #fff;
      // 		z-index: 100;
      // 	}
      // }

      // .gantt_grid_data {

      // overflow: initial;
      // 	.gantt_row {
      // 		.gantt_cell[data-column-index="0"] {
      // 			position: sticky;
      // 			left: 0;
      // 			background: #fff;
      // 			z-index: 100;
      // 		}
      // 	}
      // }
    }

    ::v-deep .gantt_task {
      .gantt_task_line {
        background-color: #ed8c76;
        border: 1px solid #ed8c76;

        .gantt_task_progress {
          background: #ec6a64;
        }
      }
    }

    ::v-deep .gantt_task_link.ss {
      .gantt_line_wrapper div {
        background-color: #dd5640;
      }

      &:hover {
        .gantt_line_wrapper div {
          box-shadow: 0 0 5px 0px #dd5640;
        }
      }

      .gantt_link_arrow_right {
        border-left-color: #dd5640;
      }
    }

    ::v-deep .gantt_task_link.fs {
      .gantt_line_wrapper div {
        background-color: #000;
      }

      &:hover {
        .gantt_line_wrapper div {
          box-shadow: 0 0 5px 0px #000;
        }
      }

      .gantt_link_arrow_right {
        border-left-color: #000;
      }
    }

    ::v-deep .gantt_task_link.ff {
      .gantt_line_wrapper div {
        background-color: #55d822;
      }

      &:hover {
        .gantt_line_wrapper div {
          box-shadow: 0 0 5px 0px #55d822;
        }
      }

      .gantt_link_arrow_left {
        border-right-color: #55d822;
      }
    }

    ::v-deep .gantt_task_link.sf {
      .gantt_line_wrapper div {
        background-color: #0055e2;
      }

      &:hover {
        .gantt_line_wrapper div {
          box-shadow: 0 0 5px 0px #0055e2;
        }
      }

      .gantt_link_arrow_left {
        border-right-color: #0055e2;
      }
    }
  }
}
</style>
<style>
.gantt_tooltip {
  display: none;
  z-index: 999;
}

.taskRow-new .gantt_cell,
.odd.taskRow-new .gantt_cell {
  background-color: #00d96d;
  color: #fff;
}

.taskRow-modify .gantt_cell,
.odd.taskRow-modify .gantt_cell {
  background-color: #1890ff;
  color: #fff;
}

.taskRow-stop .gantt_cell,
.odd.taskRow-stop .gantt_cell {
  background-color: #ccc;
  color: #fff;
}

.gantt_cal_light .gantt_cal_ltitle {
  font-size: 13px;
}

#custom-context-menu .rc-options button.db-task-item {
  display: block;
  width: 100%;
  padding: 0 10px;
  line-height: 32px;
  font-size: 14px;
  margin-bottom: 1px;
  border: none;
  cursor: pointer;
}

#custom-context-menu .rc-options button.db-task-item:disabled {
  cursor: not-allowed;
}

#custom-context-menu .rc-options button.db-task-item:not(:disabled):hover {
  background-color: #f4f6f9;
  font-weight: bold;
  color: #0055e2;
}
</style>
