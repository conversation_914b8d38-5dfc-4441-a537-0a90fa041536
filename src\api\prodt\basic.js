import request from '@/utils/request'

/**
 * 产品基础信息
 * @returns {*}
 */

//根据id获取详情
export function getDetail(id) {
  return request({
    method: 'get',
    url: `/apm/prodt/${id}`,
  })
}
//获取列表
export function list(query) {
  return request({
    method: 'get',
    url: `/apm/prodt/list`,
    params: query,
  })
}
//获取列表-分页
export function listPage(query) {
  return request({
    method: 'post',
    url: 'api/ProductMaterialLibraryDataHisController/queryByLike',
    // url: `/apm/prodt/listPage`,
    data: query,
  })
}
//根据id删除
export function remove(id) {
  return request({
    method: 'get',
    url: `/apm/prodt/remove/${id}`,
  })
}
//新增
export function save(query) {
  return request({
    method: 'post',
    url: `/apm/prodt/save`,
    data: query,
  })
}
//编辑
export function update(query) {
  return request({
    method: 'post',
    url: `/apm/prodt/update`,
    data: query,
  })
}
//导入模板下载
export function download() {
  return request({
    method: 'get',
    url: `/apm/prodt/downloadTemplate`,
    responseType: 'blob',
  })
}
//导入物料Bom信息
export function importProdt(query) {
  return request({
    method: 'post',
    url: `/apm/prodt/importProdt`,
    data: query,
  })
}
/*
 * BOM
 * */

//导入模板下载
export function downloadTemplate() {
  return request({
    method: 'get',
    url: `/apm/prodtBom/downloadTemplate`,
    responseType: 'blob',
  })
}
//导入物料Bom信息
export function importProdtBom(query) {
  return request({
    method: 'post',
    url: `/apm/prodtBom/importProdtBom`,
    data: query,
  })
}
//根据产品编码和版本查询BOM信息
export function bomList(version, prodtCode) {
  return request({
    method: 'get',
    url: `/apm/prodtBom/list/${version}/${prodtCode}`,
  })
}
//查询BOM的版本信息
export function listProdtBomVersion(prodtCode) {
  return request({
    method: 'get',
    url: `/apm/prodtBom/listProdtBomVersion/${prodtCode}`,
  })
}

//BOM新增
export function saveBom(data) {
  return request({
    method: 'post',
    url: `/apm/prodtBom/save`,
    data: data,
  })
}

//线缆图纸上传
export function importImg(data) {
  return request({
    method: 'post',
    url: `/apm/prodtEsopCable/save`,
    data: data,
  })
}
//线缆图纸修改
export function updateImg(data) {
  return request({
    method: 'post',
    url: `/apm/prodtEsopCable/update`,
    data: data,
  })
}
//获取列表-分页
export function listPageImg(query) {
  return request({
    method: 'get',
    url: `/apm/prodtEsopCable/listPage`,
    params: query,
  })
}
//根据id删除
export function removeImg(id) {
  return request({
    method: 'get',
    url: `/apm/prodtEsopCable/remove/${id}`,
  })
}
