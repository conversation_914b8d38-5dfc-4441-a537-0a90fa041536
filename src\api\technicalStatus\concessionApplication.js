import request from '@/utils/request'

/**
 * @name 获取让步申请单列表
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.compromiseCode 让步code
 * @param {*} param0.compromiseName 让步名字
 * @param {*} param0.productCode 产品编号
 * @param {*} param0.productName 产品名称
 * @param {*} param0.auditStatus 审核状态
 * @returns 
 */
export const getCompromiseList = ({
  pageSize,
  currentPage,
  compromiseCode,
  compromiseName,
  productCode,
  productName,
  auditStatus,
  typeCodeList,
  sort,
  sidx
}) => request({
  url: '/api/changeCompromise/getCompromiseList',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    compromiseCode,
    compromiseName,
    productCode,
    productName,
    auditStatus,
    typeCodeList,
    sort,
    sidx
  }
})

/**
 * @name 保存让步申请单
 * @param {*} param0.compromiseId id
 * @param {*} param0.compromiseName 让步名字
 * @param {*} param0.productCode 产品编号
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.batchSortie 批架次
 * @param {*} param0.numbers 数量
 * @param {*} param0.factDescription 不良产品事实描述
 * @param {*} param0.reason 原因
 * @param {*} param0.influence 影响
 * @returns 
 */
export const saveCompromise = ({
  compromiseId,
  compromiseName,
  productCode,
  productName,
  productVersion,
  batchSortie,
  numbers,
  factDescription,
  reason,
  reasonStr,
  influence,
  category,
  materialType
}) => request({
  url: '/api/changeCompromise/saveOrUpdateCompromise',
  method: 'POST',
  data: {
    compromiseId,
    compromiseName,
    productCode,
    productName,
    productVersion,
    batchSortie,
    numbers,
    factDescription,
    reason,
    reasonStr,
    influence,
    category,
    materialType
  }
})

/**
 * @name 编辑让步申请单
 * @param {*} param0.compromiseId id
 * @param {*} param0.compromiseName 让步名字
 * @param {*} param0.productCode 产品编号
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.batchSortie 批架次
 * @param {*} param0.numbers 数量
 * @param {*} param0.factDescription 不良产品事实描述
 * @param {*} param0.reason 原因
 * @param {*} param0.influence 影响
 * @returns 
 */
export const updateCompromise = ({
  compromiseCode,
  compromiseId,
  compromiseName,
  productCode,
  productName,
  productVersion,
  batchSortie,
  numbers,
  factDescription,
  reason,
  reasonStr,
  influence,
  category,
  materialType
}) => request({
  url: '/api/changeCompromise/saveOrUpdateCompromise',
  method: 'PUT',
  data: {
    compromiseCode,
    compromiseId,
    compromiseName,
    productCode,
    productName,
    productVersion,
    batchSortie,
    numbers,
    factDescription,
    reason,
    reasonStr,
    influence,
    category,
    materialType
  }
})

/**
 * @name 详情
 * @param {*} param0.compromiseId id
 * @returns 
 */
export const getCompromiseInfo = ({
  compromiseId
}) => request({
  url: '/api/changeCompromise/getCompromiseInfo',
  method: 'GET',
  data: {
    compromiseId
  }
})

/**
 * @name 删除
 * @param {*} param0.compromiseId id
 * @returns 
 */
export const delCompromise = ({
  compromiseId,
  compromiseCode
}) => request({
  url: '/api/changeCompromise/delCompromise',
  method: 'DELETE',
  data: {
    compromiseId,
    delFlag: 1,
    compromiseCode
  }
})