<template>
  <el-row>
    <el-form-item label="控件标题">
      <el-input v-model="activeData.__config__.label" placeholder="请输入控件标题" />
    </el-form-item>
    <el-form-item label="显示标题">
      <el-switch v-model="activeData.__config__.showLabel" />
    </el-form-item>
    <el-form-item label="标题提示">
      <el-input v-model="activeData.__config__.tipLabel" placeholder="请输入标题提示" />
    </el-form-item>
    <el-form-item label="默认值">
      <jnpf-select v-model="activeData.__config__.defaultValue" placeholder="请选择默认值"
        :options="activeData.options" :props="activeData.props" clearable />
    </el-form-item>
    <el-form-item label="排列方式">
      <el-radio-group v-model="activeData.direction" size="small" style="text-align:center">
        <el-radio-button label="horizontal">水平排列</el-radio-button>
        <el-radio-button label="vertical">垂直排列</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-divider>数据选项</el-divider>
    <el-form-item label="" label-width="40px">
      <el-radio-group v-model="activeData.__config__.dataType" size="small"
        style="text-align:center" @change="dataTypeChange">
        <el-radio-button label="static">静态数据</el-radio-button>
        <el-radio-button label="dictionary">数据字典</el-radio-button>
        <el-radio-button label="dynamic">远端数据</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <template v-if="activeData.__config__.dataType==='static'">
      <draggable :list="activeData.options" :animation="340" group="selectItem"
        handle=".option-drag">
        <div v-for="(item, index) in activeData.options" :key="index" class="select-item">
          <div class="select-line-icon option-drag">
            <i class="icon-ym icon-ym-darg" />
          </div>
          <el-input v-model="item.fullName" placeholder="选项名" size="small" />
          <el-input v-model="item.id" placeholder="选项值" size="small" />
          <div class="close-btn select-line-icon" @click="activeData.options.splice(index, 1)">
            <i class="el-icon-remove-outline" />
          </div>
        </div>
      </draggable>
      <div style="margin-left: 29px;">
        <el-button style="padding-bottom: 0" icon="el-icon-circle-plus-outline" type="text"
          @click="addSelectItem">
          添加选项
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button style="padding-bottom: 0" type="text" @click="updateSelectItem">
          批量编辑
        </el-button>
      </div>
    </template>
    <template v-if="activeData.__config__.dataType === 'dictionary'">
      <el-form-item label="数据字典">
        <el-row class="jnpf-el-row">
          <el-col :span="18">
            <JnpfTreeSelect :options="dicOptions" v-model="activeData.__config__.dictionaryType"
              placeholder="请选择数据字典" lastLevel clearable @change="dictionaryTypeChange"
              @selectChange="selectChange">
            </JnpfTreeSelect>
          </el-col>
          <el-col :span="6">
            <el-button @click="goDictionary()" style="float: right;">
              添加</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="存储字段">
        <el-select v-model="activeData.props.value" placeholder="请选择存储字段">
          <el-option label="id" value="id"></el-option>
          <el-option label="enCode" value="enCode"></el-option>
        </el-select>
      </el-form-item>
    </template>
    <template v-if="activeData.__config__.dataType === 'dynamic'">
      <el-form-item label="远端数据">
        <interface-dialog :value="activeData.__config__.propsUrl"
          :title="activeData.__config__.propsName" popupTitle="远端数据" @change="propsUrlChange" />
      </el-form-item>
      <el-form-item label="存储字段">
        <el-autocomplete class="inline-input" v-model="activeData.props.value"
          :fetch-suggestions="querySearch" placeholder="请输入内容" style="width:100%"
          @select="handleSelect($event,'value')">
          <template slot-scope="{ item }">
            <div class="sale-order-popper-item">
              <span>{{ item.defaultValue}}</span>
            </div>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="显示字段">
        <el-autocomplete class="inline-input" v-model="activeData.props.label"
          :fetch-suggestions="querySearch" placeholder="请输入内容" style="width:100%"
          @select="handleSelect($event,'label')">
          <template slot-scope="{ item }">
            <div class="sale-order-popper-item">
              <span>{{ item.defaultValue}}</span>
            </div>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="参数设置" v-if="activeData.__config__.templateJson.length">
        <SelectInterfaceBtn :templateJson="activeData.__config__.templateJson"
          :fieldOptions="formFieldsOptions" :type="3" @fieldChange="onRelationFieldChange" />
      </el-form-item>
    </template>
    <el-divider />
    <template v-if="showType==='pc'">
      <el-form-item label="选项样式">
        <el-radio-group v-model="activeData.optionType">
          <el-radio-button label="default">默认</el-radio-button>
          <el-radio-button label="button">按钮</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="activeData.optionType === 'default'" label="是否边框">
        <el-switch v-model="activeData.border" />
      </el-form-item>
      <el-form-item v-if="activeData.optionType === 'button' ||
                activeData.border" label="组件尺寸">
        <el-radio-group v-model="activeData.size">
          <el-radio-button label="medium">中等</el-radio-button>
          <el-radio-button label="small">较小</el-radio-button>
          <el-radio-button label="mini">迷你</el-radio-button>
        </el-radio-group>
      </el-form-item>
    </template>
    <el-form-item label="是否禁用">
      <el-switch v-model="activeData.disabled" />
    </el-form-item>
    <el-form-item label="是否隐藏">
      <el-switch v-model="activeData.__config__.noShow" />
    </el-form-item>
    <el-form-item label="是否必填">
      <el-switch v-model="activeData.__config__.required" />
    </el-form-item>
    <div>
      <el-dialog :visible.sync="dicVisible" append-to-body
        class="JNPF-dialog JNPF-dialog_center JNPF-dialog-tree-select" lock-scroll width="80%"
        @close="defaultValueChange">
        <dicIndex ref="dicIndex"></dicIndex>
      </el-dialog>
    </div>
    <BatchEditing v-if="updateVisible" ref="batchEditing" @change="handleSure" />
  </el-row>
</template>
<script>
import comMixin from './mixin';
import dynamicMixin from './dynamicMixin';
import handelFlidMixin from './handelFlidMixin';
import dicIndex from '@/views/systemData/dictionary/index.vue';
import BatchEditing from './BatchEditing'
import SelectInterfaceBtn from '@/components/SelectInterfaceBtn'
export default {
  components: {
    dicIndex, BatchEditing, SelectInterfaceBtn
  },
  mixins: [comMixin, dynamicMixin, handelFlidMixin],
  data() {
    return {
      dicVisible: false,
      updateVisible: false,
    }
  },
  mounted() {
    this.getDataInterfaceInfo()
  },
  methods: {
    selectChange() {
      this.$emit('changeSelect')
      this.dictionaryTypeChange(this.dictionaryId)
    },
    defaultValueChange() {
      this.selectChange()
    },
    goDictionary() {
      this.dicVisible = true
      this.$nextTick(() => {
        this.$refs.dicIndex.initData()
      })
    },
    updateSelectItem() {
      this.updateVisible = true
      this.$nextTick(() => {
        this.$refs.batchEditing.init(this.activeData.options)
      })
    },
    handleSure(arr) {
      this.activeData.options = arr || []
    },
    dataTypeChange(val) {
      this.activeData.__config__.defaultValue = ''
      this.activeData.options = []
      this.activeData.props.value = 'id'
      this.activeData.props.label = 'fullName'
      this.activeData.__config__.dictionaryType = ''
      this.activeData.__config__.propsUrl = ''
      this.activeData.__config__.propsName = ''
      this.activeData.__config__.templateJson = []
    },
  }
}
</script>
<style lang="scss" scoped>
.jnpf-el-row {
  >>> .el-input__inner {
    border-radius: 4px 0 0 4px !important;
  }

  >>> .el-button {
    border-left: 0;
    background-color: #f5f7fa;
    font-size: 13px;
    color: #909399;
    border-radius: 0 4px 4px 0;
    line-height: 12px;
  }
  >>> .el-button:hover {
    border-color: #dcdfe6;
  }
}
</style>