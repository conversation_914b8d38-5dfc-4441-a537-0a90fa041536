import request from '@/utils/request'

/**
 * @name 编码规则列表
 * @param {*} param0.ruleName 编码名称
 * @param {*} param0.pageSize 当前页条数
 * @param {*} param0.currentPage 当前页
 * @returns 
 */
export const getCodeRuleList = ({
  ruleName,
  pageSize,
  currentPage
}) => {
  return request({
    url: '/api/system/codeRule',
    method: 'GET',
    data: {
      ruleName,
      pageSize,
      currentPage
    }
  })
}

/**
 * @name 批量删除编码规则
 * @param {*} data ids 
 * @returns 
 */
export const deleteCodeRules = (data) => request({
  url: '/api/system/codeRule',
  method: 'DELETE',
  data
})

/**
 * @name 修改状态
 * @param {*} param0.id ID
 * @param {*} param0.enabledMark 启用状态 0-禁用 1-启用 
 * @returns 
 */
export const updateCodeRuleStatus = ({
  id,
  enabledMark
}) => request({
  url: '/api/system/codeRule/status',
  method: 'POST',
  data: {
    id,
    enabledMark
  }
})

/**
 * @name 新增编码规则
 * @param {*} param0.id ID 
 * @param {*} param0.ruleName 规则名称
 * @param {*} param0.padCharacter 填充字符 
 * @param {*} param0.fillWidth 补起宽度 
 * @param {*} param0.fillDirection 补起方向 
 * @param {*} param0.ruleCode 规则编码 
 * @param {*} param0.codeRule 编码规则 
 * @param {*} param0.codeRuleJson 编码规则json
 * @param {*} param0.enabledMark 启用状态 0-禁用 1-启用 
 * @returns 
 */
export const addCodeRule = ({
  id,
  ruleName,
  padCharacter,
  fillWidth,
  fillDirection,
  ruleCode,
  codeRule,
  codeRuleJson,
  enabledMark
}) => request({
  url: '/api/system/codeRule',
  method: 'PUT',
  data: {
    id,
    ruleName,
    padCharacter,
    fillWidth,
    fillDirection,
    ruleCode,
    codeRule,
    codeRuleJson,
    enabledMark
  }
})

/**
 * @name 新增编码规则
 * @param {*} param0.id ID 
 * @param {*} param0.ruleName 规则名称
 * @param {*} param0.padCharacter 填充字符 
 * @param {*} param0.fillWidth 补起宽度 
 * @param {*} param0.fillDirection 补起方向 
 * @param {*} param0.ruleCode 规则编码 
 * @param {*} param0.codeRule 编码规则 
 * @param {*} param0.codeRuleJson 编码规则json
 * @param {*} param0.enabledMark 启用状态 0-禁用 1-启用 
 * @returns 
 */
export const updateCodeRule = ({
  id,
  ruleName,
  padCharacter,
  fillWidth,
  fillDirection,
  ruleCode,
  codeRule,
  codeRuleJson,
  enabledMark
}) => request({
  url: '/api/system/codeRule',
  method: 'POST',
  data: {
    id,
    ruleName,
    padCharacter,
    fillWidth,
    fillDirection,
    ruleCode,
    codeRule,
    codeRuleJson,
    enabledMark
  }
})

/**
 * @name 获取编码规则详情
 * @param {*} id 
 * @returns 
 */
export const getCodeRuleDetail = (id) => request({
  url: `/api/system/codeRule/${id}`,
  method: "GET"
})