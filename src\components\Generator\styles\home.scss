$selectedColor: #F3F9FF;
$lighterBlue: #409EFF;

.container {
  position: relative;
  width: 100%;
  height: 100%;
}

.components-list {
  box-sizing: border-box;
  height: 100%;

  .components-part {
    background: #fff;
    border-radius: 4px;
    padding: 10px 10px 0;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .components-draggable {
    margin-right: -10px;
  }

  .components-item {
    display: inline-block;
    width: 110px;
    margin: 0 10px 10px 0;
    transition: transform 0ms !important;

    &.disabled {
      .components-body {
        background: #E4E7ED;
        cursor: not-allowed;
        border: none !important;
        color: #999 !important;

        .icon-ym {
          color: #999 !important;
        }

        &:hover {
          border: none !important;
          color: #999 !important;

          .icon-ym {
            color: #999 !important;
          }
        }
      }
    }
  }
}

.components-title {
  font-size: 14px;
  color: #043254;
  line-height: 30px;
  margin-bottom: 10px;
  font-weight: bold;
}

.components-body {
  padding-left: 8px;
  background: $selectedColor;
  font-size: 12px;
  height: 36px;
  cursor: move;
  border: 1px dashed $selectedColor;
  border-radius: 3px;
  color: #043254;
  line-height: 36px;
  display: flex;
  align-items: center;

  i {
    color: #043254;
    line-height: 16px;
    height: 16px;
    margin-right: 4px;
  }

  &:hover {
    border: 1px dashed #409EFF;
    color: #409EFF;

    i {
      color: #409EFF;
    }
  }
}

.left-board {
  width: 250px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow: hidden;
  border-radius: 4px;
}

.left-scrollbar {
  height: 100%;
  overflow: hidden;

  .el-scrollbar__wrap {
    overflow-x: auto;
  }
}

.center-scrollbar {
  height: calc(100% - 42px);
  overflow: hidden;
  box-sizing: border-box;

  .el-scrollbar__wrap {
    overflow-x: auto;
  }

  .el-scrollbar__view {
    overflow-x: hidden;
  }
}

.center-board {
  height: 100%;
  width: auto;
  margin: 0 350px 0 260px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;

  .jnpf-editor-quill {
    .ql-editor {
      min-height: 100px !important;
    }
  }

  .table-tip {
    width: 100%;
    color: #999;
    text-align: center;
    position: absolute;
    top: 60px;
    font-size: 14px;

    &.tab-tip {
      top: 45px;
    }

    &.card-tip {
      top: 30px;
    }
  }

  #ipad {
    height: calc(100% - 42px);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 711px;

    .outeripad {
      background: url('../../../assets/images/iphoneBg.png');
      width: 389px;
      height: 711px;
      padding: 65px 40px;

      .ipadHead {
        background: #f7f8f9;
        text-align: center;

        .ipadHead-img {
          margin: 0 auto;
          height: 20px;
        }
      }

      .ipadbody {
        height: 100%;

        .center-board-row>.el-form {
          height: 550px;
        }

        .center-scrollbar {
          height: 100%;
          overflow: hidden;
        }
      }
    }
  }

}

.column-empty-info {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  .empty-img {
    width: 180px;
    height: 120px;
  }

  p {
    padding: 15px 0;
  }
}

.empty-info {
  position: absolute;
  top: 20%;
  left: calc(50% - 250px);

  &.app-empty-info {
    top: calc(50% - 150px);
    left: calc(50% - 150px);

    .empty-img {
      width: 300px;
      height: 300px;
    }
  }

  .empty-img {
    width: 500px;
    height: 500px;
  }
}

.action-bar {
  position: relative;
  height: 42px;
  text-align: center;
  padding: 0 15px;
  box-sizing: border-box;
  border-bottom: 1px solid #dcdfe6;

  .delete-btn {
    color: #F56C6C !important;
  }

  .unActive-btn {
    color: #606266 !important;

    &:hover {
      color: #1890ff !important;
    }
  }

  .action-bar-right {
    position: absolute;
    right: 15px;
    top: 0;
    display: flex;
    align-items: center;
    height: 42px;
  }
}

.logo {
  position: absolute;
  left: 12px;
  top: 6px;
  line-height: 30px;
  color: #00afff;
  font-weight: 600;
  font-size: 17px;
  white-space: nowrap;

  >img {
    width: 30px;
    height: 30px;
    vertical-align: top;
  }

  .github {
    display: inline-block;
    vertical-align: sub;
    margin-left: 15px;

    >img {
      height: 22px;
    }
  }
}

.center-board-row {
  padding: 12px 12px 15px 12px;
  box-sizing: border-box;
  height: 100%;

  &>.el-form {
    height: calc(100vh - 150px);
  }

  .el-tabs__header {
    margin: 0;
  }
}

.drawing-board {
  height: 100%;
  position: relative;

  .el-select {
    width: 100%;
  }

  .components-body {
    padding: 0;
    margin: 0;
    font-size: 0;
  }

  .sortable-ghost {
    position: relative;
    display: block;
    overflow: hidden;

    i {
      display: none;
    }

    .el-form-item {
      i {
        display: inline;
      }
    }

    &::before {
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      height: 3px;
      background: #409EFF;
      z-index: 2;
    }
  }

  .components-item.sortable-ghost {
    width: 100%;
    height: 60px;
    background-color: $selectedColor;
  }

  .active-from-item {
    &>.el-form-item {
      background: $selectedColor;
      border: 1px solid #409EFF;
    }

    &>.drawing-item-copy,
    &>.drawing-item-delete,
    &>.drawing-item-add-row,
    &>.drawing-item-add-col,
    &>.drawing-item-cell {
      display: block;
    }

    &>.component-name {
      color: $lighterBlue;
    }

  }

  .el-form-item {
    margin-bottom: 10px !important;
  }
}

.drawing-item {
  position: relative;
  cursor: move;

  &.unfocus-bordered:not(.active-from-item)>div:first-child {
    border: 1px dashed #ccc;
  }

  .el-form-item {
    border: 1px dashed #e2e0e0;
    padding: 10px;
  }
}

.drawing-row-item {
  position: relative;
  cursor: move;
  box-sizing: border-box;
  border: 1px dashed #ccc;
  padding: 10px 2px;
  margin-bottom: 10px;

  .drawing-item-copy {
    right: 48px !important;
  }

  .drawing-item-delete {
    right: 16px !important;
  }

  .drawing-row-item {
    margin-bottom: 2px;
  }

  &.drawing-row-item-table {
    padding-top: 15px;

    .el-col {
      margin-top: 15px;
    }
  }

  .el-form-item {
    margin-bottom: 0;
  }

  .drag-wrapper {
    min-height: 80px;
  }

  &.active-from-item {
    border-color: $lighterBlue;
  }

  .component-name {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 18px;
    color: #bbb;
    display: inline-block;
    padding: 5px 6px 0;
  }

  .content-name {
    margin-left: 4px;
  }
}

.drawing-item,
.drawing-row-item {
  &:hover {
    &>.el-form-item {
      background: $selectedColor;
    }

    &>.drawing-item-copy,
    &>.drawing-item-delete,
    &>.drawing-item-add-row,
    &>.drawing-item-add-col,
    &>.drawing-item-cell {
      display: block;
    }
  }

  &>.drawing-item-copy,
  &>.drawing-item-delete,
  &>.drawing-item-add-row,
  &>.drawing-item-add-col,
  &>.drawing-item-cell {
    display: none;
    position: absolute;
    top: -10px;
    width: 22px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 50%;
    font-size: 12px;
    border: 1px solid;
    cursor: pointer;
    z-index: 1;
  }

  &>.drawing-item-copy,
  &>.drawing-item-add-row,
  &>.drawing-item-add-col,
  &>.drawing-item-cell {
    right: var(--rightDistance);
    border-color: $lighterBlue;
    color: $lighterBlue;
    background: #fff;

    &:hover {
      background: $lighterBlue;
      color: #fff;
    }
  }

  &>.drawing-item-add-row {
    right: 108px;
  }

  &>.drawing-item-add-col {
    right: 78px;
  }

  &>.drawing-item-cell {
    top: unset;
    bottom: 0;
    right: 0;
    border-radius: unset;
    background: $lighterBlue;

    i {
      color: #fff;
    }
  }

  &>.drawing-item-delete {
    right: var(--rightDistance);
    border-color: #F56C6C;
    color: #F56C6C;
    background: #fff;

    &:hover {
      background: #F56C6C;
      color: #fff;
    }
  }
}

.drawing-row-item .el-card__body>.el-col {
  margin-top: 0 !important;
  margin-bottom: 10px;
}

.table-wrapper-web {
  overflow: auto;
  display: flex;
  width: 100%;

  &>.el-col {
    width: 200px !important;
    flex-shrink: 0;

    .el-form-item {
      margin-bottom: 0 !important;
    }
  }
}