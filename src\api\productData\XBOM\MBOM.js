import request from '@/utils/request'

// M-BOM --- 列表查询
export function queryMBomList(params) {
  return request({
    url: `/api/mbom/searchAll`,
    method: 'GET',
    data: params
  })
}

// P-BOM --- 列表查询
export function queryPBomList(params) {
  return request({
    url: `/api/ProductPbomController/searchAll`,
    method: 'GET',
    data: params
  })
}

// M-BOM --- 列表修改
export function editMBomList(data) {
  return request({
    url: `/api/mbom/update`,
    method: 'POST',
    data
  })
}


// M-BOM --- 列表删除
export function deleteMBomList(id) {
  return request({
    url: `/api/mbom/${id}`,
    method: 'DELETE',
  })
}

/**
 * @name M-BOM 列表新增
 * @param {*} param0.bomName bom名称
 * @param {*} param0.materialCode 物料编号
 * @param {*} param0.materialVersion 物料版本
 * @param {*} param0.businessType 业务类型
 * @param {*} param0.remark 备注
 * @param {*} param0.mconstructs 结构数据
 * @returns 
 */
// M-BOM --- 列表新增
export function addMBomList({
  bomName,
  materialCode,
  materialVersion,
  businessType,
  remark,
  category,
  materialName,
  mconstructs,
  detectionMetricList
}) {
  return request({
    url: `/api/mbom/save`,
    method: 'POST',
    data: {
      bomName,
      materialCode,
      materialVersion,
      businessType,
      remark,
      category,
      materialName,
      mconstructs,
      detectionMetricList
    }
  })
}

// M-BOM --- 列表单挑数据详情
export function queryMBomDetails(id) {
  return request({
    url: `/api/mbom/getById/${id}`,
    method: 'GET',

  })
}


// M-BOM --- 获取物料编码
export function getMaterialCode(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataHisController/queryByLike`,
    method: 'POST',
    data
  })
}


// M-BOM 结构 表格数据新增
export function addStructureList(data) {
  return request({
    url: `/api/mbomConstruct/sava`,
    method: 'POST',
    data
  })
}


/**
 * @name pbom关联ebom回显
 * @param {*} id 
 * @returns 
 */
export const provideBom = (id) => request({
  url: `/api/ProductEbomController/provideBom/${id}`,
  method: 'GET',
})


/**
 * @name pbom关联ebom回显
 * @param {*} id 
 * @returns 
 */
export const providePBom = (id) => request({
  url: `/api/ProductPbomController/provideBom/${id}`,
  method: 'GET',
})


// M-BOM 结构 表格数据查询
export function queryStructureList(data) {
  return request({
    url: `/api/mbomConstruct/search`,
    method: 'POST',
    data
  })
}
export function queryStructureListVersion(data) {
  return request({
    url: `/api/mbomConstruct/searchHis`,
    method: 'POST',
    data
  })
}

// E-BOM 结构 表格数据修改
export function editStructureList(data) {
  return request({
    url: `api/mbomConstruct/update`,
    method: 'POST',
    data
  })
}

// M-BOM 结构 表格数据删除
export function deleteStructureList(id) {
  return request({
    url: `/api/mbomConstruct/${id}`,
    method: 'DELETE',

  })
}


// M-BOM 图纸 表格数据查询
export function queryDrawList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/getInfoByMbom`,
    method: 'POST',
    data
  })
}



// M-BOM 检测指标 表格数据查询
export function queryTestItemList(data) {
  return request({
    url: `/api/mbomMetrics/searchAll`,
    method: 'POST',
    data
  })
}

// M-BOM 检测指标 表格数据新增
export function addTestItemList(data) {
  return request({
    url: `/api/mbomMetrics/add`,
    method: 'POST',
    data
  })
}

// M-BOM 检测指标 表格数据修改
export function editTestItemList(data) {
  return request({
    url: `/api/mbomMetrics/update`,
    method: 'POST',
    data
  })
}

// M-BOM 检测指标 表格单条数据
export function queryTestItemDetails(id) {
  return request({
    url: `/api/mbomMetrics/getById/${id}`,
    method: 'GET',
  })
}

// M-BOM 检测指标 表格单条数据删除
export function deleteTestItemList(id) {
  return request({
    url: `/api/mbomMetrics//${id}`,
    method: 'DELETE',
  })
}



// E-BOM 查看 左侧的树形结构
export function searchTree(data) {
  return request({
    url: `/api/mbom/searchTree`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构 点击子级展示关联子级
export function searchTreeSon(data) {
  return request({
    url: `/api/mbom/searchTreeSon`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构 反查
export function peggingTree(data) {
  return request({
    url: `/api/mbom/peggingTree`,
    method: 'POST',
    data
  })
}



// BOM 比较
// BOM 比较 获取 BOM 的版本号
export function mbomVersion(code) {
  return request({
    url: `/api/mbomConstruct/getVersion/${code}`,
    method: 'GET',
  })
}


// BOM 比较 获取 BOM 的版本号
export function mbomCompare(data) {
  return request({
    url: `/api/mbomConstruct/contrast/${data.codeOne}/${data.versionOne}/${data.codeTwo}/${data.versionTwo}`,
    method: 'GET',
  })
}



// M-BOM 比较 查询归档的数据
export function queryCompareMBomList(data) {
  return request({
    url: `/api/mbom/searchHisPage`,
    method: 'POST',
    data
  })
}

