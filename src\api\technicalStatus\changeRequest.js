import request from '@/utils/request'

/**
 * @name 获取联络单列表
 * @param {*} param0.contactName 变更联络名称
 * @param {*} param0.contactCode 变更联络单号
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.auditAgree 只查询审核通过状态
 * @param {*} param0.contactType 类型
 * @param {*} param0.auditStatus 状态
 * @param {*} param0.createBy 申请人
 * @returns 
 */
export const getContactList = ({
  contactName,
  contactCode,
  pageSize,
  currentPage,
  auditAgree,
  contactType,// 类型
  auditStatus, //状态
  createBy,// 申请人
  sort,
  sidx
}) => request({
  url: '/api/changeContact/getContactList',
  method: 'GET',
  data: {
    contactName,
    contactCode,
    pageSize,
    currentPage,
    auditAgree,
    contactType,// 类型
    auditStatus, //状态
    createBy,// 申请人
    sort,
    sidx
  }
})

/**
 * @name 保存联络单
 * @param {*} param0.contactId 变更联络id
 * @param {*} param0.contactCode 变更联络code
 * @param {*} param0.contactName 变更联络名称
 * @param {*} param0.contactType 1:工艺类，2:客户类，3:质量类
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productCode 产品code
 * @param {*} param0.workmanshipCompany 工艺负责单位
 * @param {*} param0.changeContent 变更内容
 * @param {*} param0.changeProductList 受影响对象集合
 * @param {*} param0.delFlag
 * @param {*} param0.changeReason 变更原因
 * @param {*} param0.changeReasonStr 变更原因Code
 * @param {*} param0.dynamicFiledList PR扩展属性
 * @param {*} param0.unitDynamicFiledList 产品所属单位扩展属性
 * @returns 
 */
export const saveContact = ({
  contactId,
  contactCode,
  contactName,
  contactType,
  productName,
  productVersion,
  productCode,
  productCompany,
  workmanshipCompany,
  changeContent,
  changeProductList,
  delFlag,
  changeReason,
  changeReasonStr,
  dynamicFiledList,
  unitDynamicFiledList,
  category,
  materialType,
  documentInfo,
  localFiles
}) => request({
  url: '/api/changeContact/saveOrUpdateContact',
  method: 'POST',
  data: {
    contactId,
    contactCode,
    contactName,
    contactType,
    productName,
    productVersion,
    productCode,
    productCompany,
    workmanshipCompany,
    changeContent,
    changeProductList,
    delFlag,
    changeReason,
    changeReasonStr,
    dynamicFiledList,
    unitDynamicFiledList,
    category,
    materialType,
    documentInfo,
    localFiles
  }
})

/**
 * @name 编辑联络单
 * @param {*} param0.contactId 变更联络id
 * @param {*} param0.contactCode 变更联络code
 * @param {*} param0.contactName 变更联络名称
 * @param {*} param0.contactType 1:工艺类，2:客户类，3:质量类
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productCode 产品code
 * @param {*} param0.workmanshipCompany 工艺负责单位
 * @param {*} param0.changeContent 变更内容
 * @param {*} param0.changeProductList 受影响对象集合
 * @param {*} param0.delFlag
 * @param {*} param0.changeReason 变更原因
 * @param {*} param0.changeReasonStr 变更原因Code
 * @param {*} param0.dynamicFiledList PR扩展属性
 * @param {*} param0.unitDynamicFiledList 产品所属单位扩展属性
 * @returns 
 */
export const updateContact = ({
  contactId,
  contactCode,
  contactName,
  contactType,
  productName,
  productVersion,
  productCode,
  productCompany,
  workmanshipCompany,
  changeContent,
  changeProductList,
  delFlag,
  changeReason,
  changeReasonStr,
  dynamicFiledList,
  unitDynamicFiledList,
  category,
  materialType,
  documentInfo,
  localFiles
}) => request({
  url: '/api/changeContact/saveOrUpdateContact',
  method: 'PUT',
  data: {
    contactId,
    contactCode,
    contactName,
    contactType,
    productName,
    productVersion,
    productCode,
    productCompany,
    workmanshipCompany,
    changeContent,
    changeProductList,
    delFlag,
    changeReason,
    changeReasonStr,
    dynamicFiledList,
    unitDynamicFiledList,
    category,
    materialType,
    documentInfo,
    localFiles
  }
})

/**
 * @name 获取变更联络详细信息
 * @param {*} param0.contactId 变更联络id
 * @returns 
 */
export const getConcatDetail = ({
  contactId
}) => request({
  url: '/api/changeContact/getContactInfo',
  method: 'GET',
  data: {
    contactId
  }
})

/**
 * @name 保存申请单
 * @param {*} param0.requestId 申请id
 * @param {*} param0.contactCode 变更联络code
 * @param {*} param0.requestCode 申请单号
 * @param {*} param0.requestName 申请单名称
 * @param {*} param0.productCode 产品编码
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.requestType 设计变更分类
 * @param {*} param0.customerUser 使用客户
 * @param {*} param0.estimateChangeDay 预计更改日期
 * @param {*} param0.authenticationName 认证名称
 * @param {*} param0.assessmentReport 是否需要评估报告
 * @param {*} param0.changeProcess 更改流程
 * @param {*} param0.changeReason 更改原因
 * @param {*} param0.changeReasonStr 更改原因str
 * @param {*} param0.delFlag
 * @param {*} param0.changeAnalysis 变更分析
 * @returns 
 */
export const saveRequest = ({
  requestId,
  contactCode,
  requestCode,
  requestName,
  productCode,
  productName,
  productVersion,
  requestType,
  customerUser,
  estimateChangeDay,
  authenticationName,
  assessmentReport,
  changeProcess,
  changeReason,
  delFlag,
  changeAnalysis,
  changeReasonStr,
  category,
  vo,
  inputName,
  unifiedId,
  materialType
}) => request({
  url: '/api/changeRequest/saveOrUpdateRequest',
  method: 'POST',
  data: {
    requestId,
    contactCode,
    requestCode,
    requestName,
    productCode,
    productName,
    productVersion,
    requestType,
    customerUser,
    estimateChangeDay,
    authenticationName,
    assessmentReport,
    changeProcess,
    changeReason,
    delFlag,
    changeAnalysis,
    changeReasonStr,
    category,
    vo,
    inputName,
    unifiedId,
    materialType
  }
})

/**
 * @name 编辑申请单
 * @param {*} param0.requestId 申请id
 * @param {*} param0.contactCode 变更联络code
 * @param {*} param0.requestCode 申请单号
 * @param {*} param0.requestName 申请单名称
 * @param {*} param0.productCode 产品编码
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.requestType 设计变更分类
 * @param {*} param0.customerUser 使用客户
 * @param {*} param0.estimateChangeDay 预计更改日期
 * @param {*} param0.authenticationName 认证名称
 * @param {*} param0.assessmentReport 是否需要评估报告
 * @param {*} param0.changeProcess 更改流程
 * @param {*} param0.changeReason 更改原因
 * @param {*} param0.changeReasonStr 更改原因str
 * @param {*} param0.delFlag
 * @param {*} param0.changeAnalysis 变更分析
 * @returns 
 */
export const updateRequest = ({
  requestId,
  contactCode,
  requestCode,
  requestName,
  productCode,
  productName,
  productVersion,
  requestType,
  customerUser,
  estimateChangeDay,
  authenticationName,
  assessmentReport,
  changeProcess,
  changeReason,
  delFlag,
  changeAnalysis,
  changeReasonStr,
  category,
  vo,
  inputName,
  unifiedId,
  materialType
}) => request({
  url: '/api/changeRequest/saveOrUpdateRequest',
  method: 'PUT',
  data: {
    requestId,
    contactCode,
    requestCode,
    requestName,
    productCode,
    productName,
    productVersion,
    requestType,
    customerUser,
    estimateChangeDay,
    authenticationName,
    assessmentReport,
    changeProcess,
    changeReason,
    delFlag,
    changeAnalysis,
    changeReasonStr,
    category,
    vo,
    inputName,
    unifiedId,
    materialType
  }
})

/**
 * @name 变更申请列表
 * @param {*} param0.requestCode 申请单号
 * @param {*} param0.requestName 申请单名称
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.auditAgree 审核同意true 
 * @param {*} param0.auditStatus 审核状态
 * @param {*} param0.createBy 创建人 
 * @returns 
 */
export const getRequestList = ({
  pageSize,
  currentPage,
  requestCode,
  requestName,
  auditAgree,
  auditStatus,
  createBy,
  sort,
  sidx
}) => request({
  url: '/api/changeRequest/getRequestList',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    requestCode,
    requestName,
    auditAgree,
    auditStatus,
    createBy,
    sort,
    sidx
  }
})

/**
 * @name 联络表模糊查询
 * @param {*} param0.requestName 联络表名称
 * @returns 
 */
export const getContactListAll = ({
  requestName
}) => request({
  url: '/api/changeContact/getContactListAll',
  method: 'GET',
  data: {
    requestName
  }
})

/**
 * @name 获取申请单详情
 * @param {*} param0.requestId 
 * @param {*} param0.instructionCode
 * @returns 
 */
export const getRequestInfo = ({
  requestId,
  instructionCode
}) => request({
  url: '/api/changeRequest/getRequestInfo',
  method: 'GET',
  data: {
    requestId,
    instructionCode
  }
})

/**
 * @name 变更指令列表
 * @param {*} param0.instructionName 指令名称
 * @param {*} param0.instructionCode 指令单号
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.auditStatus 审核状态
 * @param {*} param0.createBy 创建人
 * @returns 
 */
export const getInstructionList = ({
  pageSize,
  currentPage,
  instructionName,
  instructionCode,
  auditStatus,
  createBy,
  sort,
  sidx
}) => request({
  url: '/api/changeInstruction/getInstructionList',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    instructionName,
    instructionCode,
    auditStatus,
    createBy,
    sort,
    sidx
  }
})

/**
 * @name 保存变更指令
 * @param {*} param0.instructionId 变更指令id
 * @param {*} param0.requestCode 申请单code
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.instructionName 指令名称
 * @param {*} param0.announcementType 变更通告类型
 * @param {*} param0.needTime 需要日期
 * @param {*} param0.delFlag 是否删除标识;是否删除标识 0：正常  1：删除
 * @returns 
 */
export const saveInstruction = ({
  instructionId,
  requestCode,
  instructionCode,
  instructionName,
  announcementType,
  needTime,
  delFlag,
  category,
  materialType,
  unifiedId
}) => request({
  url: '/api/changeInstruction/saveOrUpdateInstruction',
  method: 'POST',
  data: {
    instructionId,
    requestCode,
    instructionCode,
    instructionName,
    announcementType,
    needTime,
    delFlag,
    category,
    materialType,
    unifiedId
  }
})

/**
 * @name 编辑变更指令
 * @param {*} param0.instructionId 变更指令id
 * @param {*} param0.requestCode 申请单code
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.instructionName 指令名称
 * @param {*} param0.announcementType 变更通告类型
 * @param {*} param0.needTime 需要日期
 * @param {*} param0.delFlag 是否删除标识;是否删除标识 0：正常  1：删除
 * @returns 
 */
export const updateInstruction = ({
  instructionId,
  requestCode,
  instructionCode,
  instructionName,
  announcementType,
  needTime,
  delFlag,
  category,
  materialType,
  unifiedId
}) => request({
  url: '/api/changeInstruction/saveOrUpdateInstruction',
  method: 'PUT',
  data: {
    instructionId,
    requestCode,
    instructionCode,
    instructionName,
    announcementType,
    needTime,
    delFlag,
    category,
    materialType,
    unifiedId
  }
})

/**
 * @name 获取用户列表
 * @param {*} param0.userName 用户名称
 * @returns 
 */
export const getUserList = ({
  userName
}) => request({
  url: '/api/changeInstruction/getUserList',
  method: 'GET',
  data: {
    userName
  }
})

/**
 * @name 变更申请单下拉
 * @param {*} param0.requestName 名称
 * @returns 
 */
export const getRequestListAll = ({
  requestName
}) => request({
  url: '/api/changeRequest/getRequestListAll',
  method: 'GET',
  data: {
    requestName
  }
})

/**
 * @name 获取变更指令详情
 * @param {*} param0.instructionId 指令id
 * @returns 
 */
export const getInstructionInfo = ({
  instructionId
}) => request({
  url: '/api/changeInstruction/getInstructionInfo',
  method: 'GET',
  data: {
    instructionId
  }
})

/**
 * @name 指令任务的创建|更新
 * @param {*} param0.taskId 任务id
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.taskCode 任务code
 * @param {*} param0.taskName 任务名称
 * @param {*} param0.personCharge 负责人id
 * @param {*} param0.reviewer 审阅人id
 * @param {*} param0.needTime 任务需要日期
 * @param {*} param0.changeType 变更类型
 * @param {*} param0.beforeTask 前置任务
 * @param {*} param0.taskType 任务类型 1=修订任务 2=实施方案编制任务
 * @param {*} param0.objType 对象类型
 * @param {*} param0.objCode 对象code
 * @param {*} param0.objVersion 对象版本
 * @param {*} param0.changeProductId 产生对象id
 * @param {*} param0.delFlag 1删除
 * @param {*} param0.fileType 交付物文件类型
 * @param {*} param0.inputName 文件名称
 * @param {*} param0.vo 文件信息
 * @param {*} param0.inputRemark 描述
 * @param {*} param0.planCode 
 * @returns 
 */
export const saveOrUpdateChangeTask = ({
  planCode,
  taskId,
  instructionCode,
  taskCode,
  taskName,
  personCharge,
  reviewer,
  needTime,
  changeType,
  beforeTask,
  taskType,
  objCode,
  objType,
  objVersion,
  changeProductId,
  delFlag,
  fileType,
  inputName,
  vo,
  inputRemark,
  changePlanInfos
}) => request({
  url: '/api/instructionTask/saveOrUpdateChangeTask',
  method: 'POST',
  data: {
    planCode,
    taskId,
    instructionCode,
    taskCode,
    taskName,
    personCharge,
    reviewer,
    needTime,
    changeType,
    beforeTask,
    taskType,
    objCode,
    objType,
    objVersion,
    changeProductId,
    delFlag,
    fileType,
    inputName,
    vo,
    inputRemark,
    changePlanInfos
  }
})

/**
 * @name 批量新建指令任务
 * @returns 
 */
export const batchSaveOrUpdateTask = (data) => request({
  url: '/api/instructionTask/batchSaveOrUpdateTask',
  method: 'POST',
  data
})

/**
 * @name 获取受阅人下拉
 * @param {*} param0.userName
 * @returns 
 */
export const getRecipient = ({
  userName
}) => request({
  url: '/api/changeInstruction/getUserList',
  method: 'GET',
  data: {
    userName
  }
})

/**
 * @name 指令变更任务列表
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.planCode 方案code
 * @returns 
 */
export const getInstructionTasks = ({
  instructionCode,
  planCode
}) => request({
  url: '/api/instructionTask/getInstructionTasks',
  method: 'GET',
  data: {
    instructionCode,
    planCode
  }
})

/**
 * @name 保存|删除受影响的对象
 * @param {*} param0.data.formCode 表单code
 * @param {*} param0.data.objType 对象类型
 * @param {*} param0.data.objName 对象名称
 * @param {*} param0.data.objCode 对象code
 * @param {*} param0.data.objVersion 对象版本
 * @param {*} param0.data.totalNumber 数量
 * @param {*} param0.data.types 1:联络，2:申请，3:偏离 4指令任务
 * @returns 
 */
export const addChangeProduct = (data) => request({
  url: '/api/changeProduct/addChangeProduct',
  method: 'POST',
  data
})

// /**
//  * @name 变更联络表保存受影响的对象
//  * @param {*} param0.data.formCode 表单code
//  * @param {*} param0.data.objType 对象类型
//  * @param {*} param0.data.objName 对象名称
//  * @param {*} param0.data.objCode 对象code
//  * @param {*} param0.data.objVersion 对象版本
//  * @returns 
//  */
// export const addContactProduct = (data) => request({
//   url: '/api/changeContact/addContactProduct',
//   method: 'POST',
//   data
// })

/**
 * @name 变更联络删除受影响的对象
 * @param {*} id ID
 * @returns 
 */
export const delContactProduct = ({ id }) => request({
  url: `/api/changeContact/delContactProduct?id=${id}`,
  method: 'DELETE',
})

// /**
//  * @name 变更申请保存受影响的对象
//  * @param {*} param0.data.formCode 表单code
//  * @param {*} param0.data.objType 对象类型
//  * @param {*} param0.data.objName 对象名称
//  * @param {*} param0.data.objCode 对象code
//  * @param {*} param0.data.objVersion 对象版本
//  * @returns 
//  */
// export const addFileRequest = (data) => request({
//   url: '/api/changeRequest/addFileRequest',
//   method: 'POST',
//   data
// })

/**
 * @name 变更申请删除受影响的对象
 * @param {*} id ID
 * @returns 
 */
export const delFileRequest = ({ id }) => request({
  url: `/api/changeProduct/delChangeProduct/${id}`,
  method: 'DELETE',
})

/**
 * @name 指令任务详情
 * @param {*} param0.taskId ID
 * @returns 
 */
export const getInstructionTaskInfo = ({ taskId }) => request({
  url: '/api/instructionTask/getInstructionTaskInfo',
  method: 'GET',
  data: {
    taskId
  }
})

/**
 * @name 获取PR类型|所属单位options
 * @param {*} param0.showName 
 * @returns 
 */
export const getContactPRType = ({ showName }) => request({
  url: '/api/changeContact/getContactPRType',
  method: 'GET',
  data: {
    showName
  }
})

/**
 * @name 生成任务编码
 * @returns 
 */
export const generateTaskCode = () => request({
  url: '/api/instructionTask/generateTaskCode',
  method: 'GET'
})

/**
 * @name 获取受影响对象
 * @param {*} pageSize 
 * @param {*} currentPage 
 * @param {*} codes 更改对象
 * @returns 
 */
// /api/ProductObjectRelationController/queryObjectByCodes
export const queryObjectByCodes = ({
  pageSize,
  currentPage,
  codes
}) => request({
  url: '/api/ProductObjectRelationController/queryChangeByCodes',
  method: 'POST',
  data: {
    pageSize,
    currentPage,
    codes
  }
})

/**
 * @name 获取指令单code
 * @returns 
 */
export const getInstructionCode = () => request({
  url: '/api/changeInstruction/getInstructionCode',
  method: 'GET'
})

/**
 * @name 根据申请表自动生成任务
 * @param {*} param0.instructionCode  指令code
 * @param {*} param0.requestCode  申请单code
 * @returns 
 */
export const autoGenerateTask = ({
  instructionCode,
  requestCode
}) => request({
  url: '/api/changeInstruction/autoGenerateTask',
  method: 'POST',
  data: {
    instructionCode,
    requestCode
  }
})

/**
 * @name 变更方案列表
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.planCode 方案code
 * @param {*} param0.planName 方案名称
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.productCode 产品编码
 * @param {*} param0.productName 产品名称
 * @param {*} param0.auditStatus 审核状态
 * @returns 
 */
export const getChangePlanList = ({
  pageSize,
  currentPage,
  planCode,
  planName,
  instructionCode,
  productCode,
  productName,
  auditStatus,
  sort,
  sidx
}) => request({
  url: '/api/changePlan/getChangePlanList',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    planCode,
    planName,
    instructionCode,
    productCode,
    productName,
    auditStatus,
    sort,
    sidx
  }
})

/**
 * @name 保存变更方案
 * @param {*} param0.planCode 方案code
 * @param {*} param0.planName 方案名称
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.productCode 产品编码
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.changeReason 变更原因
 * @param {*} param0.changeReasonStr 变更原因
 * @param {*} param0.changeType 变更类别
 * @param {*} param0.planInfo 计划信息
 * @param {*} param0.personLiable 责任人
 * @param {*} param0.startTime 开始时间
 * @param {*} param0.endTime 结束时间
 * @returns 
 */
export const savePlan = ({
  planId,
  planCode,
  planName,
  instructionCode,
  productCode,
  productName,
  productVersion,
  changeReason,
  changeReasonStr,
  changeType,
  planInfo,
  personLiable,
  startTime,
  endTime,
  changePlanInfos,
  category,
  materialType,
  unifiedId
}) => request({
  url: '/api/changePlan/saveOrUpdatePlan',
  method: 'POST',
  data: {
    planId,
    planCode,
    planName,
    instructionCode,
    productCode,
    productName,
    productVersion,
    changeReason,
    changeReasonStr,
    changeType,
    planInfo,
    personLiable,
    startTime,
    endTime,
    changePlanInfos,
    category,
    materialType,
    unifiedId
  }
})

/**
 * @name 编辑变更方案
 * @param {*} param0.planCode 方案code
 * @param {*} param0.planName 方案名称
 * @param {*} param0.instructionCode 指令code
 * @param {*} param0.productCode 产品编码
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.changeReason 变更原因
 * @param {*} param0.changeReasonStr 变更原因
 * @param {*} param0.changeType 变更类别
 * @param {*} param0.planInfo 计划信息
 * @param {*} param0.personLiable 责任人
 * @param {*} param0.startTime 开始时间
 * @param {*} param0.endTime 结束时间
 * @returns 
 */
export const updatePlan = ({
  planId,
  planCode,
  planName,
  instructionCode,
  productCode,
  productName,
  productVersion,
  changeReason,
  changeReasonStr,
  changeType,
  planInfo,
  personLiable,
  startTime,
  endTime,
  changePlanInfos,
  category,
  materialType,
  unifiedId
}) => request({
  url: '/api/changePlan/saveOrUpdatePlan',
  method: 'PUT',
  data: {
    planId,
    planCode,
    planName,
    instructionCode,
    productCode,
    productName,
    productVersion,
    changeReason,
    changeReasonStr,
    changeType,
    planInfo,
    personLiable,
    startTime,
    endTime,
    changePlanInfos,
    category,
    materialType,
    unifiedId
  }
})

/**
 * @name 生成变更计划code
 * @returns 
 */
export const getChangePlanCode = () => request({
  url: '/api/changePlan/getChangePlanCode',
  method: 'GET'
})

/**
 * @name 获取需要选择的指令单列表
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.instructionCode 指令编码
 * @param {*} param0.instructionName 指令名称
 * @returns 
 */
export const getChooseInstructionList = ({
  pageSize,
  currentPage,
  instructionCode,
  instructionName,
}) => request({
  url: '/api/changeInstruction/getInstructionList',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    instructionCode,
    instructionName,
    auditStatus: 'effective',
    notUsed: true,

  }
})

/**
 * @name 获取让步申请单详情
 * @param {*} param0.planId id
 * @returns 
 */
export const getChangePlanInfo = ({
  planId
}) => request({
  url: '/api/changePlan/getChangePlanInfo',
  method: 'GET',
  data: {
    planId
  }
})

/**
 * @name 通过code获取关联对象列表
 * @param {*} param0.formCode 表单code
 * @param {*} param0.materialCode 物料code
 * @returns 
 */
export const getProductListByCode = ({
  formCode,
  materialCode
}) => request({
  url: '/api/changeProduct/getProductListByCode',
  method: 'GET',
  data: {
    formCode,
    materialCode
  }
})

/**
 * @name 删除
 * @param {*} param0 
 * @returns 
 */
export const delChangePlan = ({
  planId,
  planCode
}) => request({
  url: '/api/changePlan/delChangePlan',
  method: 'DELETE',
  data: {
    planId,
    delFlag: 1,
    planCode
  }
})

/**
 * @name 修订对象修订成功修改状态
 * @param {*} param0.formCode 表单code
 * @param {*} param0.objCode 对象code
 * @param {*} param0.objStatus 对象状态  1:已修订
 * @param {*} param0.major 大版本
 * @param {*} param0.version 小版本
 * @returns 
 */
export const updateProductStatus = ({
  formCode,
  objCode,
  objStatus,
  major,
  version,
  beforeVersion
}) => request({
  url: '/api/changeProduct/updateProductStatus',
  method: 'PUT',
  data: {
    formCode,
    objCode,
    objStatus,
    major,
    version,
    beforeVersion
  }
})

/**
 * @name 修改修订对象的版本
 * @param {*} param0.formCode 表单code
 * @param {*} param0.objCode 对象code
 * @param {*} param0.major 大版本
 * @param {*} param0.minVersion 小版本
 * @returns 
 */
export const updateProductVersion = ({
  formCode,
  objCode,
  major,
  minVersion
}) => request({
  url: '/api/changeProduct/updateProductVersion',
  method: 'PUT',
  data: {
    formCode,
    objCode,
    major,
    minVersion
  }
})

/**
 * @name 获取修订页面需要的参数
 * @param {*} param0.code 对象编码
 * @param {*} param0.categoryType 关系类型
 * @returns 
 */
export const queryParametersChange = ({
  code,
  categoryType
}) => request({
  url: '/api/ProductObjectRelationController/queryParametersChange',
  method: 'POST',
  data: {
    code,
    categoryType
  }
})

/**
 * @name 验证方案是否可以提审
 * @param {*} param0.formCode planCode
 * @returns 
 */
export const judgingRevisions = ({
  formCode,
  materialCode
}) => request({
  url: '/api/changeProduct/judgingRevisions',
  method: 'GET',
  data: {
    formCode,
    materialCode
  }
})

/**
 * @name 检查指令单是否需要更新任务
 * @param {*} param0.instructionCode instructionCode
 * @returns 
 */
export const checkInstruction = ({
  instructionCode
}) => request({
  url: '/api/changeInstruction/checkInstruction',
  method: 'POST',
  data: {
    instructionCode
  }
})

/**
 * @name 保存-变更实施计划/任务
 * @param {*} param0.planCode 方案code
 * @param {*} param0.taskCode 任务编码
 * @param {*} param0.taskName 任务名称
 * @param {*} param0.describe 任务描述
 * @param {*} param0.relatedPlans 相关方案
 * @param {*} param0.personResponsible 责任人
 * @param {*} param0.planCompleted 计划完成日期
 * @returns 
 */
export const savePlanTask = ({
  planCode,//方案code
  taskCode,//任务编码
  taskName,//任务名称
  describes,//任务描述
  relatedPlans,//相关方案
  personResponsible,//责任人
  planCompleted,//计划完成日期
  tightVOList,
  attachments,
  documentList
}) => request({
  url: '/api/changePlan/task/savePlanTask',
  method: 'POST',
  data: {
    planCode,//方案code
    taskCode,//任务编码
    taskName,//任务名称
    describes,//任务描述
    relatedPlans,//相关方案
    personResponsible,//责任人
    planCompleted,//计划完成日期
    tightVOList,
    attachments,
    documentList
  }
})

/**
 * @name 编辑-变更实施计划/任务
 * @param {*} param0.planCode 方案code
 * @param {*} param0.taskCode 任务编码
 * @param {*} param0.taskName 任务名称
 * @param {*} param0.describe 任务描述
 * @param {*} param0.relatedPlans 相关方案
 * @param {*} param0.personResponsible 责任人
 * @param {*} param0.planCompleted 计划完成日期
 * @returns 
 */
export const updatePlanTask = ({
  planCode,//方案code
  taskCode,//任务编码
  taskName,//任务名称
  describes,//任务描述
  relatedPlans,//相关方案
  personResponsible,//责任人
  planCompleted,//计划完成日期
  tightVOList,
  attachments,
  id,
  documentList,
  changeExecution
}) => request({
  url: '/api/changePlan/task/updatePlanTask',
  method: 'PUT',
  data: {
    planCode,//方案code
    taskCode,//任务编码
    taskName,//任务名称
    describes,//任务描述
    relatedPlans,//相关方案
    personResponsible,//责任人
    planCompleted,//计划完成日期
    tightVOList,
    attachments,
    id,
    documentList,
    changeExecution
  }
})

/**
 * @name 查询-变更实施计划/任务
 * @param {*} param0.pageSize 页面数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.planCode 方案code 
 * @returns 
 */
export const selectPlanTasks = ({
  pageSize,
  currentPage,
  planCode
}) => request({
  url: '/api/changePlan/task/selectPlanTasks',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    planCode
  }
})

/**
 * @name 删除-变更实施计划/任务
 * @returns 
 */
export const deletePlanTask = ({ id }) => request({
  url: `/api/changePlan/task/deletePlanTask/${id}`,
  method: 'DELETE',
})

/**
 * @name 变更实施计划/任务详情
 * @param {*} param0 
 * @returns 
 */
export const getPlanTaskInfo = ({ id }) => request({
  url: `/api/changePlan/task/getPlanTaskInfo/${id}`,
  method: 'GET'
})

/**
 * @name 方案对应的任务
 * @returns 
 */
export const getPlanTaskList = ({
  planCode,
  taskCode
}) => request({
  url: '/api/changePlan/task/getPlanTaskList',
  method: 'GET',
  data: {
    planCode,
    taskCode
  }
})

/**
 * @name 汇报开始
 * @param {*} param0.id ID
 * @param {*} param0.taskProgressVO
 * @returns 
 */
export const startMyPlanTasks = ({
  id,
  taskProgressVO
}) => request({
  url: '/api/changePlan/task/startMyPlanTasks',
  method: 'PUT',
  data: {
    id,
    taskProgressVO
  }
})

/**
 * @name 汇报结束
 * @param {*} param0.id
 * @returns 
 */
export const endMyPlanTasks = ({ id }) => request({
  url: '/api/changePlan/task/endMyPlanTasks',
  method: 'PUT',
  data: {
    id
  }
})

/**
 * @name 方案执行
 * @param {*} param0.planId 方案id
 * @param {*} param0.isExecute 执行操作：0:未执行，1:已执行
 * @returns 
 */
export const executePlan = ({
  planId,
  isExecute
}) => request({
  url: '/api/changePlan/executePlan',
  method: 'POST',
  data: {
    planId,
    isExecute
  }
})

/**
 * @name 变更指令中止
 * @param {*} param0.instructionId
 * @returns 
 */
export const discontinue = ({
  instructionId
}) => request({
  url: '/api/changeInstruction/discontinue',
  method: 'PUT',
  data: {
    instructionId
  }
})

/**
 * @name 作废方案
 * @param {*} param0.planId 方案id
 * @returns 
 */
export const cancelPlan = ({
  planId
}) => request({
  url: '/api/changePlan/cancelPlan',
  method: 'PUT',
  data: {
    planId
  }
})

/**
 * @name 删除紧前任务
 * @returns 
 */
export const delplanTask = (id) => request({
  url: `api/changePlan/task/deletePlanTask/${id}`,
  method: 'DELETE'
})