@import "./font.scss";

.JNPF-common-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 10px;
  flex-shrink: 0;

  .JNPF-common-head-right {
    flex-shrink: 0;
    font-size: 0;

    .el-link {
      margin-left: 12px;
    }

    .JNPF-common-head-icon {
      color: #606266;
    }
  }
}

.JNPF-common-el-tree {
  margin: 10px 0;
}

.JNPF-common-layout {
  height: 100%;
  width: 100%;
  display: flex;
  position: relative;
  background: #EBEEF5;

  .JNPF-common-layout-left {
    width: 320px;
    // max-width: 320px;
    background-color: #fff;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 10px;

    .JNPF-common-title {
      flex-shrink: 0;
      padding: 0 10px;
    }

    .JNPF-common-tree-search-box {
      padding: 10px 10px 0;
    }

    .JNPF-common-el-tree-scrollbar {
      flex: 1;

      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }

    .custom-tree-node {
      width: calc(100% - 30px) !important;
    }

    .JNPF-common-el-tree {
      flex: 1;
      overflow: auto;
      overflow-x: hidden;

      .el-tree-node__content {
        height: 40px;
        line-height: 40px;
      }

      // .is-current>.el-tree-node__content .custom-tree-node {
      //   color: #409eff;
      // }
    }
  }
}

.JNPF-common-layout-center {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .JNPF-common-layout-main {
    flex: 1;
    padding: 0 0 10px;
    background-color: #fff;
    height: 100%;
    overflow: hidden;

    &.nohead {
      padding-top: 10px;
    }
  }
}

.JNPF-common-search-box {
  background: #fff;
  flex-shrink: 0;
  margin-bottom: 10px;
  padding: 15px 10px 0;
  position: relative;

  .el-form-item {
    width: 100%;
    display: flex;
    margin-bottom: 14px !important;


    .el-form-item__label {
      flex-shrink: 0;
      padding-right: 12px;
    }

    .el-form-item__content {
      flex: 1;
    }

    .el-input,
    .el-select,
    .el-cascader,
    .comSelect-container,
    .el-date-editor {
      width: 100%;
    }
  }

  .JNPF-common-search-box-right {
    position: absolute;
    right: 10px;
    top: 15px;
  }
}

.comSelect-container {
  height: 36px;
}

.el-table {
  border-top: 1px solid #EBEEF5;

  thead {

    tr,
    tr th {
      background-color: #f5f7fa;
      font-weight: normal;
      color: #606266;
    }
  }

  .el-switch {
    transform: scale(0.8);
  }

  .el-button--mini.el-button--text {
    padding: 3px 0;
  }

  &.columnTable {
    border-top: none;

    thead {

      tr,
      tr th {
        background-color: #fff;
      }
    }

    td {
      border-bottom: none;
    }

    &.el-table--mini td,
    &.el-table--mini th {
      padding: 3px 0;
    }

    &::before {
      height: 0;
    }
  }

  &::before {
    height: 0 !important;
  }

  .el-table__fixed-right-patch {
    background-color: #f5f7fa;
    top: 0;
  }

  .has-gutter .gutter {
    border-bottom: 1px solid #ebeef5;
  }

  .el-table__empty-block {
    display: inline-block;
  }

  .el-table__column-filter-trigger {
    line-height: 22px;
  }

  .caret-wrapper {
    height: 23px !important;
  }

  .sort-caret.ascending {
    top: 0px !important;
  }

  .sort-caret.descending {
    bottom: 1px !important;
  }

  .el-table__body {
    .is-right {

      .sign-main,
      .signature-main {
        justify-content: flex-end;
      }
    }

    .is-center {

      .sign-main,
      .signature-main {
        justify-content: center;
      }
    }
  }

}

.JNPF-table-delBtn {
  color: #ff3a3a !important;

  &.el-button--text:hover,
  &.el-button--text:focus {
    color: #ff3a3a !important;
  }

  &.is-disabled {
    color: #C0C4CC !important;

    &.el-button--text:hover,
    &.el-button--text:focus {
      color: #C0C4CC !important;
    }
  }
}

.JNPF-dialog {
  .el-input__validateIcon {
    display: none !important;
  }

  &.JNPF-dialog_center {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-dialog {
      border-radius: 4px;
      margin: 0 !important;

      .el-dialog__header {
        border-bottom: 1px solid #dcdfe6;
        padding: 0 20px;
        height: 56px;
        line-height: 56px;

        .el-dialog__headerbtn {
          top: 16px;
          right: 16px;
        }
      }

      .el-dialog__body {
        overflow: auto;
        overflow-x: hidden;
        max-height: 70vh;
        padding: 20px 50px 2px;
      }

      .el-dialog__close {
        font-size: 24px;
      }
    }

    &.form-script-dialog {
      .el-dialog .el-dialog__body {
        padding: 20px 20px 10px;
      }

      .form-script-dialog-body {
        height: 600px;
        display: flex;
        overflow: hidden;

        .left-tree {
          height: 600px;
          width: 220px;
          flex-shrink: 0;
          margin-right: 10px;
          overflow: hidden auto;
        }

        .right-main {
          height: 600px;
          flex: 1;
          display: flex;
          flex-direction: column;

          .codeEditor {
            flex: 1;
            border: 1px solid #dcdfe6;
          }

          .tips {
            flex-shrink: 0;
            padding: 8px 16px;
            background-color: #ecf8ff;
            border-radius: 4px;
            border-left: 5px solid #50bfff;
            margin-top: 20px;

            p {
              line-height: 24px;
              color: #5e6d82;

              span {
                display: inline-block;
                padding-right: 10px;
              }
            }
          }
        }
      }
    }

    &.formula-dialog {
      .el-dialog .el-dialog__body {
        padding: 20px 20px 10px;
      }

      .formula-dialog-body {

        .code-editor-area {
          height: 250px;
          border: 1px solid #dcdfe6;
          border-radius: 6px;
          margin-bottom: 10px;
          overflow: hidden;
        }

        .operation-area {
          height: 250px;
          display: flex;

          .area-item {
            height: 250px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            width: 220px;
            overflow: hidden;

            &.formula-area {
              margin: 0 20px;
            }

            &.formula-desc-area {
              width: 280px;

              .area-content {
                padding: 10px;
              }
            }

            .area-title {
              padding: 0 10px;
              height: 34px;
              line-height: 34px;
              border-bottom: 1px solid #dcdfe6;
            }

            .area-content {
              padding: 10px 0;
              height: 216px;
              overflow: hidden auto;

              .formula-desc-wrapper {
                color: #5e6d82;
                overflow-y: auto;

                &>li {
                  margin-bottom: 4px;
                  word-break: break-all;
                  word-wrap: break-word;
                  list-style-type: none;
                  font-size: 12px;
                  line-height: 18px;
                }

                .formula-name {
                  color: #1890FF;
                }
              }
            }
          }
        }
      }
    }

    &.transfer-dialog {
      .el-dialog .el-dialog__body {
        padding: 10px;
      }

      .transfer-pane__body-tab {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .el-tabs__header {
          margin-bottom: 0;
          flex-shrink: 0;

          .el-tabs__nav {
            width: 100%;

            .el-tabs__item {
              width: 33.33%;
              text-align: center;
              padding: 0 20px;
            }
          }
        }

        &.hasSys-tab {
          .el-tabs__header .el-tabs__nav .el-tabs__item {
            width: 25%;
          }
        }

        .el-tabs__content {
          flex: 1;

          .el-tab-pane {
            height: 100%;
            padding: 10px;
            overflow: auto;

            .JNPF-common-el-tree {
              margin: 0;
            }
          }
        }
      }
    }

    &.JNPF-dialog-tree-select {
      .el-dialog__body {
        height: 60vh;
        padding: 0 0 10px !important;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .JNPF-common-layout-left {
          margin-right: 1px;
        }

        .JNPF-common-search-box {
          margin-bottom: 0;

          .JNPF-common-search-box-right {
            padding: 6px 10px 0 0;
          }
        }
      }
    }

    &.template-dialog {

      .el-dialog__body {
        height: 292px !important;
        padding: 10px 0 10px !important;
        overflow: hidden !important;

        .template-search {
          padding: 0 50px 10px;
        }

        .template-list {
          height: 240px;
          width: 100%;

          .template-item {
            margin: 0 50px;
            height: 40px;
            border-radius: 4px;
            margin-bottom: 10px;
            line-height: 40px;
            padding: 0 20px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            background-color: #ebeef5;
            cursor: pointer;

            &:hover {
              color: #1890ff;
              background-color: #bae7ff;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .template-description {
          height: calc(100% - 80px);
          width: 100%;
        }
      }
    }
  }

  &.enlarge-dialog .el-dialog .el-dialog__header {
    border-bottom: none;
  }

  .el-select {
    width: 100%;
  }
}

.JNPF-dialog-notice {
  .el-dialog__body {
    padding: 0 60px !important;
  }

  .notice-wrapper {
    .title {
      font-size: 18px;
      font-weight: normal;
      text-align: center;
    }

    .info {
      line-height: 35px;
      border-bottom: 1px solid #dcdfe6;
      text-align: center;

      span {
        padding: 0 10px;
      }
    }

    .main {
      margin: 8px 0;
      line-height: 22px;
      min-height: 300px;
      overflow: auto;
    }

    .file-list {
      padding: 10px 0;
      border-top: 1px solid #dcdfe6;
    }
  }
}

.JNPF-dialog-tree {
  .el-dialog {
    .el-dialog__body {
      .el-tree {
        overflow: auto;
        overflow-x: hidden;
        height: 340px;
      }
    }
  }
}

.JNPF-dialog-export {
  .el-dialog {
    .el-dialog__body {
      padding: 20px 20px 0 !important;
    }
  }

  .column-item.el-checkbox {
    width: calc(33.33% - 30px);

    .el-checkbox__label {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
    }
  }

  .export-label {
    font-size: 18px;
    font-weight: bold;
    width: 100%;

    span {
      margin-left: 10px;
      font-size: 14px;
      font-weight: normal;
    }
  }

  .export-line .el-form-item__content {
    border-top: 1px solid #dcdfe6;
  }

  .upload-line {
    line-height: 32px;
    border-bottom: 1px solid #dcdfe6;
    margin-bottom: 5px;
  }

  .footer-tip {
    float: left;
    font-size: 14px;
    color: #999999;
    padding-top: 7px;
  }
}

.JNPF-dialog-import {
  .el-dialog__body {
    padding: 20px 40px 2px !important;
  }

  .import-main {
    height: 500px;
    margin-top: 20px;
    overflow: hidden;
    position: relative;

    .upload {
      display: flex;
      border: 1px solid #dcdfe6;
      margin-bottom: 25px;

      &.error-show {
        margin-top: 10px;
        margin-bottom: 15px;

        .up_left {
          height: 120px;
        }

        .up_right {
          padding-top: 20px;
          font-size: 16px;

          .el-link {
            font-size: 16px;
          }
        }
      }

      .up_left {
        width: 126px;
        height: 140px;
        background: #f9f9f9;
        text-align: center;
        padding-top: 20px;
        flex-shrink: 0;

        img {
          width: 80px;
          height: 80px;
        }
      }

      .up_right {
        color: #333;
        margin-left: 30px;
        font-size: 14px;
        padding-top: 30px;
        flex: 1;

        .title {
          font-size: 18px;
          font-weight: bold;
        }

        .tip {
          margin: 15px 0;
        }
      }

      .upload-area {
        display: flex;
        padding-right: 200px;

        .el-upload {
          margin-right: 50px;
          flex-shrink: 0;
        }

        .el-upload-list {
          flex: 1;
        }

        .el-upload-list__item:first-child {
          margin-top: 5px;
        }
      }
    }

    .success {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 110px;

      .success-title {
        margin: 20px 0;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .contips {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
    }
  }
}

.JNPF-common-title {
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  color: #606266;
  align-items: center;

  &.noBorder {
    border: none;
  }

  h2 {
    font-size: 16px;
    line-height: 50px;
    font-weight: 500;

    &.bold {
      font-weight: 600;
    }
  }

  .options {

    &>i,
    .el-link {
      font-size: 16px;
      margin-left: 6px;
      cursor: pointer;
    }
  }

  &+.el-table {
    border-top: none;
  }
}

.JNPF-dialog-add {
  .el-dialog__body {
    padding: 20px !important;
  }

  .add-main {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .add-item {
      width: 270px;
      height: 136px;
      background: #fef3e6;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding-left: 20px;

      &:hover {
        opacity: 0.9;
      }

      &.add-item-left {
        background: #f1f5ff;

        .add-icon {
          background: #ccd9ff;
          color: #537eff;
        }
      }

      // &.add-item-list {
      //   background: #fef3e6;

      //   .add-icon {
      //     background: #fce1bf;
      //     color: #ea986c;
      //   }
      // }

      .add-icon {
        width: 56px;
        height: 56px;
        margin-right: 10px;
        background: #fce1bf;
        border-radius: 10px;
        color: #ea986c;
        flex-shrink: 0;
        font-size: 30px;
        line-height: 56px;
        text-align: center;
      }

      .add-txt {
        height: 56px;

        P {
          line-height: 28px;
        }

        .add-title {
          font-size: 18px;
          font-weight: bold;
        }

        .add-desc {
          color: #8d8989;
          font-size: 12px;
        }
      }
    }
  }
}

.custom-tree-node {
  width: calc(100% - 24px);
  flex: 1;
  display: flex;
  font-size: 0;
  align-items: center;

  i {
    font-size: 14px;
  }

  .text {
    flex: 1;
    font-size: 14px;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  i+.text {
    padding-left: 6px;
  }

  .more {
    float: right;
  }
}

.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mt-8 {
  margin-top: 8px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.ml-8 {
  margin-left: 8px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-50 {
  margin-left: 50px;
}

.pd-10 {
  padding: 10px;
}

.pd-10-20 {
  padding: 10px 20px;
}

.m-0-10 {
  margin: 0 10px
}

.JNPF-common-page-header {
  padding: 14px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 1px solid #dcdfe6;

  .options {
    flex-shrink: 0;
    margin-left: auto;
  }

  .el-page-header {
    .el-page-header__left {
      flex-shrink: 0;
    }

    .el-page-header__content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .steps {
    width: 300px;
    padding: 6px 20px;
    background: #fff;
    justify-items: flex-start;

    .el-step__title {
      cursor: pointer;
    }
  }
}

.JNPF-preview-main {
  position: absolute;
  background-color: #fff;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  overflow: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  &.nohead {
    padding-top: 10px;
  }

  .btns {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 100;
  }

  .el-table--mini td {
    padding: 2px 0;
  }

  .el-table {
    flex: 1;
  }

  .JNPF-common-search-box {
    margin-bottom: 0;
  }

  .main {
    padding: 10px;
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
  }
}

.JNPF-flex-main {
  display: flex;
  flex-direction: column;

  .el-table {
    flex: 1;

    &::before {
      border-bottom: 0;
      height: 0px
    }
  }

  .el-table__fixed::before,
  .el-table__fixed-right::before {
    border-bottom: 0;
    height: 0px
  }
}

// 处理el-tabs
.JNPF-el_tabs {
  height: 100%;

  &.el-tabs--top {
    display: flex;
    flex-direction: column;
  }

  .el-tabs__content {
    flex: 1;
    overflow: auto;
    overflow-x: hidden;

    .el-tab-pane {
      height: 100%;
    }
  }
}

.text-primary {
  color: #188ae2 !important;
}

.text-success {
  color: #0eac5c !important;
}

.text-info {
  color: #35b8e0 !important;
}

.text-warning {
  color: #f9c851 !important;
}

.text-danger {
  color: #ff5b5b !important;
}

.text-pink {
  color: #ff8acc !important;
}

.text-purple {
  color: #5b69bc !important;
}

.text-inverse {
  color: #3b3e47 !important;
}

.text-dark {
  color: #282828 !important;
}

.text-white {
  color: #ffffff !important;
}

.text-color {
  color: #6a6c6f !important;
}

.text-grey {
  color: #999 !important;
}

.i-default {
  color: #6b7a99 !important;
}

.title-color {
  color: #475059 !important;
}

.JNPF-select-tags {
  display: none !important;
}

.flow-form-main {
  z-index: 110;

  .el-date-editor {
    width: 100%;
  }

  .approve-result {
    position: absolute;
    right: 10px;
    top: 135px;
    z-index: 100;
    width: 100px;
    height: 100px;
    opacity: 0.7;

    .approve-result-img {
      width: 100%;
      height: 100%;

      &.wait {
        background: url('../images/flowStatus/wait.png') no-repeat;
        background-size: 100%;
      }

      &.adopt {
        background: url('../images/flowStatus/adopt.png') no-repeat;
        background-size: 100%;
      }

      &.reject {
        background: url('../images/flowStatus/reject.png') no-repeat;
        background-size: 100%;
      }

      &.revoke {
        background: url('../images/flowStatus/revoke.png') no-repeat;
        background-size: 100%;
      }

      &.cancel {
        background: url('../images/flowStatus/cancel.png') no-repeat;
        background-size: 100%;
      }
    }
  }

  .flow-mask {
    width: 100%;
    height: 100%;
    opacity: 0.0;
    filter: alpha(opacity=00);
    background: #fff;
    position: absolute;
    top: 0px;
    left: 0;
    z-index: 100;
  }

  .com-title {
    height: 60px;
    line-height: 60px;
    text-align: center;
    position: relative;
    color: #606266;

    h1 {
      font-size: 18px;
      margin: 0;
    }

    .number {
      position: absolute;
      right: 0;
      bottom: 0;
      height: 30px;
      line-height: 30px;
      font-size: 14px;
    }
  }

  .flow-form {
    .el-input.is-disabled .el-input__inner {
      background-color: #fff !important;
      border-color: #DCDFE6 !important;
      color: #606266;
    }

    .el-textarea.is-disabled .el-textarea__inner {
      background-color: #fff !important;
      border-color: #DCDFE6 !important;
      color: #606266 !important;
    }

    .is-checked {
      .el-radio__input.is-disabled+span.el-radio__label {
        color: #1890ff !important;
      }
    }
  }

  .el-form {
    padding-top: 10px;
    flex: 1;
    overflow: auto;
    overflow-x: hidden;

    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }

  .timeline-cell {
    line-height: 30px;

    .signImg {
      width: 300px;
      vertical-align: top;
    }
  }

  .timeline-icon {
    font-size: 16px;
    background-color: #fff;
    margin-left: -2px;
  }

  .el-tabs__header {
    padding: 0 10px;
  }

  .JNPF-el_tabs .el-tabs__content {
    overflow: hidden;

    .el-tab-pane {
      overflow: auto;
      overflow-x: hidden;
      padding: 0 10px 10px;
    }
  }
}

.dynamic-form-main {
  height: calc(100% - 61px);
  overflow: auto;
  overflow-x: hidden;
  padding: 10px;
}

.functional,
.blend {
  .navbar {

    &.dark {
      background: #031e39;
      border-bottom: none;

      .functional-logo-container {
        background: #031e39;
      }

      .right-menu {
        .right-menu-item {
          color: #fff;
        }
      }

      .icon-ym {
        color: #fff;
      }

      .top-menu .el-menu {

        .el-menu-item,
        .el-submenu__title {
          color: #fff;

          i {
            color: #fff;
          }

          &.is-active {
            color: #1890ff;

            i {
              color: #1890ff;
            }
          }

          &:hover {
            color: #1890ff;

            i {
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  .top-menu {
    .el-menu {
      height: 60px;
      background: transparent;

      &.el-menu--horizontal {
        border-bottom: none;

        .el-icon-arrow-down {
          display: none !important;
        }
      }

      .el-menu-item,
      .el-submenu__title {
        height: 61px !important;
        line-height: 60px !important;
        // width: 120px;
        float: left;
        text-align: center;
        background: transparent !important;
        color: #333;

        i {
          font-weight: normal;
          color: #333;
        }

        .left-icon {
          margin-left: 0 !important;
          margin-right: 4px;
        }

        &.is-active {
          color: #1890ff;

          i {
            color: #1890ff;
          }
        }

        &:hover {
          color: #1890ff;

          i {
            color: #1890ff;
          }
        }
      }

      .el-submenu {
        // width: 120px;
        float: left;
        text-align: center;

        &:hover {
          .el-submenu__title {
            color: #1890ff;
            background: transparent;

            i {
              color: #1890ff;
            }
          }
        }

        &.is-active {
          .el-submenu__title {
            color: #1890ff;

            i {
              color: #1890ff;
            }
          }
        }
      }
    }

    .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
      background: transparent;
    }
  }

  &.el-menu--horizontal {

    .el-menu {
      &.el-menu--popup {
        min-width: 160px;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
      }

      .nest-menu {
        .el-submenu {
          &.is-active {
            .el-submenu__title {
              color: #1890ff;

              i {
                color: #1890ff;
              }
            }
          }
        }

        .el-menu-item,
        .el-submenu__title {

          .el-submenu__icon-arrow {
            margin-top: -5px;
          }

          .left-icon {
            font-size: 20px;
            margin-right: 6px;
            margin-left: 15px;
          }

          &:hover {
            color: #1890ff;

            i {
              color: #1890ff;
            }
          }

          &.is-active {
            color: #1890ff;

            i {
              color: #1890ff;
            }
          }
        }
      }
    }

    &.dark {
      .el-menu {
        background: #031e39;

        .el-menu-item,
        .el-submenu__title {
          background: #031e39;
          color: #fff;

          i {
            color: #fff;
          }
        }

      }
    }
  }
}

#app .plain {
  .navbar-platform {
    color: #666;
    line-height: 60px;
    font-size: 16px;
    margin-left: 20px;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
    background: #001529;
    color: #fff;
  }

  .plain-sidebar-container {
    width: 80px;
    background-color: #001529;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 0 66px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 66px 12px 0 rgba(0, 0, 0, 0.1);

    .el-scrollbar {
      height: calc(100% - 60px);
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .left-icon {
          font-size: 28px;
        }
      }
    }

    .el-menu {
      .el-menu-item {
        height: 70px !important;
        text-align: center;
      }

      .left-icon {
        margin-right: 0;
        font-size: 30px;
        margin-bottom: 0;
      }

      .mainTitle {
        font-size: 12px;
        line-height: 24px;
        margin-top: -8px;
      }
    }

    .el-submenu {
      overflow: hidden;
      height: 70px !important;
      text-align: center;

      &>.el-submenu__title {
        padding: 0 !important;
        height: 70px !important;

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
  }

  .main-container {
    margin-left: 80px;
  }

}

.plain.el-menu--vertical {
  top: 0;

  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
  }
}

.JNPF-full-dialog {
  .el-dialog__header {
    padding: 0;
  }

  .el-dialog__body {
    padding: 0;
    height: 100%;
    overflow: hidden;
  }

  .JNPF-full-dialog-header {
    padding: 0 20px;
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      height: 60px;
      width: 410px;
      display: flex;
      align-items: center;
    }

    .header-logo {
      width: auto;
      height: 60px;
      vertical-align: top;
      margin-right: 3px;
      font-size: 30px;
      color: #fff;
    }

    .header-txt {
      display: inline-block;
      margin: 0;
      color: #333;
      font-size: 18px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
      cursor: pointer;
    }

    .steps {
      width: 450px;
      padding: 6px 20px;
      background: #fff;
      justify-items: flex-start;
      flex-shrink: 0;

      &.steps2 {
        width: 300px;
      }

      &.steps4 {
        width: 600px;
      }

      .el-step__title {
        cursor: pointer;
      }
    }

    .options {
      width: 410px;
      text-align: right;
    }
  }

  .main {
    height: calc(100vh - 60px);
    overflow: hidden;
    background: #EBEEF5;
    padding: 10px;

    .basic-box {
      height: 100%;
      overflow: hidden;
    }

    .basicForm {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      height: 100%;
      overflow: auto;
      overflow-x: hidden;

      .el-select {
        width: 100%;
      }
    }
  }
}

.table-actions {
  margin-top: 10px;
  border: #ebeef5 1px dashed;
  text-align: center;
}

.JNPF-common-drawer {
  .el-drawer__header {
    height: 60px;
    border-bottom: 1px solid #dcdfe6;
    padding: 0 20px;
    margin-bottom: 0;

    &>span {
      line-height: 60px;
    }
  }

  .el-drawer__body {
    overflow: hidden;
  }

  .JNPF-flex-main {
    height: calc(100vh - 60px);

    .dynamicForm {
      flex: 1;
      padding: 10px;
      overflow: auto;
    }

    .drawer-footer {
      padding: 10px 20px 20px;
      width: 100%;
      box-sizing: border-box;
      text-align: right;
    }
  }

  .JNPF-common-search-box {
    margin-bottom: 0;

    .JNPF-common-search-box-right {
      padding: 6px 10px 0 0;
    }
  }
}

.sysLog-main {
  .JNPF-common-drawer {
    .el-drawer__body {
      overflow: auto;
    }
  }

  .drawer-form {
    margin: 20px 20px 0;

    .el-collapse-item__content {
      word-wrap: break-word;

      .jnpf-code-box {
        background: #848484;
        padding: 15px;
        color: #fff;
        font-size: 12px;
        border-radius: 4px;
      }
    }
  }
}

.portal-common-noData {
  text-align: center;
  padding-top: 50px;

  &.portal-common-noData-eChart {
    padding-top: 70px;

    .noData-img {
      width: 200px;
      height: 200px;
    }
  }

  .noData-img {
    width: 100px;
    height: 100px;
  }

  .noData-txt {
    font-size: 14px;
    color: #909399;
    line-height: 20px;
  }
}

.input-table-footer-btn {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;

  .footer-btn {
    display: inline-block;
    margin-right: 10px;
  }
}

.portal-layout-nodata {
  text-align: center;
  position: absolute;
  top: calc(50% - 200px);
  left: calc(50% - 200px);

  .layout-nodata-img {
    width: 400px;
    height: 400px;
  }

  .layout-nodata-txt {
    margin-top: -60px;
    font-size: 20px;
    color: #909399;
    line-height: 30px;
  }
}

.portal-todoList-box {
  .el-card__header {
    height: 55px;
    padding: 0;
  }

  .el-card__body {
    width: 100%;
    height: calc(100% - 55px);
  }

  .portal-todoList-box-body {
    padding: 16px 20px;
    height: 100%;
    overflow: hidden;

    .item {
      display: block;
      line-height: 20px;
      font-size: 0;
      margin-bottom: 12px;
      cursor: pointer;

      &::after {
        content: "";
        clear: both;
        overflow: hidden;
      }

      .name {
        font-size: 14px;
        display: inline-block;
        width: calc(100% - 90px);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        vertical-align: top;
      }

      .time {
        font-size: 14px;
        display: inline-block;
        color: #999;
        width: 90px;
        text-align: right;
      }
    }
  }
}

.portal-eChart-box {
  display: flex;
  flex-direction: column;

  .el-card__header {
    height: 55px;
    padding: 0;
  }

  .el-card__body {
    width: 100%;
    overflow: hidden;
    flex: 1;
  }

  .eChart-box-body,
  #chart {
    width: 100%;
    height: 100%;
  }

  #chart {
    z-index: 10;
    padding: 10px;
  }
}

.el-table {
  .el-rate {
    vertical-align: baseline;
  }

  .el-slider {
    .el-slider__runway {
      .el-slider__button-wrapper {
        z-index: 3 !important;
      }
    }
  }
}

.dynamicDetail {

  .dynamicDetail-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .dy-fileList {
    .dy-fileList-item {
      color: #606266;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: color 0.3s;
      white-space: nowrap;
      cursor: pointer;
      margin-top: 10px;
      line-height: 25px;

      .el-icon-document {
        margin-right: 7px;
        line-height: inherit;
      }

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  .dy-img {
    width: 120px;
    height: 120px;
    overflow: hidden;
    margin: 0 8px 8px 0;
    border: 1px solid #c0ccda;
    border-radius: 6px;
  }

  .el-color-picker__mask {
    background: none;
  }

  .el-switch.is-disabled {
    opacity: 1;
  }

  .slider-box {
    position: relative;

    .slider-mark {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: transparent;
      z-index: 1100;
    }
  }

  .el-table th {
    line-height: 23px !important;
  }
}

// 重定义element-ui 样式
.el-message-box__wrapper .el-message-box {
  border-width: 0;
}

.el-rate {
  display: inline-block;
  vertical-align: text-top;
}

.el-message-box {
  .el-textarea {
    textarea {
      min-height: 88px !important;
    }
  }
}

.el-table__expanded-cell[class*=cell] {
  padding: 5px 50px 5px 25px !important;
}

.el-button+.el-popconfirm,
.el-button+.el-dropdown,
.el-popconfirm+.el-button,
.el-popconfirm+.el-dropdown {
  margin-left: 10px;
}

textarea.el-textarea__inner {
  resize: none;
}

.el-form--label-top .el-form-item__label {
  padding: 0 !important;
}

.el-input-number.is-controls-right .el-input__inner {
  text-align: left !important;
}

.el-popconfirm__main {
  margin: 14px 0;
}

.el-button [class*=icon-ym-]+span {
  margin-left: 5px;
}

.el-form .el-form-item__content .el-alert__content {
  line-height: 20px;
  min-height: 20px;
}

.el-input-number.el-input-number--small {
  width: 160px;
}

// 布局错位问题修正
.el-col {
  .el-form-item--small .el-form-item__label {
    line-height: 33px;
  }

  .el-form-item--mini .el-form-item__label {
    line-height: 29px;
  }
}

.item-table {
  .el-link {
    font-size: 12px;
  }
}

.word-form {
  padding: 0 !important;
  margin: 0 !important;
  border-top: 1px solid #dcdfe6;
  border-left: 1px solid #dcdfe6;

  .mb-10,
  .mb-20 {
    margin-bottom: unset !important;
  }

  .table-grid-box {
    margin-bottom: 0;

    td {
      padding-top: 0;
    }
  }

  .el-card__header {
    border-right: 1px solid #dcdfe6;
  }

  .el-tabs__header {
    margin-bottom: 0 !important
  }

  &.word-from-detail {
    .el-form-item__label+.el-form-item__content {
      padding: 0 15px;

      .el-rate,
      .el-color-picker,
      .el-switch {
        padding-left: 0;
      }

      .amountChinese {
        margin-left: 0;
      }
    }

    .el-col.item-table {
      border-bottom: none;
    }
  }

  .el-col {
    border-right: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    padding: 0 !important;
    box-sizing: border-box;

    &.item-card {
      border: none
    }
  }

  .el-form-item {
    margin-bottom: 0 !important;

    .el-form-item__label+.el-form-item__content {
      border-left: 1px solid #dcdfe6;
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .el-form-item__error {
      top: 68% !important;
    }

    .el-form-item__label {
      padding-top: 5px;
      padding-bottom: 5px;
    }
  }

  .el-input__inner {
    // border: 0 !important;
  }



  textarea {
    border: 0 !important;
  }

  .table-actions {
    border: none;
    margin-top: 0;
  }

  .el-row {
    margin: 0 !important;

    .el-col {
      border: 0;
    }
  }

  .el-card {
    box-shadow: none !important;
    border: 0;
    margin-bottom: 0;
    border-radius: 0;

    .el-card__body {
      padding: 0;
    }

    .el-row {
      .el-col {
        border: 0;
      }
    }
  }

  h1 {
    border: 0 !important;
    border-bottom: 0 !important;
  }

  .ql-toolbar {
    border: 0 !important;
    border-bottom: 1px solid #dcdfe6 !important;
  }

  .ql-container {
    border: 0 !important;
  }

  .el-radio-group,
  .el-checkbox-group,
  .el-rate,
  .el-color-picker,
  .el-switch {
    padding-left: 15px;
  }

  .el-slider,
  .UploadFile-container {
    padding: 0 15px;
  }

  .input-number {
    margin-left: 0px;
  }


  .amountChinese {
    margin-left: 15px;
  }

  .el-input-group--append,
  .el-input-group--prepend {
    padding: 0 15px;
  }

  .el-input-number {
    margin-left: -15px !important;
  }

  .work-number {
    margin-left: 15px !important;
  }

  .JNPFAmount {
    padding: 0 15px;

    .el-input-number {
      margin-left: 0;
    }
  }

  .el-table {

    td,
    th {
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6 !important;

      &:last-child {
        border-right: none !important;
      }

      &:hover {

        &::after,
        &::before {
          border-color: transparent !important;
        }
      }
    }

    .el-input-number {
      margin-left: 0;
    }

    .el-color-picker,
    .el-switch {
      padding-left: 0;
    }
  }

  .el-table th:nth-last-child(2) {
    border-right: none !important;
  }

  .JNPF-common-title {
    border-bottom: 1px solid #dcdfe6 !important;
  }
}

.complexHeader {
  &.el-table th:nth-last-child(2) {
    border-right: 1px solid #dcdfe6 !important;
  }
}

.table-switch.el-switch.is-disabled {
  opacity: 1;

  .el-switch__core,
  .el-switch__label {
    cursor: pointer;
  }
}

.table-checkbox.el-checkbox.is-disabled {

  .el-checkbox__inner,
  .el-checkbox__inner::after,
  input,
  .el-checkbox__label {
    cursor: pointer !important;
  }

  .el-checkbox__inner {
    background-color: #fff !important;

    &:hover {
      border-color: #1890ff !important;
    }
  }

  &.is-checked {
    .el-checkbox__inner {
      background-color: #1890ff !important;
      border-color: #1890ff !important;

      &::after {
        border-color: #fff !important;
      }
    }

    input {
      color: #fff !important;
      border-color: #fff !important;
    }
  }

}

.el-dropdown-menu__item .icon-ym {
  vertical-align: -1px;
}

.transfer__body {
  line-height: 32px;
  display: flex;
  justify-content: space-around;
  padding-top: 10px;
  height: 400px;

  .transfer-pane {
    width: 360px;
  }

  .transfer-pane__tools {
    margin-bottom: 8px;
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .removeAllBtn {
      color: #f56c6c;
    }
  }

  .transfer-pane__body {
    position: relative;
    width: 100%;
    height: calc(100% - 40px);
    overflow: auto;
    overflow-x: hidden;
    font-size: 14px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    .el-tab-pane {
      padding: 10px 0 !important;
    }

    &.left-pane {
      .custom-title {
        height: 39px;
        padding: 0 12px;
        line-height: 39px;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        border-bottom: 1px solid #dcdfe6;

        &+.single-list {
          height: calc(100% - 40px);
        }
      }

      .single-list {
        height: 100%;
        overflow: auto;
        overflow-x: hidden;

        .selected-item-user {
          cursor: pointer;
        }

        .selected-item {
          padding: 0px 12px;
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }

  .selected-item-user {
    padding: 0 12px;

    &:hover {
      background-color: #f5f7fa;
    }

    .selected-item-main {
      border-bottom: 1px solid #F4F6F9;
      display: flex;
      align-items: center;
      height: 50px;
    }

    .selected-item-headIcon {
      flex-shrink: 0;

      &.icon {
        width: 36px;
        height: 36px;
        text-align: center;

        i {
          font-size: 22px;
          line-height: 36px;
        }
      }
    }

    .selected-item-text {
      min-width: 0;
      flex: 1;
      margin-left: 10px;

      .name {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .organize {
        height: 17px;
        line-height: 17px;
        color: #999999;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .el-icon-delete:hover {
      color: #f56c6c;
      cursor: pointer;
    }
  }

  .selected-item-user-multiple {
    padding-left: 12px;
    position: relative;

    .selected-item-title {
      font-size: 14px;
      color: #303133;
      display: flex;
      align-items: center;

      span {
        padding-left: 6px;
      }
    }

    .selected-item-main {
      box-sizing: content-box;
      display: flex;
      align-items: center;
      height: 64px;
      padding-left: 21px;
      position: relative;

      &:hover {
        background-color: #f5f7fa;
      }

      &:last-child {
        border-bottom: 1px solid #F2F5F8;
      }

      &:not(:last-child) {
        &:before {
          position: absolute;
          content: "";
          bottom: 0;
          right: 0;
          width: calc(100% - 21px);
          height: 1px;
          background-color: #F2F5F8;
        }
      }
    }

    .selected-item-headIcon {
      flex-shrink: 0;

      &.icon {
        width: 36px;
        height: 36px;
        text-align: center;

        i {
          font-size: 22px;
          line-height: 36px;
        }
      }
    }

    .selected-item-icon {
      width: 36px;
      height: 36px;
      background: linear-gradient(193deg, #A7D6FF 0%, #1990FA 100%);
      border-radius: 50%;
      line-height: 36px;
      color: #FFFFFF;
      font-size: 14px;
      text-align: center;
    }

    .selected-item-text {
      flex: 1;
      margin-left: 10px;
      min-width: 0;

      .name {
        color: #333333;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .organize {
        color: #999999;
        height: 17px;
        line-height: 17px;
        color: #999999;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .delete {
      margin-right: 15px;
      font-size: 15px;
      align-items: center;

      &:hover {
        color: #f56c6c;
        cursor: pointer;
      }
    }
  }

  .right-pane {
    box-sizing: border-box;
    overflow: auto;
    border: 1px solid #dcdfe6;

    .selected-item {
      padding: 0px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:hover {
        background-color: #f5f7fa;
      }

      span {
        max-width: 90%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .el-icon-delete:hover {
        color: #f56c6c;
        cursor: pointer;
      }
    }
  }
}

.blend {
  .navbar {
    &.dark {
      background: #031e39;
      border-bottom: none;

      .right-menu {
        .right-menu-item {
          color: #fff;
        }
      }

      .icon-ym {
        color: #fff;
      }

      .el-tabs__item {
        color: #fff;
      }
    }
  }

  #topMenu .el-menu-item.submenu-title-noDropdown,
  #topMenu .el-menu .el-submenu>.el-submenu__title {
    padding: 0 20px !important;
  }
}

.popupSelect-container {
  width: 100%;

  .popover-container {
    width: 100%;

    .el-popover__reference-wrapper,
    .el-select {
      width: 100%;
    }
  }

  &>.el-select {
    width: 100%;
  }
}

.required-sign {
  color: #f56c6c;
}

.child-table-column {
  .child-table__row {
    background: transparent;
    border-bottom: 1px solid #ebeef5 !important;
    display: flex;
    align-items: center;
    min-height: 39px;

    td {
      border: none !important;
      flex-shrink: 0;

      &.td-flex-1 {
        flex: 1;
      }

      .cell {
        min-height: 23px !important;
        //white-space: pre-wrap;

        &.ellipsis {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .el-rate {
            white-space: pre-wrap !important;
          }
        }
      }


    }

    &:last-child {
      border-bottom: none !important;
    }
  }

  .expand-more-btn {
    height: 39px;
    text-align: center;
    padding-top: 4px;
  }
}

.el-table {
  td.child-table-box {
    padding: 0 !important;
    vertical-align: top !important;

    &>.cell {
      padding: 0 !important;
    }
  }

  .table-child-first {
    border-right: none !important;
  }

  .td-child-first {
    width: 1px;
    flex-shrink: 0;

    .cell {
      padding: 0 !important;
    }
  }
}

.jnpf-table__empty-text {
  padding: 30px;
  line-height: 30px;
}

.jnpf-detail-text {
  color: #999;

}

.sign-main,
.signature-main {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  .sign-img,
  .signature-img {
    width: 100px;
    height: 50px;
  }

  .add-sign,
  .add-signature {
    height: 50px;
    font-size: 36px;
    margin-top: 10px;
    color: #2188ff;
  }

  .sign-title,
  .signature-title {
    font-size: 16px;
    color: #2188ff;
  }
}

.upAndDown-button {
  position: absolute;
  left: 20px;
}

.options {
  .dropdown {
    margin-right: 10px;
  }

  .el-button {
    min-width: 70px;
  }
}

.dropdown-item {
  min-width: 70px;
  text-align: center;
}

.row-action {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 32px;

  .ym-custom-arrow-expand {
    position: absolute;
    top: 50%;
    opacity: 0;
    z-index: -1;
    transform: translate(-50%, -50%);
    cursor: pointer;
    line-height: 32px;
    margin-left: 10px;
  }
}

.el-table__row:hover {
  .index {
    display: none;

    &.btn-disabled {
      display: block;

      &+.ym-custom-arrow-expand {
        opacity: 0;
        z-index: -1;
      }
    }
  }

  .ym-custom-arrow-expand {
    z-index: 9;
    opacity: 1;
  }
}

.table-grid {
  width: 100%;
  text-align: center;
  border-collapse: collapse;
  table-layout: fixed;

  .table-cell {
    min-height: 45px;
    overflow: auto;
    padding-top: 10px;
  }

  .drawing-row-item {
    padding: 10px 2px;
    text-align: left;
  }
}

.table-grid-box {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin-bottom: 18px;

  &>tbody {
    &>tr {
      &>td {
        border: var(--borderWidth) var(--borderType) var(--borderColor);
        background-color: var(--backgroundColor);
        overflow: hidden;
        height: 50px;
        padding: 18px 18px 0;
      }
    }
  }
}

.el-autocomplete-suggestion {
  min-width: 180px !important;
}

.tooltip-question {
  color: #a0acb7 !important;
  opacity: 0.8;
}

.condition-clearn {
  color: #ff3a3a !important;
  font-size: 24px;
  cursor: pointer;
}

.condition-del-btn {
  .icon-ym-btn-clearn {
    font-size: 12px !important;
  }
}

.el-table-column--selection .cell {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.login-type-box {
  display: flex;
  align-items: center;

  .circle-box {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;
  }

  .circle-box-primary {
    background-color: #1890FF;
  }

  .circle-box-error {
    background-color: #ed6f6f;
  }
}

.input-number {
  .el-input-number--small {
    width: 100% !important;
  }
}

.jnpf-release-dialog {
  >>>.el-dialog {
    .el-dialog__body {
      padding: 12px 55px;
    }
  }

  .release-main {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;

    .release-item {
      width: 220px;
    }

    .top-item {
      position: relative;
      width: 220px;
      height: 70px;
      cursor: pointer;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      text-align: center;
      color: #606266;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;

      &.active {
        border-color: #1890ff;
        color: #1890ff;
        box-shadow: 0 0 6px rgba(6, 58, 108, 0.1);

        .item-icon {
          border-color: #1890ff;
        }

        .icon-checked {
          display: block;
        }
      }

      .item-icon {
        display: inline-block;
        border: 1px solid #606266;
        text-align: center;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        font-size: 16px;
        margin-right: 10px;
        line-height: 26px;
        border-width: 1px;
      }

      .item-title {
        font-size: 16px;
        font-weight: 400;
      }

      .icon-checked {
        display: none;
        width: 18px;
        height: 18px;
        border: 18px solid #1890ff;
        border-left: 18px solid transparent;
        border-top: 18px solid transparent;
        border-bottom-right-radius: 4px;
        position: absolute;
        right: 0px;
        bottom: 0px;

        i {
          font-size: 16px;
          position: absolute;
          top: 0;
          left: -2px;
          color: #fff;
        }
      }
    }

    .released {
      padding: 5px 10px;
      width: 220px;
      line-height: 30px;
      overflow: auto;
      border-radius: 4px;
      background: #ebeef5;
      max-height: 120px;
    }
  }
}