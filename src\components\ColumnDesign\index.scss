.columnDesign-container {
  height: 100%;
  width: 100%;
  position: relative;
  padding-top: 42px;

  .main-board {
    height: 100%;
    width: auto;
    margin: 0 350px 0 0;
    padding: 20px 10px 10px;
    overflow: auto;
    overflow-x: hidden;
    background: #fff;
    border-radius: 0 0 4px 4px;

    .cap {
      margin-bottom: 20px;
    }

    .noSearch {
      text-align: center;
      color: #666;
      padding: 5px;
    }
  }

  .right-board {
    width: 340px;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    padding-top: 3px;
    margin-left: 10px;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;

    .left-tree-query {
      >>>.el-form-item__label {
        padding: 0;
      }
    }

    .field-box {
      position: relative;
      height: calc(100% - 42px);
      overflow: hidden;
      box-sizing: border-box;

      .el-scrollbar {
        height: 100%;
        overflow-x: hidden; //隐藏水平滚动条

        .el-scrollbar__view {
          padding: 10px;
        }

        >>>.el-scrollbar__bar.is-vertical {
          width: 6px !important;
        }
      }

      .searchList,
      .columnList {
        height: 100%;
      }
    }

    >>>.top-tabs {
      .el-tabs__header {
        margin-bottom: 0 !important;
      }

      .el-tabs__nav {
        width: 100%;
      }

      .el-tabs__item {
        width: 33%;
        text-align: center;
        padding: 0;
      }

      &.top-tabs_app {
        .el-tabs__item {
          width: 25%;
          text-align: center;
          padding: 0;
        }
      }
    }
  }

  .setting-box {
    >>>.el-select {
      width: 100%;
    }

    .btn-cap {
      color: #909399;
      font-size: 12px;
    }

    .btnsList {

      .btn-label {
        width: 80px;
        display: inline-block;
        line-height: 32px;
      }

      >>>.el-checkbox {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;

        .el-checkbox__input {
          line-height: 28px;
        }

        .el-checkbox__label {
          width: 226px;
        }
      }

      .upload {
        width: 216px;
        float: right;
        margin-bottom: 10px;
      }
    }

    .columnBtnList {
      >>>.el-checkbox {
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .typeList {
    display: flex;
    flex-wrap: wrap;

    .item:nth-child(3n+3) {
      margin-right: unset;
    }

    .item {
      width: 100px;
      margin-bottom: 15px;
      margin-right: 10px;

      &.item-box {
        width: 150px;

        .item-img {
          height: 100px;
        }
      }

      .item-img {
        width: 100%;
        height: 70px;
        background: #EBEEF5;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        position: relative;
        border: 1px solid #EBEEF5;

        img {
          width: 100%;
          height: 100%;
          z-index: -1;
        }

        &.checked {
          border: 1px solid #409eff;
        }

        .icon-checked {
          display: block;
          width: 12px;
          height: 12px;
          border: 12px solid #409eff;
          border-left: 12px solid transparent;
          border-top: 12px solid transparent;
          border-bottom-right-radius: 4px;
          position: absolute;
          right: -1px;
          bottom: -1px;

          i {
            position: absolute;
            top: -2px;
            left: -2px;
            font-size: 12px;
            color: #fff;
          }
        }
      }

      .item-name {
        font-size: 12px;
        color: #707070;
        margin-top: 10px;
        text-align: center;
      }
    }
  }

  .custom-btns-list {
    .custom-item {
      display: flex;
      border: 1px dashed #fff;
      box-sizing: border-box;

      & .close-btn {
        cursor: pointer;
        color: #f56c6c;
      }

      & .el-input+.el-input {
        margin-left: 4px;
      }
    }

    .custom-item+.custom-item {
      margin-top: 4px;
    }

    .custom-item.sortable-chosen {
      border: 1px dashed #409eff;
    }

    .custom-line-icon {
      line-height: 32px;
      font-size: 22px;
      padding: 0 4px;
      color: #606266;

      .icon-ym {
        font-size: 20px;
        line-height: 32px;
        display: inline-block;
      }

      &.option-drag {
        padding-left: 0;
      }

      &.close-btn {
        padding-right: 0;
      }
    }

    .custom-line-value {
      width: 105px;
      flex-shrink: 0;
      line-height: 32px;
      font-size: 14px;
    }

    .custom-btn {
      padding: 9px !important;
    }

    .option-drag {
      cursor: move;
    }
  }
}