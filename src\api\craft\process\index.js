import request from '@/utils/request'

// 获取基础工序信息分类
export function getProcessTypeList(query) {
  return request({
    method: 'get',
    url: '/api/productProcessCategoryController/getList',
    data: query,
  })
}

/*获取基础工序信息列表*/
export function list(params) {
  return request({
    method: 'get',
    url: '/api/ProductProcessController/queryByLike',
    data: params,
  })
}
/*获取基础工序信息列表-分页*/
export function listPage(query) {
  return request({
    method: 'get',
    url: '/apm/baseOperation/listPage',
    data: query,
  })
}
/*新增基础工序*/
export function save(query) {
  return request({
    method: 'post',
    url: '/apm/baseOperation/save',
    data: query,
  })
}

/*编辑基础工序*/
export function update(query) {
  return request({
    method: 'post',
    url: '/apm/baseOperation/update',
    data: query,
  })
}
/*删除基础工序*/
export function remove(id) {
  return request({
    method: 'get',
    url: `/apm/baseOperation/remove/${id}`,
  })
}
/*启用禁用*/
export function enable(id) {
  return request({
    method: 'get',
    url: `/apm/baseOperation/enable/${id}`,
  })
}
//导入模板下载
export function download() {
  return request({
    method: 'get',
    url: `/apm/baseOperation/downloadTemplate`,
    responseType: 'blob',
  })
}
//导入物料Bom信息
export function importProdt(query) {
  return request({
    method: 'post',
    url: `/apm/baseOperation/importBaseOperation`,
    data: query,
  })
}

