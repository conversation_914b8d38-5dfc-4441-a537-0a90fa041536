import request from '@/utils/request'

// df-bom --- 分页查询
export function queryDFBomList(params) {
  return request({
    url: `/api/ProductDfbomController/queryByLike`,
    method: 'GET',
    data: params
  })
}

// df-bom --- 新增
export function addDFBomList(data) {
  return request({
    url: `/api/ProductDfbomController/save`,
    method: 'POST',
    data
  })
}

// df-bom --- 删除
export function deleteDFBomList(id) {
  return request({
    url: `/api/ProductDfbomController/${id}`,
    method: 'DELETE',
  })
}

// df-bom --- 修改
export function editDFBomList(data) {
  return request({
    url: `/api/ProductDfbomController/update`,
    method: 'PUT',
    data
  })
}

// df-bom --- 查看详情
export function checkDFBomList(id) {
  return request({
    url: `/api/ProductDfbomController/detail/${id}`,
    method: 'GET',
  })
}

// DF-BOM 目录管理

// 新增目录
export function addDirectory(data) {
  return request({
    url: `/api/ProductDfbomLibraryController/save`,
    method: 'POST',
    data
  })
}

// 删除目录
export function deleteDirectory(id) {
  return request({
    url: `/api/ProductDfbomLibraryController/${id}`,
    method: 'DELETE',
  })
}

// 修改目录
export function modifyDirectory(data) {
  return request({
    url: `/api/ProductDfbomLibraryController/update`,
    method: 'PUT',
    data
  })
}

// 目录查询
export function queryDirectory(data) {
  return request({
    url: `/api/ProductDfbomLibraryController/queryAll?code=${data.code}&version=${data.version}&id=${data.id}`,
    method: 'GET',
  })
}


// DF-BOM BOM 的数据接口

// BOM 列表分页查询
export function queryBOMList(data) {
  return request({
    url: `/api/ProductDfbomLibraryDataController/queryByLike`,
    method: 'POST',
    data
  })
}
// BOM 列表 新增
export function addBOMList(data) {
  return request({
    url: `/api/ProductDfbomLibraryDataController/save`,
    method: 'POST',
    data
  })
}
// BOM 列表 修改
export function editBOMList(data) {
  return request({
    url: `/api/ProductDfbomLibraryDataController/update`,
    method: 'PUT',
    data
  })
}

// BOM 列表 查询
export function queryBOMListDetails(id) {
  return request({
    url: `/api/ProductDfbomLibraryDataController/detail/${id}`,
    method: 'GET',
  })
}

// BOM 列表 删除
export function deleteBOMList(id) {
  return request({
    url: `/api/ProductDfbomLibraryDataController/${id}`,
    method: 'DELETE',
  })
}