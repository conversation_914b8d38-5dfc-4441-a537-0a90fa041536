<template>
  <transition name="el-zoom-in-center" v-if="visible">
    <div class="JNPF-preview-main flow-form-main">
      <div class="JNPF-common-page-header">
        <div class="title">
          <div @click="close" class="title-back-wrap">
            <i class="el-icon-back"></i>
            <div>返回</div>
          </div>
          <slot name="title"></slot>
        </div>
        <div class="handle">
          <slot name="handle"></slot>
        </div>
      </div>
      <div class="center-notData"></div>
      <div style="flex: 1;">
        <slot name="content"></slot>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  data() {
    return {
      visible: false
    }
  },
  methods: {
    acceptParams() {
      this.visible = true
    },
    close() {
      this.visible = false
    }
  }
}
</script>
<style lang="scss">
.JNPF-preview-main {
  .JNPF-common-page-header {
    padding: 14px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    border-bottom: 1px solid #dcdfe6;

    .title {
      display: flex;
      align-items: center;

      &-back-wrap {
        cursor: pointer;
        color: #3377FF;
        display: flex;
        align-items: center;

        i {
          margin-right: 10px;
        }

        &::after {
          content: "";
          width: 1px;
          height: 16px;
          background-color: #DBDFE9;
          margin: 0 22px;
        }

      }
    }

  }

  .center-notData {
    height: 10px;
    background-color: #ebeef5;
  }

}
</style>