.drop-element, .drop-element:after, .drop-element:before, .drop-element *, .drop-element *:after, .drop-element *:before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.drop-element {
  position: absolute;
  display: none; }
  .drop-element.drop-open {
    display: block; }

.drop-element.drop-theme-basic {
  max-width: 100%;
  max-height: 100%; }
  .drop-element.drop-theme-basic .drop-content {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    font-family: inherit;
    background: #eeeeee;
    color: #444444;
    padding: 1em;
    font-size: 1.1em;
    line-height: 1.5em; }
