$bg-color : #ebeef5;
$line-color : #A9B4CD;
$base-size : 12px;

// Mixin flex 垂直居中布局
@mixin flex-center() {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
}

//  三点省略 支持单行多行
// Mixin { n:Number } n：省略行数限制 
@mixin ellipsis($n) {
  overflow: hidden;
  text-overflow: ellipsis;

  @if $n>1 {
    display: -webkit-box;
    -webkit-line-clamp: $n;
    -webkit-box-orient: vertical;
  }

  @else {
    white-space: nowrap;
  }
}

// 按钮变大效果
@mixin btn-bigger() {
  transform: scale(1.2);
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, .1);
}

.node-wrap-box {
  position: relative;
  @include flex-center();
  flex-direction: column;

  &.condition,
  &.branchFlow,
  &.interflow {
    padding: 30px 50px 0
  }

  &.empty {
    overflow: hidden;
  }

  &.approver::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    border-style: solid;
    border-width: 8px 6px 4px;
    border-color: $line-color transparent transparent;
    background: $bg-color;
  }

  &.approver.branchFlow::before,
  &.approver.interflow::before {
    top: 20px;
  }

  &.error {

    &.condition .error-tip,
    &.branchFlow .error-tip,
    &.interflow .error-tip {
      right: 0;
    }

    .error-tip {
      right: -40px;
    }

    .flow-path-card {
      border: 1px solid #f00;

      &:hover {
        border-width: 0;
      }
    }
  }

  .error-tip {
    position: absolute;
    right: 1px;
    top: 15%;
    width: 30px;
    height: 30px;
    color: #f00;
    cursor: pointer;
    border-radius: 50%;
    border: 1px solid;
    line-height: 30px;
    transition: right .5s;
  }

  &.condition .error-tip,
  &.branchFlow .error-tip,
  &.interflow .error-tip {
    right: 60px;
  }
}

.end-node {
  font-size: $base-size;
  text-align: center;
  @include flex-center();
  flex-direction: column;

  &::before {
    content: "";
    width: 10px;
    height: 10px;
    margin: auto;
    border: none;
    margin-bottom: 12px;
    border-radius: 50%;
    background: #A9B4CD;
  }
}

.flow-path-card {
  width: 220px;
  min-height: 82px;
  font-size: $base-size;
  border-radius: 4px;
  text-align: left;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.3);
  background: #FFF;
  border-radius: 2px;
  font-size: 12px;

  &:hover {
    box-shadow: 0 0 0 2px #1890ff, 0 0 5px 4px rgba(0, 0, 0, 0.2);
  }

  &.copy {
    .header {
      background-color: #1890ff;
    }
  }

  &.timer {
    .header {
      color: #f5811c;
      border-bottom: 1px solid #EEEEEE;

      .actions {
        color: #606266;
      }
    }
  }

  &.approver,
  &.subFlow {
    &:hover {
      box-shadow: 0 0 0 2px #1890ff, 0 0 5px 4px rgba(0, 0, 0, 0.2);
    }

    .header {
      background-color: #1890ff;
    }
  }

  &.start-node {
    &:hover {
      box-shadow: 0 0 0 2px #576a95, 0 0 5px 4px rgba(0, 0, 0, 0.2);
    }

    .header {
      background-color: #576a95;
    }
  }


  .header {
    padding-left: 10px;
    padding-right: 30px;
    width: 100%;
    height: 30px;
    line-height: 30px;
    color: white;
    position: relative;
    box-sizing: border-box;

    .title-box {
      position: relative;
      display: inline-block;
      @include ellipsis(1);
    }

    .title-input {
      position: absolute;
      left: 0;
      border: none;
      background: inherit;
      color: inherit;
      opacity: 0;
      margin-top: 6px;

      &:focus {
        border-radius: 4px;
        font-size: $base-size;
        padding: 2px;
        padding-left: 4px;
        width: 97%;
        margin-left: 1px;
        height: 18px;
        box-sizing: border-box;
        box-shadow: 0 0 1px 1px #1890ff;
        background-color: $bg-color;
        color: black;
        opacity: 1;
      }
    }

    .title-text {
      vertical-align: middle;
    }

    >.actions {
      position: absolute;
      right: 0;
      top: 0;
      visibility: hidden;
    }

    >.async-state {
      position: absolute;
      right: 25px;
      top: 5px;
    }
  }

  &.timer:hover {
    .actions {
      visibility: visible;
      margin-right: 4px;
    }
  }

  &.subFlow {
    .header {
      .title-box {
        width: 140px !important;
      }
    }
  }

  &:not(.start-node):not(.timer):hover {
    .actions {
      visibility: visible;
      margin-right: 4px;
    }

    .title-text {
      border-bottom: 1px dashed currentColor;
    }
  }

  &.start-node:hover {
    .title-text {
      border-bottom: 1px dashed currentColor;
    }
  }

  .body {
    position: relative;
    padding: 10px;
    box-sizing: border-box;

    .text {
      word-break: break-all;
      margin: 0 ellipsis(4);
    }
  }

  .icon-wrapper {
    position: absolute;
    top: 0;
    height: 100%;
    width: 14px;
    box-sizing: border-box;

    &.left {
      left: 0;
    }

    &.right {
      right: 0;
    }

    > {

      .right-arrow,
      .left-arrow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.flow-path-card.condition {
  .header {
    line-height: 30px;
    color: inherit;
    border-bottom: 1px solid #EEEEEE;

    .title-box {
      height: auto !important;
    }

    .title-text {
      color: #15bc83;
    }

  }

  .body {
    padding: 10px;
    color: #606266;
  }

  .icon-wrapper {
    &:hover {
      background-color: #f1f1f1;
    }
  }

  .right-arrow,
  .left-arrow {
    visibility: hidden;
  }

  &:hover {

    .right-arrow,
    .left-arrow {
      visibility: visible;
    }

    .priority {
      display: none;
    }
  }
}

.col-box:first-of-type>.node-wrap .left {
  display: none;
}

.col-box:last-of-type>.node-wrap .right {
  display: none;
}

.add-node-btn-box {
  width: 220px;
  height: 100px;
  position: relative;
  padding-top: 30px;
  margin: auto;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    margin: auto;
    width: 1px;
    height: 100%;
    background-color: $line-color;
  }

  .add-node-btn {
    display: flex;
    justify-content: center;

    .btn {
      width: 32px;
      height: 32px;
      border-radius: 16px;
      cursor: pointer;
      outline: none;
      background-color: #1890ff;
      border-color: transparent;
      transition: transform 0.5s;

      &:hover {
        @include btn-bigger()
      }

      .icon {
        color: white;
      }
    }
  }
}

.branch-wrap {
  .branch-box-wrap {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
  }

  .branch-box {
    align-items: stretch;
    border-bottom: 1px solid $line-color;
    border-top: 1px solid $line-color;
    box-sizing: border-box;
    background: $bg-color;

    >.col-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      &:first-of-type {

        &::before,
        &::after {
          content: "";
          position: absolute;
          left: 0;
          height: 3px;
          width: calc(50% - 1px);
          background: $bg-color;
        }

        &::before {
          top: -2px;
        }

        &::after {
          bottom: -2px;
        }
      }

      &:last-of-type {

        &::before,
        &::after {
          content: "";
          position: absolute;
          right: 0;
          height: 3px;
          width: calc(50% - 1px);
          background: $bg-color;
        }

        &::before {
          top: -2px;
        }

        &::after {
          bottom: -2px;
        }
      }

      .center-line {
        height: 100%;
        width: 1px;
        background: $line-color;
        position: absolute;
      }
    }

    >.btn {
      font-size: 14px;
      z-index: 99;
      cursor: pointer;
      position: absolute;
      top: 0;
      left: 50%;
      outline: none;
      transform: translate(-50%, -50%);
      padding: 9px 16px;
      border: none;
      border-radius: 15px;
      background: white;
      box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.2);
      transition: transform .3s;
      color: #1890ff;

      &:hover {
        transform: scale(1.1) translate(-46%, -50%);
      }
    }
  }
}

.condition-box {
  display: flex;
  justify-content: space-around;
  align-items: center;
  text-align: center;
  padding: 10px 0;

  >div:nth-child(1) .iconfont {
    color: #FF943E;
  }

  >div:nth-child(2) .iconfont {
    color: #3296FA;
  }

  .condition-disabled {
    color: #C0C4CC;

    .condition-icon {
      background: #e5e5e5;
      color: #999;
      cursor: default;

      &:hover {
        background: #e5e5e5;
        box-shadow: none;

        >.icon-ym,
        >[class^=el-icon-],
        >.ym-custom {
          color: #999;
        }
      }
    }
  }

  .condition-icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    border: 1px solid #e5e5e5;
    border-radius: 30px;
    box-sizing: border-box;
    font-size: 12px;
    cursor: pointer;
    margin-bottom: 4px;

    .icon-ym,
    [class^=el-icon-],
    .ym-custom {
      font-size: 32px;
    }

    &:hover {
      background: #3296FA;
      box-shadow: 0 0 8px 4px rgba(0, 0, 0, .1);

      >.icon-ym,
      >[class^=el-icon-],
      >.ym-custom {
        color: white;
      }
    }
  }
}

.relative {
  position: relative;
}

.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.icon {
  vertical-align: middle;
  width: 14px;
  height: 14px;
  font-size: 14px;
}

.priority {
  position: absolute;
  right: 0;
  font-size: 12px;
}

input::-ms-clear,
input::-ms-reveal {
  display: none;
}