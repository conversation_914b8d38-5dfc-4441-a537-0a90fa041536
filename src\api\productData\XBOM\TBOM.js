import request from '@/utils/request'


// 分页查询
export function queryTBomList(params) {
  return request({
    url: `/api/tbom/searchAll`,
    method: 'GET',
    data: params
  })
}

// E-bom --- 获取物料编码（产品选择）
export function getMaterialCode(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/queryByLike`,
    method: 'POST',
    data
  })
}

// 新增
export function addTBomList(data) {
  return request({
    url: `/api/tbom/save`,
    method: 'POST',
    data
  })
}

// T-bom --- 删除
export function deleteTBomList(id) {
  return request({
    url: `/api/tbom/${id}`,
    method: 'DELETE',
  })
}

// T-bom --- 修改
export function editTBomList(data) {
  return request({
    url: `/api/tbom/update`,
    method: 'POST',
    data
  })
}
// T-bom --- 新增(新)
export function addTBomListNew(data) {
  return request({
    url: `/api/tbomTest/save`,
    method: 'POST',
    data
  })
}

// T-bom --- 修改(新)
export function editTBomListNew(data) {
  return request({
    url: `/api/tbomTest/update`,
    method: 'POST',
    data
  })
}

// T-bom --- 下一步新增
export function nextTBomSave(data) {
  return request({
    url: `/api/tbom/save`,
    method: 'POST',
    data
  })
}

// T-bom --- 下一步编辑
export function nextTBomEdit(data) {
  return request({
    url: `/api/tbom/save`,
    method: 'PUT',
    data
  })
}

// T-bom --- 查看详情
export function getTBomDetails(id) {
  return request({
    url: `/api/tbom/getById/${id}`,
    method: 'GET',
  })
}

// 指标查询
export function queryTbomMetricsList(data) {
  return request({
    url: `/api/tbomMetrics/searchAll`,
    method: 'POST',
    data
  })
}
// 查询公共测试项
export function queryTbomCommonList(data) {
  return request({
    url: `/api/tbomCommon/searchAll`,
    method: 'POST',
    data
  })
}
// 指标详情
export function getTbomMetricsDetails(id) {
  return request({
    url: `/api/tbomMetrics/getById/${id}`,
    method: 'GET',
  })
}

// 删除测试依据
export function deleteTbomAccording(id) {
  return request({
    url: `/api/tbomAccording/${id}`,
    method: 'DELETE',
  })
}

// 测试项删除
export function deleteTbomTest(id) {
  return request({
    url: `/api/tbomTest/${id}`,
    method: 'DELETE',
  })
}
// 指标删除
export function deleteTbomMetrics(id) {
  return request({
    url: `/api/tbomMetrics/${id}`,
    method: 'DELETE',
  })
}


