<template>
  <div :class="['custom-width-container', draggingH ? 'none-select' : '']" :style="{ width: wrapWidth + 'px' }">
    <slot style="width: 100%;"></slot>
    <div @mousedown="startDragH" ref="rightRef" class="custom-width-right-border"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      draggingH: false,
      wrapWidth: 320
    };
  },
  mounted() {
    document.addEventListener("mousemove", this.onDrag);
    document.addEventListener("mouseup", this.endDrag);
  },
  beforeDestroy() {
    document.removeEventListener("mousemove", this.onDrag);
    document.removeEventListener("mouseup", this.endDrag);
  },
  methods: {
    startDragH(e) {
      this.draggingH = true;
      this.startX = e.clientX;
    },
    onDrag(e) {
      if (this.draggingH) {
        let delta = e.clientX - this.startX;
        this.wrapWidth = this.wrapWidth + delta;
        this.startX = e.clientX
      }
    },
    endDrag() {
      this.draggingH = false;
    },
  },
};
</script>
<style lang="scss" scoped>
>>>.JNPF-common-layout-left {
  width: 100%;
}

.custom-width-container {
  display: flex;
  // width: 200px;
  margin-right: 10px;
  overflow: hidden;
  position: relative;
}

.custom-width-right-border {
  width: 6px;
  height: 100%;
  position: absolute;
  right: 0px;
  cursor: col-resize;
  background-color: rgb(222, 222, 222);
  z-index: 2;
}
</style>
<style>
.none-select {
  user-select: none
}
</style>