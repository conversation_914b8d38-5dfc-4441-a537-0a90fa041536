.qm-image {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #000
}

.qm-image .qm-image-scene {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute
}

.qm-image .qm-image-scene .qm-image-rect {
    position: absolute;
    border: 1px solid #fff
}

.qm-image .qm-image-view {
    position: absolute
}

.qm-image .qm-image-background,
.qm-image .qm-image-item {
    display: block;
    position: absolute;
    background: #000
}

.qm-container,
.qm-view {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden
}

.qm-container canvas,
.qm-view canvas {
    outline: none
}

.qm-rect-selcetion {
    position: absolute;
    background: #000;
    border: 1px solid #000
}

.qm-loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px
}

.qm-loading .qm-loading-gif {
    width: 360px;
    height: 300px;
    display: block;
    margin: 0 auto;
    background: center;
    /*    background-image: url(/page/loading.gif)*/
}

.qm-loading-progress {
    position: absolute;
    left: 50%;
    top: 20%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    opacity: 0.5;
}

.qm-dwg {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;
    overflow: hidden
}

.qm-dwg .qm-scene {
    position: relative
}

.qm-dwg .qm-scene .qm-dwg-view {
    position: relative
}

.qm-dwg .qm-scene .qm-dwg-tile {
    position: absolute;
    width: 512px;
    height: 512px;
    z-index: 2;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat
}

.qm-dwg .qm-rect {
    position: absolute;
    border: 2px solid #fff;
    z-index: 9;
    display: none
}

.qmd-properties:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('pro.png');
    background-size: cover;
}

.qmd-plan:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('plan.png');
    background-size: cover;
}

.qmd-home:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('home.png');
    background-size: cover;
}

.qmd-information:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('info.png');
    background-size: cover;
}

.qmd-tree:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('tree.png');
    background-size: cover;
}

.qmd-set:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('set.png');
    background-size: cover;
}

.qmd-zoomrect:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('select.png');
    background-size: cover;
}

.qmd-dingwei:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('dingwei.png');
    background-size: cover;
}

.qmd-dingweiM:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('dingweiM.png');
    background-size: cover;
}

.qmd-3D:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('3D.png');
    background-size: cover;
}

.qmd-measure:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('celiang.png');
    background-size: cover;
}

.qmd-lookat:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('sf.png');
    background-size: cover;
}

.qmd-firstperson:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('walk.png');
    background-size: cover;
}

.qmd-walk:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('walk.png');
    background-size: cover;
}

.qmd-circlelook:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('circle.png');
    background-size: cover;
}

.qmd-section-axial:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('feng.png');
    background-size: cover;
}

.qmd-view:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('camera.png');
    background-size: cover;
}

.qmd-extmodel:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('sb.png');
    background-size: cover;
}

.qmd-edit:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('edit.png');
    background-size: cover;
}

.qmd-see:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('see.png');
    background-size: cover;
}

.qmd-list:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('list.png');
    background-size: cover;
}

.qmd-add:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('add.png');
    background-size: cover;
}

.qmd-bof:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('play.png');
}

.qmd-bangding:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('bd.png');
    background-size: cover;
}

.qmd-delete:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background: url('del.png');
    background-size: cover;
}

.qmd-circle:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('circle2.png');
    background-size: cover;
}

.qmd-cancle:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('cancle.png');
    background-size: cover;
}

.qmd-save:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('save.png');
    background-size: cover;
}

.qmd-rectangle:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('rectangle.png');
    background-size: cover;
}

.qmd-cloud:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('cloud.png');
    background-size: cover;
}

.qmd-text:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('text.png');
    background-size: cover;
}

.qmd-arrow:before {
    content: '';
    display: block;
    width: 28px;
    height: 28px;
    background: url('icon-arrow_right.png');
    background-size: cover;
}

.qm-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #fff
}

.qm-collapse .qm-group-content {
    display: none
}

.qm-collapse .qm-icon:before {
    transform: rotate(-40deg)
}

.qm-icon {
    position: relative;
    float: left;
    padding: 6px
}

.qm-icon:before {
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-right: 8px solid #888;
    border-top: 8px solid transparent
}

.qm-label {
    margin: 3px 0;
    display: block;
    height: 14px;
    float: left
}

.qm-label input[type='checkbox'] {
    display: none
}

.qm-label .qm-checkbox {
    display: inline-block;
    width: 14px;
    height: 14px;
    overflow: hidden;
    text-align: center;
    line-height: 14px;
    background: #fff;
    border-radius: 2px;
    position: relative
}

.qm-label .qm-checkbox-name {
    display: inline-block;
    vertical-align: top;
    line-height: 14px;
    margin-left: 5px
}

.qm-label.qm-half .qm-checkbox {
    background: #EEEEEE
}

.qm-label.qm-half .qm-checkbox:after {
    width: 8px;
    height: 2px;
    background: #fff;
    content: '';
    position: absolute;
    left: 3px;
    top: 6px
}

.qm-label.qm-checked .qm-checkbox {
    background: #CC0000;
    font-size: 12px;
    line-height: 14px
}

.qm-label.qm-checked .qm-checkbox:after {
    content: '';
    display: block;
    width: 14px;
    height: 14px;
    background: url('check.png');
    background-size: cover;
}

.qm-scroll-bar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 5px
}

.qm-scroll-bar::-webkit-scrollbar-track {
    border-radius: 5px;
    background-color: rgba(102, 102, 102, 0.2)
}

.qm-scroll-bar::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: rgba(153, 153, 153, 0.8)
}

.qm-scroll-bar::-webkit-scrollbar-corner {
    background-color: transparent
}

.qm-radio {
    display: inline-block;
    cursor: pointer;
    font-size: 12px;
    line-height: 14px
}

.qm-radio .qm-radio-input {
    display: none
}

.qm-radio .qm-radio-display {
    width: 14px;
    height: 14px;
    display: block;
    float: left;
    background: #fff;
    border-radius: 100px
}

.qm-radio .qm-radio-value {
    float: left;
    margin-left: 10px
}

.qm-radio .qm-radio-input:checked+.qm-radio-display {
    border: 5px solid #EEEEEE
}

.qm-radio:after {
    clear: both;
    content: '';
    display: table
}

.qm-checkbox {
    display: inline-block;
    cursor: pointer;
    font-size: 12px;
    line-height: 13px
}

.qm-checkbox .qm-checkbox-input {
    display: none
}

.qm-checkbox .qm-checkbox-display {
    width: 14px;
    height: 14px;
    padding: 1px;
    display: block;
    float: left;
    background: #fff;
    border-radius: 2px
}

.qm-checkbox .qm-checkbox-value {
    float: left;
    margin-left: 10px
}

.qm-checkbox .qm-checkbox-input:checked+.qm-checkbox-display {
    background: #EEEEEE
}

.qm-checkbox .qm-checkbox-input:checked+.qm-checkbox-display:before {
    font-family: "qmodel-ff" !important;
    font-size: 12px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: '\e740';
    display: inline;
    color: #fff
}

.qm-checkbox:after {
    clear: both;
    content: '';
    display: table
}

.qm-toolbar {
    box-sizing: border-box;
    border: 1px solid #EEEEEE;
    background-color: rgba(20, 8, 8, 0.66);
    z-index: 8;
    white-space: nowrap
}

.qm-toolbar:after {
    display: table;
    content: '';
    clear: both
}

.qm-toolbar.qm-toolbar-bottom {
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translate(-50%, 0)
}

.qm-toolbar.qm-tree-toolbar {
    position: absolute;
    left: 10px;
    top: 10px
}

.qm-toolbar.qm-toolbar-select {
    position: absolute;
    left: 10px;
    bottom: 10px
}

.qm-toolbar .qm-button {
    margin-left: 0px
}

.qm-toolbar-button {
    position: relative
}

.qm-toolbar-button.qm-button {
    opacity: 1
}

.qm-toolbar-button::before {
    opacity: .6
}

.qm-toolbar-button::after {
    content: '';
    display: block;
    position: absolute;
    border-bottom: 5px solid #fff;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    right: 5px;
    top: 3px
}

.qm-toolbar-button:hover .qm-sub-toolbar {
    display: block
}

.qm-toolbar-button .qm-sub-toolbar {
    display: none;
    background: rgba(20, 8, 8, 0.66);
    position: absolute;
    left: 0;
    bottom: 100%
}

.qm-toolbar-button .qm-sub-toolbar .qm-button {
    display: block;
    margin-left: 0
}

.qm-menu {
    width: 120px;
    position: absolute;
    z-index: 99;
    padding: 5px 0;
    background: rgba(20, 8, 8, 0.66)
}

.qm-menu .qm-sub-menu {
    position: relative
}

.qm-menu .qm-sub-menu>.qm-menu-item {
    padding-right: 26px;
    position: relative
}

.qm-menu .qm-sub-menu>.qm-menu-item:after {
    content: '';
    width: 9px;
    height: 9px;
    border-right: 1px solid #fff;
    border-top: 1px solid #fff;
    transform: rotate(45deg);
    position: absolute;
    right: 12px;
    top: 10px
}

.qm-menu .qm-sub-menu .qm-menu {
    display: none;
    position: absolute;
    top: -5px;
    border: 1px solid #888888
}

.qm-menu .qm-sub-menu:hover .qm-menu {
    display: block
}

.qm-menu .qm-menu-item {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: #fff;
    padding: 0 10px;
    overflow: hidden;
    cursor: pointer;
    user-select: none
}

.qm-menu .qm-menu-item:hover {
    background: #EEEEEE
}

.qm-menu .qm-spacer {
    margin: 5px 0;
    height: 1px;
    background: #4a4a4a
}

.qm-menu-left .qm-menu {
    right: 100%
}

.qm-menu-right .qm-menu {
    left: 100%
}

.qm-disabled {
    color: #888 !important
}

.qm-disabled:hover {
    background: none !important
}

.qm-disabled .qm-menu-item {
    color: #888 !important
}

.qm-disabled .qm-menu-item:hover {
    background: none !important
}

.qm-disabled .qm-menu-item:after {
    border-color: #888 !important
}

.qm-disabled.qm-sub-menu:hover .qm-menu {
    display: none
}

.qm-button {
    color: #87d2f0;
    opacity: 1;
    box-sizing: border-box;
    text-align: center;
    height: 36px;
    padding: 5px;
    line-height: 28px;
    cursor: pointer;
    display: inline-block;
    font-size: 16px;
    vertical-align: top;
    font-family: 'qmodel-ff';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.qm-button-mini {
    color: #87d2f0;
    opacity: 1;
    box-sizing: border-box;
    text-align: center;
    height: 20px;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 18px;
    cursor: pointer;
    display: inline-block;
    font-size: 16px;
    vertical-align: top;
    font-family: 'qmodel-ff';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.qm-button:hover {
    opacity: 1
}

.qm-button.qm-checked {
    background-color: rgba(136, 136, 136, 0.4);
    padding-bottom: 5px;
    border-bottom: 4px solid #888;
    opacity: 1
}

.qm-button .qm-button-name {
    font-size: 16px;
    line-height: 2;
    display: inline-block;
    vertical-align: middle;
    padding: 0 8px;
    white-space: nowrap;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
    box-sizing: border-box
}

.qm-combobox {
    position: relative;
    display: inline-block
}

.qm-combobox:after {
    position: absolute;
    right: 5px;
    top: 5px;
    content: '';
    border-bottom: 5px solid #fff;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    opacity: .6
}

.qm-combobox .qm-current:after {
    display: table;
    content: '';
    clear: both
}

.qm-combobox .qm-button {
    width: 100%
}

.qm-combobox .qm-sub-toolbar {
    width: 100%;
    display: none;
    border: 1px solid #888;
    background-color: rgba(20, 8, 8, 0.66);
    position: absolute;
    left: 0;
    bottom: 100%;
    margin-bottom: 2px
}

.qm-combobox .qm-sub-toolbar .qm-button {
    display: block;
    margin: 0
}

.qm-combobox .qm-button-name {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis
}

.qm-combobox.qm-expand .qm-sub-toolbar {
    display: block
}

.qm-combobox .qm-select {
    font-size: 12px;
    color: #fff;
    text-align: center;
    width: 100px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 30px;
    cursor: pointer
}

.qm-toolbar-select .qm-combobox:after {
    right: 6px;
    top: 50%;
    content: '';
    margin-top: -3px;
    border-bottom: 0;
    border-top: 5px solid #fff;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    opacity: .6;
    transition: all .5s
}

.qm-toolbar-select .qm-combobox.qm-expand:after {
    transform: rotate(180deg)
}

.qm-panel {
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
    color: #FFFFFF;
    position: absolute;
    overflow: hidden;
    user-select: none;
    background-color: rgba(20, 8, 8, 0.66);
    z-index: 9;
    border: 1px solid rgb(145, 25, 17)
}

.qm-panel.qm-has-title {
    padding-top: 40px
}

.qm-panel * {
    box-sizing: border-box
}

.qm-panel .qm-resize {
    height: 8px;
    width: 8px;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 9
}

.qm-panel .qm-resize:after {
    display: block;
    float: right;
    content: '';
    width: 8px;
    height: 8px;
    cursor: nw-resize
}

.qm-panel .qm-title {
    height: 40px;
    padding: 10px 30px 10px 10px;
    line-height: 20px;
    font-size: 14px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: -moz-none;
    border-bottom: 1px solid rgb(16, 16, 16);
    margin-top: -40px;
    background-color: rgba(16, 16, 16, 0.66);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.qm-panel .qm-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    z-index: 99
}

.qm-panel .qm-close:after,
.qm-panel .qm-close:before {
    content: '';
    display: block;
    width: 16px;
    height: 1px;
    background-color: #FFFFFF;
    position: absolute;
    margin-top: 8px
}

.qm-panel .qm-close:before {
    transform: rotate(45deg)
}

.qm-panel .qm-close:after {
    transform: rotate(-45deg)
}

.qm-panel .qm-panel-body {
    width: 100%;
    height: 100%;
    overflow: hidden
}

.qm-panel .qm-panel-container {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    position: relative
}

.qm-panel .qm-panel-tips {
    font-size: 12px;
    margin-top: 36px;
    text-align: center;
    color: #ece2e2
}

.qm-panel .qm-panel-tips:before {
    font-size: 20px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    vertical-align: middle;
    margin-right: 2px;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg)
    }
    100% {
        transform: rotate(360deg)
    }
}

.qm-panel .qm-panel-loading {
    font-size: 12px;
    margin-top: 36px;
    text-align: center;
    color: #ece2e2
}

.qm-panel .qm-panel-loading:before {
    display: inline-block;
    content: '';
    margin-right: 5px;
    vertical-align: text-bottom;
    width: 16px;
    height: 16px;
    border: 2px solid #c1c1c1;
    border-top-color: transparent;
    border-radius: 100%;
    animation: rotate 1s linear infinite
}

.qm-panel .qm-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px
}

.qm-panel .qm-table .qm-group-title {
    background-color: rgba(36, 36, 36, 0.45)
}

.qm-panel .qm-table .qm-group-title td {
    color: #FFFFFF;
    border-bottom: 1px solid #EEEEEE
}

.qm-panel .qm-table .qm-key {
    color: #FFFFFF;
    padding-left: 26px;
    width: 40%
}

.qm-panel .qm-table .qm-value {
    color: #FFFFFF
}

.qm-panel .qm-table td {
    vertical-align: middle;
    line-height: 20px;
    padding: 5px;
    border: 1px solid #EEEEEE
}

.qm-panel .qm-foot {
    font-size: 12px
}

.qm-panel .qm-allLayers {
    margin: 2px 10px 3px;
    border-bottom: 1px solid #ece2e2;
    height: 27px
}

.qm-panel .qm-allLayers .eyes {
    top: 1px;
    position: relative;
    width: 16px;
    height: 12px;
    display: inline-block;
    font-size: 20px
}

.qm-panel .qm-allLayers .name {
    text-overflow: ellipsis;
    font-size: 12px;
    position: absolute;
    top: 5px;
    left: 0;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    padding-left: 35px
}

.qm-panel .qm-layers {
    width: 100%;
    padding-left: 10px;
    margin: 0;
    height: 100%;
    padding-bottom: 40px;
    overflow-y: auto
}

.qm-panel .qm-layers li {
    list-style: none;
    height: 25px;
    line-height: 25px;
    position: relative
}

.qm-panel .qm-layers li span {
    display: inline-block
}

.qm-panel .qm-layers .color {
    width: 13px;
    height: 13px;
    margin: 0 9px;
    display: inline-block
}

.qm-panel .qm-layers .eyes {
    top: 1px;
    position: relative;
    width: 16px;
    height: 12px;
    display: inline-block;
    font-size: 20px
}

.qm-panel .qm-layers .name {
    text-overflow: ellipsis;
    font-size: 12px;
    position: absolute;
    top: 0;
    left: 0;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    padding-left: 50px
}

.qm-panel .disable .color {
    background: transparent !important;
    border: 1px solid #888
}

.qm-panel .disable .name {
    color: #888
}

.qm-property-tab {
    margin: 0 10px 2px
}

.qm-property-tab .qm-tabs-list {
    display: table;
    width: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0
}

.qm-property-tab .qm-tabs-list .qm-tabs-option {
    display: table-cell;
    text-align: center;
    height: 38px;
    vertical-align: middle;
    border-bottom: 3px solid #888
}

.qm-property-tab .qm-tabs-list .qm-tabs-option.active {
    border-color: #EEEEEE
}

.layers-panel .qm-panel-container {
    overflow-y: hidden
}

@media screen and (min-width:415px) {
    .qm-mobile .tree-panel,
    .qm-mobile .property-panel,
    .qm-mobile .layers-panel,
    .qm-mobile .area-panel {
        max-width: 373px;
        max-height: 600px
    }
    .qm-mobile .view-panel {
        max-width: 373px;
        max-height: 467px
    }
    .qm-mobile .view-panel .qm-panel-container {
        padding-bottom: 60px
    }
    .qm-mobile .layers-panel {
        font-size: 38px
    }
    .qm-mobile .layers-panel .color {
        font-size: 1.1em;
        margin: .25em .3em 0 .3em
    }
    .qm-mobile .layers-panel .name {
        padding-left: 5em
    }
    .qm-mobile .layers-panel .eyes {
        top: .1em
    }
}

.qm-tree {
    width: 100%;
    font-size: 12px;
    line-height: 1.83
}

.qm-tree .qm-tree-node {
    position: relative;
    padding-left: 26px;
    white-space: nowrap
}

.qm-tree .qm-tree-node .qm-icon {
    position: absolute;
    left: 5px
}

.qm-tree .qm-tree-node .qm-tree-name {
    cursor: pointer;
    margin-left: 2px;
    padding: 0 3px;
    white-space: nowrap
}

.qm-tree .qm-tree-node .qm-tree-name.qm-selected {
    background: #EEEEEE;
    color: #fff
}

.qm-tree .qm-tree-node .qm-tree-name.qm-disabled {
    color: #ece2e2
}

.qm-tree .qm-tree-node:after {
    display: table;
    clear: both;
    content: ''
}

.qm-tree-empty {
    width: 100%;
    font-size: 12px;
    line-height: 1.83
}

.qm-tree-drawing .qm-tree-node {
    padding-left: 0
}

.qm-collapse+.qm-sub-tree {
    display: none
}

.qm-sub-tree {
    padding-left: 16px
}

.qm-setting {
    font-size: 12px
}

.qm-setting .qm-radio {
    display: block;
    float: left;
    width: 50%;
    padding: 8px 0
}

.qm-setting .qm-checkbox {
    display: block;
    padding: 8px 0
}

.qm-setting .icon-checked,
.qm-setting .icon-nochecked {
    margin-left: 10px;
    position: relative;
    top: 2px;
    margin-right: 5px
}

.qm-setting .icon-checked {
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 5px solid #EEEEEE;
    background: #fff
}

.qm-setting .icon-nochecked {
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #fff
}

.qm-setting .qm-input-range {
    margin: 0;
    background: linear-gradient(to right, #4a90e2 0, #4a90e2 19.6429%, #ece2e2 19.6429%, #ece2e2 100%)
}

.qm-setting .qm-setting-tab-default,
.qm-setting .qm-setting-tab-effect {
    padding: 0;
    margin: 0;
    list-style: none;
    display: none
}

.qm-setting .qm-setting-tab-default.qm-show,
.qm-setting .qm-setting-tab-effect.qm-show {
    display: block
}

.qm-setting .qm-setting-li {
    background-color: rgba(85, 85, 85, 0.45);
    margin-top: 2px;
    padding: 10px 0
}

.qm-setting .qm-setting-li:after {
    content: '';
    display: table;
    clear: both
}

.qm-setting .qm-setting-margin {
    margin-top: 0
}

.qm-setting .qm-setting-select {
    padding: 15px 0
}

.qm-setting .qm-setting-select #modeSelect {
    float: left;
    margin-left: 8px
}

.qm-setting .qm-setting-select:after {
    content: '';
    display: table;
    clear: both
}

.qm-setting .qm-setting-name {
    line-height: 30px;
    display: block;
    float: left;
    width: 92px;
    text-align: right
}

.qm-setting .qm-setting-value {
    float: left;
    height: 30px;
    width: 200px;
    margin-left: 8px
}

.qm-setting .qm-setting-range {
    height: 46px;
    margin: -10px 0
}

.qm-setting .qm-color {
    float: left;
    margin-left: 8px
}

.qm-setting .qm-color:after {
    content: '';
    display: table;
    clear: both
}

.qm-setting .qm-color .qm-color-item {
    display: block;
    width: 30px;
    height: 26px;
    float: left;
    cursor: pointer;
    margin-right: 3px;
    position: relative;
    padding: 2px
}

.qm-setting .qm-color .qm-color-item .qm-color-node {
    display: block;
    width: 26px;
    height: 22px
}

.qm-setting .qm-color .qm-color-item.qm-color-select .qm-color-node:after {
    position: absolute;
    display: block;
    content: '';
    width: 100%;
    height: 100%;
    border: 1px solid #EEEEEE;
    left: 0;
    top: 0;
    box-sizing: border-box
}

.qm-setting .qm-select {
    width: 200px;
    height: 30px;
    display: block;
    position: relative
}

.qm-setting .qm-select.qm-select-open .qm-select-current {
    border-color: #EEEEEE
}

.qm-setting .qm-select.qm-select-open .qm-select-options {
    display: block
}

.qm-setting .qm-select .qm-select-current,
.qm-setting .qm-select .qm-select-option {
    width: 100%;
    height: 100%;
    font-size: 12px;
    color: #FFFFFF;
    padding: 0 10px;
    line-height: 28px
}

.qm-setting .qm-select .qm-select-current {
    border: solid 1px #EEEEEE;
    position: relative;
    padding-right: 28px
}

.qm-setting .qm-select .qm-select-current::after {
    content: '';
    display: block;
    position: absolute;
    top: 11px;
    right: 10px;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid rgba(255, 255, 255, 0.6)
}

.qm-setting .qm-select .qm-select-options {
    display: none;
    position: absolute;
    width: 100%;
    border: 1px solid #888;
    background: #3f3f3f;
    z-index: 9
}

.qm-setting .qm-thumbnail {
    background: rgba(85, 85, 85, 0.45)
}

.qm-setting .qm-thumbnail .qm-setting-name {
    padding: 15px 0 0
}

.qm-setting .qm-thumbnail:after {
    display: table;
    content: '';
    clear: both
}

.qm-setting .qm-thumbnail-value {
    float: left;
    margin-left: 8px;
    padding: 18px 0 0;
    border-top: 1px solid #888
}

.qm-setting .qm-thumbnail-value:after {
    display: table;
    content: '';
    clear: both
}

.qm-setting .qm-thumbnail-value .qm-thumbnail-item {
    width: 30px;
    height: 26px;
    margin-right: 3px;
    padding: 2px;
    float: left
}

.qm-setting .qm-thumbnail-value .qm-thumbnail-item.selected {
    padding: 1px;
    border: 1px solid #EEEEEE
}

.qm-setting .qm-thumbnail-value .qm-thumbnail-item img {
    display: block;
    width: 26px;
    height: 22px
}

.qm-setting-tabs {
    padding-left: 10px;
    padding-right: 10px
}

.qm-setting-tabs .qm-tabs-list {
    margin: 0;
    padding: 0;
    list-style: none
}

.qm-setting-tabs .qm-tabs-option {
    width: 154px;
    height: 40px;
    font-family: MicrosoftYaHei;
    font-size: 12px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    float: left;
    cursor: pointer;
    border-bottom: 3px solid #ece2e2
}

.qm-setting-tabs .qm-tabs-option.active {
    border-bottom: 3px solid #EEEEEE;
    color: #EEEEEE
}

.qm-li-more {
    text-align: right
}

.qm-panel-more {
    color: #ece2e2;
    height: 40px;
    padding: 12px;
    line-height: 16px;
    vertical-align: middle
}

.qm-arrow {
    display: inline-block;
    vertical-align: middle;
    width: 16px;
    height: 16px
}

.qm-arrow:after {
    content: '';
    display: block;
    margin: 3px auto;
    transition: all .5s;
    border-left: 1px solid #fff;
    border-top: 1px solid #fff;
    width: 6px;
    height: 6px;
    transform: rotate(-135deg)
}

.qm-show-more .qm-arrow:after {
    transform: rotate(45deg)
}

.qm-panel-container .qm-setting-tab-effect {
    height: 280px;
    overflow: auto
}

.qm-panel-container .qm-setting-tab-effect .qm-setting-disabled {
    color: #888
}

.qm-panel-container .qm-setting-tab-effect .qm-setting-disabled img {
    opacity: .5
}

.qm-panel-container .qm-setting-tab-effect .qm-setting-disabled .qm-checkbox-display {
    background: #888
}

.qm-panel-container .qm-setting-tab-effect .qm-more-list {
    display: none
}

.qm-panel-container .qm-setting-tab-effect .qm-more-list.qm-show {
    display: block
}

.qm-setting-foot {
    font-size: 12px
}

.qm-setting-foot .qm-reset {
    text-align: right;
    padding-right: 14px;
    color: #EEEEEE;
    line-height: 40px;
    cursor: pointer
}

.qm-drawing-wrap .qm-setting .qm-setting-tab-default {
    height: 144px;
    padding-top: 1px
}

.qm-drawing-wrap .qm-setting .qm-setting-value {
    height: auto
}

.qm-drawing-wrap .qm-setting .qm-radio {
    float: none
}

.qm-drawing-wrap .qm-setting .qm-setting-li {
    padding: 0
}

.qm-drawing-wrap .qm-setting .lineWidth .qm-setting-value {
    height: 35px
}

.qm-walk-panel {
    padding: 4px 0;
    line-height: 40px;
    border: solid 1px #EEEEEE;
    background: rgba(20, 8, 8, 0.712);
    transform: translate(-50%, 0)
}

.qm-walk-panel .qm-panel-body {
    overflow: visible
}

.qm-walk-panel .qm-panel-container {
    overflow-y: hidden
}

.qm-walk-panel .qm-walk-button {
    border-right: 1px solid #888;
    float: left
}

.qm-walk-panel .qm-button {
    width: 40px;
    height: 40px;
    margin: 0 4px;
    padding: 4px
}

.qm-walk-panel .active {
    background: #979797;
    padding: 4px;
    border-radius: 3px;
    background: rgba(97, 97, 97, 0.6);
    opacity: 1
}

.qm-walk-panel .qm-walk-exit {
    display: inline-block;
    width: 60px;
    height: 30px;
    border-radius: 3px;
    border: solid 1px #979797;
    text-align: center;
    line-height: 28px;
    margin-left: 10px
}

.qm-walk-panel .qm-close {
    display: none
}

.qm-walk-gravity {
    width: 76px;
    height: 40px;
    margin: 0 10px;
    border-left: 1px solid #888;
    border-right: 1px solid #888;
    padding: 13px 12px;
    vertical-align: middle;
    float: left
}

.qm-walk-gravity .qm-checkbox-value {
    margin-left: 9px
}

.qm-person {
    font-size: 12px
}

.qm-person .person-btns .speedBtn {
    display: block;
    text-align: center;
    line-height: 22px;
    margin: 8px 0;
    float: left
}

.qm-person .person-btns .qmd-add,
.qm-person .person-btns .qmd-minus {
    width: 24px;
    height: 24px;
    font-size: 14px;
    border-radius: 3px;
    cursor: pointer;
    border: solid 1px #ece2e2;
}

.qm-person .person-btns .speedNum {
    display: block;
    width: 56px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    float: left;
    margin: 12px 0
}

.qm-walk-speed {
    padding: 0 14px;
    float: left;
    border-right: 1px solid #888
}

.qm-walk-speed .qm-walk-name {
    display: block;
    float: left;
    padding: 5px 0;
    height: 43px;
    line-height: 30px
}

.qm-walk-speed .speedBtn {
    display: block;
    text-align: center;
    line-height: 22px;
    margin: 8px 0;
    float: left
}

.qm-walk-speed .qmd-add,
.qm-walk-speed .qmd-minus {
    width: 24px;
    height: 24px;
    font-size: 14px;
    border-radius: 3px;
    cursor: pointer;
    border: solid 1px #ece2e2;
}

.qm-walk-speed .speedNum {
    display: block;
    width: 56px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    float: left;
    margin: 12px 0
}

.qm-walk-disabled {
    color: #888888
}

.qm-walk-disabled .qm-checkbox-display {
    background: #444
}

.qm-walk-label {
    width: 70px;
    height: 40px;
    border-right: 1px solid #888;
    padding: 13px 10px;
    vertical-align: middle;
    float: left
}

.qm-mobile * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.qm-mobile .qm-quit {
    padding-right: .4em;
    box-sizing: border-box;
    width: 2.7em;
    height: .85em;
    position: absolute;
    color: #fff;
    top: 0;
    right: 0;
    background-color: rgba(17, 17, 17, 0.7)
}

.qm-mobile .qm-quit .qm-walk-exit {
    font-size: .35em;
    display: inline-block;
    height: 100%;
    width: 100%;
    line-height: 2.5em;
    text-align: center;
    float: left
}

.qm-mobile .qm-quit .close {
    position: absolute;
    right: .7em;
    top: .24em
}

.qm-mobile .qm-quit .close:after,
.qm-mobile .qm-quit .close:before {
    content: '';
    display: block;
    width: .4em;
    height: 1px;
    background-color: #fff;
    position: absolute;
    margin-top: 8px
}

.qm-mobile .qm-quit .close:before {
    transform: rotate(45deg)
}

.qm-mobile .qm-quit .close:after {
    transform: rotate(-45deg)
}

.qm-mobile .qm-walk-panel {
    padding: .6em .2em;
    transform: none;
    background: transparent;
    overflow: visible
}

.qm-mobile .qm-walk-panel .ball {
    position: absolute;
    height: 1.8em;
    top: .7em;
    left: .7em;
    width: 1.8em;
    background-color: rgba(255, 255, 255, 0.2);
    border: solid .1em #fff;
    border-radius: 100%
}

.qm-mobile .qm-walk-panel .qm-walk-gravity {
    border-top: 1px solid #888;
    border-left: none;
    border-right: none;
    display: block;
    float: none;
    width: 10.2em;
    height: 3.4em;
    margin: 0 .4em;
    padding: .8em 1em
}

.qm-mobile .qm-walk-panel .qm-walk-gravity .qm-checkbox-value {
    font-size: 1.13em
}

.qm-mobile .qm-walk-panel .arrow-up {
    padding-top: .3em
}

.qm-mobile .qm-walk-panel .arrow-down {
    padding-top: .5em
}

.qm-mobile .qm-walk-panel .person-btns {
    position: relative
}

.qm-mobile .qm-walk-panel .jump {
    position: absolute;
    right: 0;
    top: 0;
    width: 4.3em;
    height: 5em;
    margin: .8em 0;
    border-left: 1px solid #888
}

.qm-mobile .qm-walk-panel .jump span {
    display: block;
    width: 100%;
    height: 50%;
    font-size: 1.5em;
    opacity: 1;
    line-height: 1em
}

.qm-mobile .qm-walk-panel .speedBtn {
    border: none;
    margin-bottom: 0;
    float: none;
    display: inline-block
}

.qm-mobile .qm-walk-panel .qm-panel-container {
    overflow: visible;
    background-color: rgba(17, 17, 17, 0.7);
    width: 4.5em;
    height: 2em;
    position: absolute;
    right: .2em
}

.qm-mobile .qm-walk-panel .controllerPanel {
    position: absolute;
    left: .2em;
    top: 0;
    width: 3.2em;
    height: 3.2em;
    border-radius: 100%;
    background-color: rgba(17, 17, 17, 0.5)
}

.qm-mobile .qm-walk-panel .qm-person {
    font-size: .3em;
    padding-left: .3em
}

.qm-mobile .qm-walk-panel .speedNum {
    float: none;
    display: inline-block;
    width: 3.5em;
    height: 1em;
    line-height: 1.5em;
    font-size: 1.14em;
    margin: .95em 0
}

.qm-mobile .qm-walk-panel .qmd-minus {
    margin-left: .6em
}

.qm-mobile .qm-walk-panel .qmd-add,
.qm-mobile .qm-walk-panel .qmd-minus {
    height: 2.3em;
    font-size: 1em;
    width: 2.4em
}

.qm-mobile .qm-walk-panel .qm-checkbox-display {
    width: 1.3em;
    height: 1.3em;
    border-radius: .3em;
    padding: .1em .2em
}

.qm-mobile .qm-walk-panel .qm-checkbox-value {
    margin-left: 2em;
    font-size: 1.03em;
    line-height: 1.15em
}

input[type=range] {
    -webkit-appearance: none;
    width: 100%;
    border-radius: 6px;
    border: none
}

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none
}

input[type=range]::-webkit-slider-runnable-track {
    height: 6px;
    border-radius: 6px
}

input[type=range]::-moz-range-progress {
    height: 4px;
    border-radius: 2px;
    background: #4a90e2
}

input[type=range]:focus {
    outline: none
}

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 14px;
    width: 14px;
    margin-top: -5px;
    background: #fff;
    border-radius: 10px
}

.qm-range {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 0 10px;
    font-size: 12px;
    box-sizing: border-box;
    line-height: 25px
}

.qm-range:focus {
    outline: none
}

.qm-range .qm-input-range {
    width: 100%;
    padding: 0;
    height: 6px
}

.qm-range .qm-range-min,
.qm-range .qm-range-cur,
.qm-range .qm-range-max {
    position: absolute;
    bottom: 0;
    color: #ece2e2
}

.qm-range .qm-range-min {
    left: 10px
}

.qm-range .qm-range-cur {
    left: 50%;
    transform: translate(-50%, 0);
    color: #fff
}

.qm-range .qm-range-max {
    right: 10px;
    text-align: right
}

.qm-multiple-range {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 6px;
    background: #ccc;
    border-radius: 5px;
    margin: 5px 0
}

.qm-range-track {
    position: absolute;
    height: 100%
}

.qm-slider {
    box-sizing: border-box;
    position: absolute;
    width: 12px;
    height: 12px;
    background: #ddd;
    border: 1px solid #979797;
    border-radius: 100px;
    top: -3px;
    cursor: pointer
}

.qm-slider-max {
    transform: translate(-100%, 0)
}

.qm-info {
    padding: 0 10px;
    font-size: 14px;
    line-height: 24px
}

.qm-info .qm-info-list {
    padding: 20px 5px;
    margin: 0;
    height: 128px;
    list-style: none;
    border-bottom: 1px solid #888
}

.qm-info .qm-info-list li {
    font-size: 12px;
    color: #FFFFFF;
    line-height: 2.17
}

.qm-info .qm-info-power {
    font-size: 14px;
    text-align: right;
    height: 40px;
    line-height: 40px
}

.qm-info .qm-info-power a {
    color: #EEEEEE;
    text-decoration: none
}

.qm-mobile .qm-panel {
    border: 0
}

.qm-mobile .qm-toolbar.qm-toolbar-select {
    left: 0;
    top: .4em;
    bottom: inherit;
    width: 4.03em
}

.qm-mobile .qm-toolbar.qm-toolbar-select .qm-button {
    padding: 0 0 0 .3em;
    width: 100%;
    font-size: 1em;
    height: 1em;
    opacity: 1;
    text-align: left
}

.qm-mobile .qm-toolbar.qm-toolbar-select .qm-button-name {
    font-size: .4em;
    line-height: 2.5em;
    width: 6.7em;
    text-align: left
}

.qm-mobile .qm-toolbar.qm-toolbar-select .qm-sub-toolbar {
    right: inherit;
    top: 1em
}

.qm-mobile .qm-toolbar.qm-toolbar-select:after {
    border-top: .14em solid #fff;
    border-left: .14em solid transparent;
    border-right: .14em solid transparent;
    position: absolute;
    opacity: .6;
    right: .23em;
    top: .45em
}

.qm-drawing-wrap .qm-sub-toolbar {
    max-height: 800px;
    overflow-y: auto;
    overflow-x: hidden
}

.qm-drawing-wrap.qm-mobile .qm-expand .qm-current .qm-button,
.qm-mobile-dwg .qm-expand .qm-current .qm-button {
    border-right: 1px solid #888
}

.qm-drawing-wrap.qm-mobile .qm-sub-toolbar::-webkit-scrollbar-track-piece,
.qm-mobile-dwg .qm-sub-toolbar::-webkit-scrollbar-track-piece {
    background-color: rgba(0, 0, 0, 0);
    border-left: 1px solid rgba(0, 0, 0, 0)
}

.qm-drawing-wrap.qm-mobile .qm-sub-toolbar::-webkit-scrollbar,
.qm-mobile-dwg .qm-sub-toolbar::-webkit-scrollbar {
    width: 5px;
    height: 13px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px
}

.qm-drawing-wrap.qm-mobile .qm-sub-toolbar::-webkit-scrollbar-thumb,
.qm-mobile-dwg .qm-sub-toolbar::-webkit-scrollbar-thumb {
    background-color: rgba(153, 153, 153, 0.8);
    background-clip: padding-box;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    min-height: 28px
}

.qm-drawing-wrap.qm-mobile .qm-sub-toolbar::-webkit-scrollbar-thumb:hover,
.qm-mobile-dwg .qm-sub-toolbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select .qm-button,
.qm-mobile-dwg .qm-toolbar-select .qm-button {
    line-height: 1em !important;
    padding-left: 0;
    overflow: hidden
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select .qm-button .qm-button-name,
.qm-mobile-dwg .qm-toolbar-select .qm-button .qm-button-name {
    padding: 0 0 0 1em;
    width: 100%;
    box-sizing: border-box
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select .qm-current .qm-button-name,
.qm-mobile-dwg .qm-toolbar-select .qm-current .qm-button-name {
    padding: 0 1em
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select .qm-current .qm-button,
.qm-mobile-dwg .qm-toolbar-select .qm-current .qm-button {
    margin-left: 0
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select .qm-sub-toolbar,
.qm-mobile-dwg .qm-toolbar-select .qm-sub-toolbar {
    max-height: 6em;
    overflow: auto;
    border: 1px solid #888;
    box-sizing: border-box
}

.qm-drawing-wrap.qm-mobile .measure-panel .qm-panel-container,
.qm-mobile-dwg .measure-panel .qm-panel-container {
    border: 1px solid #888;
    left: .14em
}

.qm-drawing-wrap.qm-mobile .measure-panel .qm-measure-distance,
.qm-mobile-dwg .measure-panel .qm-measure-distance {
    border-bottom: 0
}

.qm-drawing-wrap.qm-mobile .measure-panel .qm-close,
.qm-mobile-dwg .measure-panel .qm-close {
    color: #fff;
    border: 1px solid #888
}

.qm-drawing-wrap.qm-mobile .measure-panel .qm-measure-info,
.qm-mobile-dwg .measure-panel .qm-measure-info {
    line-height: 1em
}

.qm-drawing-wrap.qm-mobile .measure-panel .qm-measure-distance,
.qm-mobile-dwg .measure-panel .qm-measure-distance {
    height: 2.4em;
    line-height: 2.4em
}

.qm-drawing-wrap.qm-mobile .measure-panel .qm-measure-x,
.qm-mobile-dwg .measure-panel .qm-measure-x,
.qm-drawing-wrap.qm-mobile .measure-panel .qm-measure-y,
.qm-mobile-dwg .measure-panel .qm-measure-y {
    padding-left: 1em
}

.qm-mobile .qmd-tree {
    margin-left: 0;
    line-height: 1.7em;
    background: url("tree.png");
}

.qm-mobile .qm-button {
    width: 2em;
    height: 2em;
    padding-top: .2em;
    font-size: .5em;
    display: block
}

.qm-mobile .qm-button.qm-checked {
    border: 0;
    color: #EEEEEE
}

.qm-mobile .qm-combobox {
    display: block
}

.qm-mobile .qm-combobox:after {
    display: none
}

.qm-mobile .qm-combobox .qm-sub-toolbar {
    left: inherit;
    right: 1.05em;
    bottom: inherit
}

.qm-mobile .qm-combobox .qm-sub-toolbar .qm-button {
    background-color: rgba(20, 8, 8, 0.66);
    opacity: 1;
    padding: 0;
    font-size: .45em;
    width: 2.2em;
    height: 2.2em;
    line-height: 2.2em
}

.qm-mobile .qm-expand .qm-current .qm-button {
    background-color: #111;
    color: #EEEEEE
}

.qm-mobile-rfa .qm-toolbar .qm-button-name,
.qm-mobile-dwg .qm-toolbar .qm-button-name {
    display: block;
    padding: 0 1em
}

.qm-mobile-rfa .qm-toolbar .qm-current .qm-button,
.qm-mobile-dwg .qm-toolbar .qm-current .qm-button {
    padding: 0
}

.qm-mobile-rfa .qm-toolbar .qm-current .qm-button-name,
.qm-mobile-dwg .qm-toolbar .qm-current .qm-button-name {
    padding: 0 1em
}

.qm-mobile-rfa .qm-toolbar .qm-sub-toolbar,
.qm-mobile-dwg .qm-toolbar .qm-sub-toolbar {
    max-height: 9em;
    overflow: auto
}

.qm-mobile-rfa .qm-toolbar .qm-sub-toolbar .qm-button,
.qm-mobile-dwg .qm-toolbar .qm-sub-toolbar .qm-button {
    width: 100%;
    font-size: 1em;
    height: 1em
}

.qm-mobile-rfa .qm-family {
    width: 100%
}

.qm-mobile-rfa .qm-sub-toolbar .qm-button {
    padding: 0 0 0 .1em !important
}

.qm-mobile-rfa .qm-current .qm-button {
    padding: 0 0 0 .1em !important
}

.qm-mobile-rfa .qm-button-name {
    padding: 0 !important;
    width: 7.8em !important
}

.qm-mobile-rfa .qm-toolbar.qm-toolbar-select {
    width: 4em
}

.qm-mobile .qm-panel-tips {
    font-size: .3em
}

.qm-mobile .qm-panel-tips:before {
    font-size: 1em
}

.qm-mobile .view-panel .qm-title {
    font-size: .35em !important;
    padding: 0 0 0 2.9em !important
}

.qm-mobile .view-panel .qm-close {
    font-size: .75em !important;
    left: .7em !important;
    overflow: hidden
}

.qm-mobile .view-panel .qm-close:before {
    margin-left: -0.29em
}

.qm-mobile .view-panel .qm-close:after {
    margin-left: -0.29em
}

.qm-mobile .area-panel .qm-panel-container {
    padding-bottom: 2em
}

.qm-mobile .property-panel,
.qm-mobile .area-panel {
    padding-top: 0;
    font-size: .85em
}

.qm-mobile .property-panel .qm-panel-body,
.qm-mobile .area-panel .qm-panel-body {
    padding-top: 0 !important
}

.qm-mobile .property-panel .qm-table,
.qm-mobile .area-panel .qm-table {
    font-size: inherit
}

.qm-mobile .property-panel .qm-table td,
.qm-mobile .area-panel .qm-table td {
    line-height: 2.1em;
    font-size: .4em
}

.qm-mobile .property-panel .qm-table .qm-key,
.qm-mobile .area-panel .qm-table .qm-key {
    padding-left: 2em;
    width: 33%
}

.qm-mobile .property-panel .qm-icon,
.qm-mobile .area-panel .qm-icon {
    padding-left: .7em;
    padding-right: .6em;
    padding-top: .88em
}

.qm-mobile .property-panel .qm-icon:before,
.qm-mobile .area-panel .qm-icon:before {
    border-right: .5em solid #ece2e2;
    border-top: .5em solid transparent
}

.qm-mobile .property-panel .qm-value,
.qm-mobile .area-panel .qm-value {
    padding: 0;
    padding-left: 1em
}

.qm-mobile .property-panel .qm-panel-header,
.qm-mobile .area-panel .qm-panel-header {
    border-bottom: 1px solid #888;
    padding-top: 0 !important
}

.qm-mobile .property-panel .qm-panel-header .qm-property-tab,
.qm-mobile .area-panel .qm-panel-header .qm-property-tab {
    height: 1.5em;
    margin: 0 .3em;
    padding: .3em 0;
    border-bottom: 0;
    margin-top: 0 !important
}

.qm-mobile .property-panel .qm-panel-header .qm-property-tab .qm-tabs-list,
.qm-mobile .area-panel .qm-panel-header .qm-property-tab .qm-tabs-list {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-size: .37em;
    border: 1px solid #888;
    border-radius: 5px;
    position: relative
}

.qm-mobile .property-panel .qm-panel-header .qm-property-tab .qm-tabs-list .qm-tabs-option,
.qm-mobile .area-panel .qm-panel-header .qm-property-tab .qm-tabs-list .qm-tabs-option {
    font-size: 1.1em;
    float: left;
    list-style: none;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 2.1em;
    height: 100%;
    border: 0 !important
}

.qm-mobile .property-panel .qm-panel-header .qm-property-tab .qm-tabs-list .active,
.qm-mobile .area-panel .qm-panel-header .qm-property-tab .qm-tabs-list .active {
    background: #EEEEEE
}

.qm-mobile .qm-panel .qm-close:after,
.qm-mobile .qm-panel .qm-close:before {
    width: 16px;
    height: 1px;
    margin-top: .3em
}

.qm-mobile .layers-panel {
    padding-top: 0
}

.qm-mobile .layers-panel .qm-allLayers {
    height: 1em;
    line-height: 1em
}

.qm-mobile .layers-panel .qm-allLayers .eyes {
    float: left;
    font-size: .65em;
    top: 0;
    width: 1em;
    height: 1em
}

.qm-mobile .layers-panel .qm-allLayers .name {
    font-size: .38em;
    padding-left: 3em;
    top: 0
}

.qm-mobile .layers-panel .qm-layers {
    margin: 0;
    padding-left: .3em
}

.qm-mobile .layers-panel .qm-layers .name {
    font-size: .38em;
    padding-left: 4em
}

.qm-mobile .layers-panel .qm-layers li {
    height: 1em;
    line-height: 1em
}

.qm-mobile .layers-panel .qm-layers li .eyes {
    float: left;
    font-size: .65em;
    top: 0;
    width: 1em;
    height: 1em
}

.qm-mobile .layers-panel .qm-layers li .color {
    width: .4em;
    float: left;
    height: .4em;
    margin: .3em .3em 0 .19em;
    display: inline-block
}

.qm-drawing-wrap.qm-mobile .qm-panel-container,
.qm-mobile-dwg .qm-panel-container {
    padding-bottom: 1.6em
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select,
.qm-mobile-dwg .qm-toolbar-select {
    width: 4em
}

.qm-drawing-wrap.qm-mobile .qm-toolbar-select .qm-family,
.qm-mobile-dwg .qm-toolbar-select .qm-family {
    width: 4em
}

.qm-drawing-wrap.qm-mobile .qm-scroll-bar .qm-current .qm-button-name {
    font-size: .375em
}

.qm-drawing-wrap.qm-mobile .qm-scroll-bar .qm-button {
    width: 100%
}

.qm-drawing-wrap.qm-mobile .qm-scroll-bar .qm-button .qm-button-name {
    height: 100%;
    font-size: .85em
}

.tree-panel .qm-panel-container,
.property-panel .qm-panel-container {
    padding-bottom: 3.2em
}

.qm-mobile .qm-loading {
    font-size: .2em;
    z-index: ece2e2
}

.qm-mobile .qm-loading .qm-loading-gif {
    font-size: 1.1em;
    width: 3.375em;
    height: 2.7em;
    background: url(http://www.qmodel.cn/css/loading.gif) no-repeat 0 0;
    background-size: 100% 100%
}

.qm-mobile.qm-drawing-wrap .qm-loading-text {
    font-size: 1em;
    color: #fff;
    margin-top: .5em
}

.qm-mobile .qmd-view,
.qm-mobile .qmd-layers,
.qm-mobile .qmd-home,
.qm-mobile .qmd-properties,
.qm-mobile .qmd-measure,
.qm-mobile .qmd-sectionbox,
.qm-mobile .qmd-firstperson {
    padding: 0;
    margin: 0;
    line-height: 2em
}

.qm-mobile .qmd-view:before,
.qm-mobile .qmd-layers:before,
.qm-mobile .qmd-home:before,
.qm-mobile .qmd-properties:before,
.qm-mobile .qmd-measure:before,
.qm-mobile .qmd-sectionbox:before,
.qm-mobile .qmd-firstperson:before {
    font-size: 1.5em
}

.qm-mobile .qm-tree-drawing {
    font-size: .25em
}

.qm-mobile .qm-tree-drawing .qm-tree-name {
    font-size: 1.7em !important
}

.qm-mobile .qm-tree-empty:nth-child(1) {
    font-size: .9em;
    padding-left: 0
}

.qm-mobile .qm-tree-empty {
    font-size: 1em;
    line-height: normal
}

.qm-mobile .qm-tree-empty .qm-sub-tree {
    padding-left: .4em
}

.qm-mobile .qm-tree-empty .qm-tree {
    line-height: 1em;
    padding-left: .1em
}

.qm-mobile .qm-tree-empty .qm-tree .qm-tree-node {
    padding-left: 1.5em
}

.qm-mobile .qm-tree-empty .qm-tree .qm-tree-node .qm-icon {
    top: .7em;
    margin-right: 0;
    left: 0
}

.qm-mobile .qm-tree-empty .qm-tree .qm-tree-node .qm-tree-name {
    margin-left: 0
}

.qm-mobile .qm-tree-empty .qm-tree .qm-tree-name {
    font-size: .85em !important
}

.qm-mobile .qm-tree-empty .qm-tree-name {
    font-size: .9em
}

.qm-mobile .qm-tree-empty .qm-tree-node {
    font-size: .5em;
    line-height: 2.35em;
    border-bottom: .05em solid #888 !important
}

.qm-mobile .qm-tree-empty .qm-tree-node .qm-icon {
    left: 0;
    margin-right: 0;
    font-size: .8em;
    top: .9em
}

.qm-mobile .qm-tree-empty .qm-icon {
    padding: .3em
}

.qm-mobile .qm-tree {
    width: 100%;
    line-height: 3.6em;
    padding-left: 1em;
    font-size: .3em
}

.qm-mobile .qm-tree .qm-tree-node {
    position: relative;
    padding-left: 2em;
    border-bottom: .05em solid #888
}

.qm-mobile .qm-tree .qm-tree-node .qm-icon {
    left: -0.2em;
    top: 1.2em;
    margin-right: 1.7em;
    padding: .5em
}

.qm-mobile .qm-tree .qm-tree-node .qm-label {
    margin-top: 1.1em
}

.qm-mobile .qm-tree .qm-tree-node .qm-tree-name {
    cursor: pointer;
    margin-left: .4em;
    padding: 0;
    font-size: 1.3em
}

.qm-mobile .qm-tree .qm-tree-node .qm-tree-name.qm-selected {
    background: #4990e2;
    color: #fff
}

.qm-mobile .qm-tree .qm-tree-node:after {
    display: none;
    clear: both;
    content: '';
    width: 100%;
    height: .04em;
    background: #ece2e2
}

.qm-mobile .qm-collapse+.qm-sub-tree {
    display: none
}

.qm-mobile .qm-sub-tree {
    padding-left: 1em
}

.qm-mobile .qm-label.qm-checked .qm-checkbox {
    background: #EEEEEE;
}

.qm-mobile .qm-label .qm-checkbox {
    display: block;
    font-size: .92em;
    width: 1.7em;
    height: 1.7em;
    overflow: hidden;
    text-align: center;
    line-height: 1.7em;
    background: #fff;
    border-radius: .35em;
    position: relative
}

.qm-mobile .qm-icon:before {
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-right: .8em solid #ece2e2;
    border-top: .8em solid transparent
}

.qm-mobile .qm-label {
    margin: .95em .5em .03em 0;
    height: 1em
}

.qm-mobile .qm-panel .qm-close {
    top: .5em;
    right: inherit;
    left: .31em;
    width: .6em;
    height: .6em;
    font-size: 1em
}

.qm-mobile .qm-panel .qm-title {
    height: 3.1em;
    font-size: .5em;
    line-height: 3.1em;
    padding: 0 0 0 2.7em;
    margin-top: 0
}

.qm-mobile .qm-panel .qm-close:after,
.qm-mobile .qm-panel .qm-close:before {
    width: .6em
}

.qm-mobile .qmd-tree:before {
    font-size: 1.3em;
    position: relative;
    left: -2px;
    background: url("tree.png");
}

.qm-mobile .qm-toolbar.qm-toolbar-bottom {
    right: 0;
    top: .4em;
    left: inherit;
    bottom: inherit;
    transform: translate(0, 0)
}

.qm-mobile .qm-toolbar.qm-tree-toolbar {
    left: 0;
    top: .4em
}

.qm-mobile .qm-panel {
    z-index: 99
}

.qm-mobile .area-panel {
    z-index: 100
}

.qm-mobile .tree-panel {
    padding-top: 0;
    font-size: .85em
}

.qm-mobile .qm-container {
    padding-bottom: 1.5em;
    -webkit-overflow-scrolling: touch
}

.qm-mobile .qm-container>.qm-tree {
    font-size: .32em;
    padding-left: 0
}

.qm-mobile .qm-container>.qm-tree>.qm-tree-node {
    padding-left: 1em
}

.qm-mobile .qm-container>.qm-tree>.qm-sub-tree {
    padding-left: 2em
}

.qm-mobile .qm-sub-tree .qm-tree {
    font-size: 1em
}

.qm-mobile .qm-collapse .qm-icon:before {
    transform: rotate(-45deg)
}

.qm-drawing-wrap .qm-loading-text {
    color: #fff;
    font-size: 14px;
    margin-top: 5px
}

.qm-tree-header {
    margin: 0 10px;
    border-bottom: 1px solid #555
}

.qm-select {
    width: 100%;
    height: 30px;
    display: inline-block;
    font-size: 12px;
    line-height: 24px;
    position: relative
}

.qm-select .qm-select-current {
    display: block;
    position: relative;
    color: #FFFFFF;
    padding-right: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.qm-select .qm-select-current.qm-open::after {
    transform: rotate(180deg)
}

.qm-select .qm-select-current::after {
    position: absolute;
    right: 5px;
    top: 50%;
    border-top: 5px solid rgba(255, 255, 255, 0.6);
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    transform: translate(0, -50%);
    content: '';
    display: block
}

.qm-select .qm-select-list {
    display: none;
    width: 100%;
    position: absolute;
    z-index: 9;
    list-style: none;
    margin: 0;
    padding: 0;
    top: 100%;
    left: 0;
    border: 1px solid #888;
    background: #111;
    color: #fff
}

.qm-select .qm-select-list .qm-select-option {
    padding-left: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.qm-select .qm-select-list .qm-select-option:hover {
    background: #EEEEEE
}

.qm-select .qm-open+.qm-select-list {
    display: block
}

.qm-select-tree {
    padding: 3px 0 2px;
    width: 65px
}

.qm-mobile .qm-tree-header {
    height: 1.6em;
    margin: 0 .3em;
    padding: .3em 0;
    border-bottom: 0;
    margin-top: 0 !important
}

.qm-mobile .qm-tree-container {
    border-top: 1px solid #888
}

.qm-mobile .tree-panel .qm-panel-header {
    border-bottom: 1px solid #888
}

.qm-mobile .tree-panel .qm-panel-body {
    padding-top: 0 !important
}

.qm-tabs {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-size: .37em;
    border: 1px solid #888;
    border-radius: 5px;
    position: relative
}

.qm-tabs .qm-tabs-list {
    margin: 0;
    padding: 0;
    height: 100%
}

.qm-tabs .qm-tabs-option {
    float: left;
    list-style: none;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 2.3em;
    height: 100%;
    font-size: 1.1em
}

.qm-tabs .active {
    background: #EEEEEE
}

.qm-tabs .qm-open+.qm-select-list {
    display: block
}

.qm-tabs-map {
    margin: 7px 10px;
    padding-left: 10px;
    width: 80px;
    height: 20px;
    line-height: 20px;
    border: 1px solid #888
}

.threeTabs .qm-tabs-option:nth-child(2) {
    width: 33.5%;
    border-left: 1px solid #888;
    border-right: 1px solid #888
}

.threeTabs .qm-tabs-option:nth-child(1) {
    width: 33%;
    padding-left: .8em
}

.threeTabs .qm-tabs-option:nth-child(3) {
    width: 33.5%;
    padding-right: .8em
}

.doubleTabs .qm-tabs-option {
    width: 50%
}

.singleTab .qm-tabs-tree {
    border: none
}

.singleTab .active {
    background: transparent
}

.singleTab .qm-tabs-option {
    width: 100%;
    text-align: left;
    padding-left: .8em
}

.qm-tips {
    position: absolute;
    width: 280px;
    padding: 10px;
    top: 10px;
    left: 50%;
    transform: translate(-50%, 0);
    border-radius: 3px;
    background-color: #eff7ff;
    border: solid 1px #a0cbfc
}

.qm-tips .qm-tips-container {
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 14px;
    color: #888888
}

.qm-close {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 16px;
    color: #4a90e2
}

.qm-close::after {
    display: inline-block;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.qm-tips-key {
    color: #f5a623
}

.qm-mobile .qm-tips {
    z-index: ece2e2;
    width: 5em;
    padding: .1em;
    top: 1em;
    transform: translate(-50%, 0);
    border-radius: .1em;
    border: solid 1px #a0cbfc
}

.qm-mobile .qm-tips .qm-tips-container {
    height: 1.7em;
    line-height: 1.7em;
    font-size: .3em
}

.qm-mobile .qm-tips .qm-close {
    right: 1em;
    top: .3em;
    font-size: .35em
}

.qm-route-form {
    padding: 0 10px;
    font-size: 12px
}

.qm-route-form .qm-route-title {
    height: 43px;
    padding: 9px 0
}

.qm-route-form .qm-route-add {
    display: block;
    float: left;
    cursor: pointer;
    width: 130px;
    height: 25px;
    border-radius: 3px;
    border: 1px solid #EEEEEE;
    color: #EEEEEE;
    text-align: center;
    line-height: 22px
}

.qm-route-form .qm-route-add .qm-icon-add {
    width: 10px;
    height: 10px;
    display: inline-block;
    position: relative;
    margin-right: 8px
}

.qm-route-form .qm-route-add .qm-icon-add::before {
    content: '';
    display: block;
    width: 10px;
    height: 2px;
    position: absolute;
    background: #EEEEEE;
    left: 0;
    top: 4px
}

.qm-route-form .qm-route-add .qm-icon-add::after {
    content: '';
    display: block;
    position: absolute;
    width: 2px;
    height: 10px;
    background: #EEEEEE;
    left: 4px;
    top: 0
}

.qm-route-form .qm-route-clear {
    display: block;
    float: right;
    height: 25px;
    line-height: 22px;
    color: #EEEEEE;
    cursor: pointer
}

.qm-route-form .qm-route-clear.qm-route-disabled {
    color: #888
}

.qm-route-form .qm-route-panel-tips {
    text-align: center;
    color: #ece2e2;
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translate(-50%, 0)
}

.qm-route-form .qm-route-list {
    height: 180px;
    margin: 0;
    padding: 5px 0;
    list-style: none;
    border-top: 1px solid #555555;
    border-bottom: 1px solid #555555;
    overflow: auto
}

.qm-route-form .qm-route-list .qm-route-li {
    padding: 0 10px;
    height: 25px;
    line-height: 25px;
    color: #ffffff;
    margin-bottom: 1px
}

.qm-route-form .qm-route-list .qm-route-li:hover {
    background: rgba(102, 102, 102, 0.3)
}

.qm-route-form .qm-route-list .qm-route-li:hover .qm-route-box {
    display: block
}

.qm-route-form .qm-route-list .qm-route-li .qm-route-box {
    float: right;
    display: none
}

.qm-route-form .qm-route-list .qm-route-li .qm-route-button {
    cursor: pointer;
    margin-left: 10px;
    color: #EEEEEE
}

.qm-route-form .qm-route-list .qm-route-li.qm-selected {
    background: rgba(102, 102, 102, 0.3)
}

.qm-route-form .qm-route-list .qm-route-li:last-child .qm-route-play {
    display: none
}

.qm-route-form .qm-route-list.qm-route-disabled .qm-route-li:hover .qm-route-box {
    display: none
}

.qm-route-form .qm-route-foot .qm-route-time {
    margin: 10px 0;
    color: #ffffff;
    line-height: 25px
}

.qm-route-form .qm-route-foot .qm-route-time .qm-route-number {
    -webkit-appearance: none;
    width: 60px;
    height: 25px;
    padding: 0 0 0 9px;
    border: 1px solid #979797;
    color: #ffffff;
    background: transparent;
    outline: none
}

.qm-route-form .qm-route-foot .qm-route-time .qm-route-number::-webkit-outer-spin-button,
.qm-route-form .qm-route-foot .qm-route-time .qm-route-number::-webkit-inner-spin-button {
    -webkit-appearance: none
}

.qm-route-form .qm-route-foot .qm-route-time .qm-route-tips {
    margin-left: 9px;
    color: #ece2e2ece2e2
}

.qm-route-form .qm-route-foot .qm-route-control {
    width: 120px;
    height: 25px;
    color: #ffffff;
    border-radius: 3px;
    background: #EEEEEE;
    text-align: center;
    line-height: 23px;
    cursor: pointer
}

.qm-route-form .qm-route-foot .qm-route-control.qm-route-disabled {
    background: none;
    border: 1px solid #888;
    color: #888
}

.qm-route-form .qm-route-foot .qm-route-control.qm-route-disabled .qm-icon-play {
    border-left: 10px solid #888
}

.qm-route-form .qm-route-foot .qm-icon-play {
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid #fff;
    margin-right: 7px
}

.qm-route-form .qm-route-foot .qm-icon-stop {
    display: inline-block;
    width: 9px;
    height: 9px;
    background: #ffffff;
    margin-right: 7px
}

canvas {
    -webkit-tap-highlight-color: transparent
}

.qm-property-tab {
    margin: 0 10px 2px
}

.qm-property-tab .qm-tabs-list {
    width: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0
}

.qm-property-tab .qm-tabs-list:after {
    content: '';
    display: table;
    clear: both
}

.qm-property-tab .qm-tabs-list .qm-tabs-option {
    font-size: 14px;
    width: 50%;
    float: left;
    text-align: center;
    height: 38px;
    line-height: 38px;
    border-bottom: 3px solid #888
}

.qm-property-tab .qm-tabs-list .qm-tabs-option.active {
    border-color: #EEEEEE
}

.qm-property-icon {
    display: none;
    color: #ccc;
    vertical-align: middle;
    margin-left: 5px
}

.qm-property-icon:hover {
    color: #EEEEEE
}

.qm-tree-name.qm-selected+.qm-property-icon {
    display: inline-block
}

.qm-map-container {
    position: relative
}

.qm-map-toolbar {
    position: absolute
}

.qm-map-toolbar .qm-map-button {
    width: 30px;
    height: 20px;
    margin-left: 2px;
    border-radius: 2px;
    float: left;
    font-size: 12px;
    text-align: center;
    line-height: 20px
}

.qm-map-toolbar .qm-map-section,
.qm-map-toolbar .qm-map-isolate {
    background: #EEEEEE
}

.qm-map-toolbar .qm-map-cancel {
    background: #4a4a4a
}

.qm-family {
    width: 180px
}

.qm-family .qm-button {
    text-align: left
}

.qm-family .qm-sub-toolbar {
    max-height: 400px;
    overflow-y: auto
}

.qm-setting-none {
    display: none
}

.qm-mobile .qm-tree-icon {
    line-height: 2em;
    margin: 0 .1em;
    font-size: 2em
}

.qm-tree-label {
    display: none;
    padding: 8px 0;
    float: right
}

.qm-drawable-context {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 7;
    overflow: hidden
}

.qm-drawable-container,
.qm-drawable-dom {
    position: relative;
    z-index: 9
}

.qm-drawable-svg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%
}

.qm-drawable-point {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 20px solid #0f0;
    margin: -20px 0 0 -10px
}

.qm-drawable-image {
    position: absolute;
    display: block
}

.qm-drawable-text {
    position: absolute;
    display: block
}

.qm-drawable-label {
    position: absolute;
    user-select: none;
    transform: translate(-50%, -100%)
}

.qm-drawable-selected {
    opacity: 1 !important;
    border-color: #000080
}

.qm-tooltip-arrow {
    left: 7px;
    border-width: 7px 7px 0;
    border-color: transparent;
    border-top-color: rgba(0, 0, 0, 0.75);
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    bottom: -7px
}

.qm-tooltip-inner {
    max-width: 250px;
    padding: 3px 10px;
    text-align: left;
    text-decoration: none;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    min-height: 20px;
    font-size: 12px;
    white-space: nowrap
}

.qm-tooltip {
    background-color: rgba(0, 0, 0, 0.75);
    color: #fff;
    position: absolute;
    z-index: 1060;
    visibility: visible;
    font-size: 12px;
    line-height: 1.5
}

.qm-drawable-mini {
    display: none
}

.qm-drawable-mini-svg .qm-drawable-lead-line {
    display: none
}

.qm-drawable-show {
    display: block;
    z-index: 2
}

.qm-drawable-show .qm-drawable-lead-line {
    display: block
}

.qm-drawable-lead-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    position: absolute;
    user-select: none
}

.qm-drawable-lead-label.qm-drawable-show {
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4
}

.qm-annotation {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 9;
    min-width: 520px
}

.qm-toolbar-annotation {
    float: left
}

.qm-toolbar-control {
    float: left;
    margin-left: 10px;
    padding: 10px
}

.qm-toolbar-control .qm-cancel,
.qm-toolbar-control .qm-save {
    width: 60px;
    height: 30px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 28px;
    float: left;
    cursor: pointer
}

.qm-toolbar-control .qm-save {
    border-radius: 3px;
    border: 1px solid #979797
}

.qm-dwg svg {
    z-index: 3
}

.qm-dwg textarea {
    z-index: 3
}

.qm-color .qm-color-button {
    width: 24px;
    height: 24px;
    margin: 4px;
    border-radius: 100px;
    border: 2px solid #fff
}

.qm-line .qm-line-button {
    width: 24px
}

.qm-size {
    width: 50px !important;
    text-align: center;
    font-size: 16px
}

.qm-hide {
    display: none
}

.qm-drawing-container canvas {
    width: 100%;
    height: 100%
}

.qm-drawing-container .qm-family {
    width: auto
}

.qm-measure-conext {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0
}

.qm-measure-conext .qm-measure-svg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0
}

.qm-measure-conext .qm-measure-text {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0
}

.qm-measure-conext .qm-measure-text .qm-measure-handle {
    border-radius: 100px;
    display: block;
    position: absolute;
    transform: translate(-50%, -50%);
    color: #fff;
    text-align: center;
    font-style: normal;
    font-size: 12px;
    line-height: 1;
    cursor: default
}

.qm-measure-conext .qm-measure-text .qm-measure-number {
    position: absolute;
    font-size: 12px;
    color: #fff;
    line-height: 16px;
    padding: 0 6px;
    border-radius: 100px
}

.qm-measure-panel {
    min-width: 260px;
    height: 50px;
    border-radius: 3px;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    color: #fff;
    text-align: center;
    line-height: 50px;
    padding: 0 50px 0 20px;
    bottom: 70px;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 9
}

.qm-measure-panel .qm-measure-distance {
    color: #f57f23;
    margin: 0 5px
}

.qm-measure-panel .qm-measure-reset {
    display: none;
    width: 50px;
    height: 24px;
    background: none;
    border-radius: 3px;
    border: 1px solid #fff;
    color: #fff;
    font-size: 12px;
    margin-left: 10px
}

.qm-measure-panel .qm-measure-close {
    top: 17px;
    right: 17px
}

.qm-hide {
    display: none
}

.qm-measure-tab {
    margin: 0 10px;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #555
}

.qm-measure-tab::after {
    content: '';
    display: table;
    clear: both
}

.qm-measure-tab .qm-measure-tab-item {
    width: 33.3%;
    height: 32px;
    margin: 4px 0;
    text-align: center;
    float: left;
    border-radius: 3px;
    opacity: .6;
    cursor: pointer
}

.qm-measure-tab .qm-measure-tab-item.qm-active {
    background-color: rgba(136, 136, 136, 0.4);
    opacity: 1
}

.qm-measure-info {
    list-style: none;
    font-size: 12px;
    padding: 0;
    margin: 0;
    line-height: 24px
}

.qm-measure-info li {
    padding-left: 27px
}

.qm-measure-info .qm-measure-distance {
    color: #fff;
    padding-left: 11px;
    height: 32px;
    line-height: 32px
}

.qm-measure-info .qm-measure-distance::after {
    content: '';
    display: table;
    clear: both
}

.qm-measure-info .qm-measure-distance .qm-measure-value {
    font-size: 14px;
    color: #ff9d0b
}

.qm-measure-info .qm-measure-x {
    color: #d0021b
}

.qm-measure-info .qm-measure-y {
    color: #7ed321
}

.qm-measure-info .qm-measure-z {
    color: #4a90e2
}

.qm-measure-info .qm-measure-reset {
    cursor: pointer;
    font-size: 32px;
    float: right;
    display: none
}

.qm-mobile .qm-measure-reset {
    font-size: 1.8em;
    display: block
}

.qm-mobile .qm-measure-info {
    font-size: .37em
}

.qm-mobile .angleStyle .qm-panel-container {
    top: 1.38em !important
}

.qm-mobile .measure-panel {
    right: 0;
    background: transparent
}

.qm-mobile .measure-panel .qm-title {
    display: none
}

.qm-mobile .measure-panel .qm-panel-container {
    width: auto;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 1.3em;
    right: 1.3em;
    background: rgba(20, 8, 8, 0.66)
}

.qm-mobile .measure-panel .qm-measure-tab {
    background: rgba(20, 8, 8, 0.66);
    position: absolute;
    left: .15em;
    bottom: 0;
    height: 1em;
    width: 1em;
    margin: 0
}

.qm-mobile .measure-panel .qm-measure-tab .qm-measure-tab-item {
    width: 100%;
    height: 100%;
    margin: 0;
    line-height: 1em;
    background: transparent;
    display: none
}

.qm-mobile .measure-panel .qm-measure-tab .qm-measure-tab-item .qmd-distance,
.qm-mobile .measure-panel .qm-measure-tab .qm-measure-tab-item .qmd-angle {
    font-size: 1em
}

.qm-mobile .measure-panel .qm-measure-tab .qm-active {
    display: block !important
}

.qm-mobile .measure-panel .qm-close {
    top: auto;
    left: auto;
    background: rgba(20, 8, 8, 0.66);
    height: 1em;
    width: 1em;
    right: .15em;
    bottom: 0;
    line-height: .5em;
    text-align: center
}

.qm-mobile .measure-panel .qm-close .quit {
    font-size: .335em;
    color: #fff
}

.qm-mobile .measure-panel .qm-close:before {
    display: none
}

.qm-mobile .measure-panel .qm-close:after {
    display: none
}

.qm-mobile .measure-panel .qm-measure-value {
    font-size: 1em
}

.qm-mobile .measure-panel .qm-measure-x,
.qm-mobile .measure-panel .qm-measure-y,
.qm-mobile .measure-panel .qm-measure-z {
    width: 48%;
    height: 1.8em;
    line-height: 1.8em;
    display: inline-block;
    padding-left: .6em;
    font-size: 1em
}

.qm-mobile .measure-panel .qm-measure-distance {
    margin: 0 .4em;
    padding: 0 0 0 .3em;
    height: 2.8em;
    line-height: 2.8em;
    border-bottom: 1px solid #ece2e2
}

.qm-drawing-wrap .qm-measure-tab .qm-measure-tab-item {
    width: 50%
}

.qm-drawing-wrap.qm-mobile .qm-measure-tab {
    display: none
}

.qm-map .qm-close {
    top: 6px
}

.qm-map.qm-map-big .qm-close {
    top: 10px
}

.qm-map-header {
    position: relative;
    height: 30px
}

.qm-map-header .qm-map-screen {
    position: absolute;
    right: 35px;
    top: 6px;
    font-size: 14px;
    line-height: 1;
    display: block
}

.qm-map-header .qm-select-map {
    margin: 6px 10px;
    width: 120px;
    height: 20px;
    line-height: 20px
}

.qm-map-header .qm-select-map .qm-select-current {
    padding-left: 10px;
    border: 1px solid #888
}

.qm-map-header .qm-select-map .qm-select-list {
    max-height: 180px;
    overflow-y: auto
}

.qm-map-header .qm-map-move {
    position: absolute;
    left: 130px;
    height: 100%;
    right: 50px;
    top: 0
}

.qm-map-big {
    width: 530px !important;
    height: 500px !important;
    transform: translate(-50%, -50%)
}

.qm-map-big .qm-panel-container {
    overflow: hidden
}

.qm-map-big .qm-map-header {
    height: 46px;
    border-bottom: 1px solid #888;
    padding: 6px 0
}

.qm-map-big .qm-map-header .qm-select-map {
    width: 140px;
    height: 25px;
    font-size: 14px;
    margin: 4px 14px;
    line-height: 25px
}

.qm-map-big .qm-map-header .qm-select-map .qm-select-current {
    border-color: #979797
}

.qm-map-big .qm-map-header .qm-map-screen {
    top: 10px
}

.qm-map-big .qm-map-header .qm-map-move {
    left: 154px
}

.qm-map-big .qm-map-foot {
    display: block;
    font-size: 12px;
    height: 34px;
    line-height: 34px
}

.qm-map-big .qm-map-foot .qmd-information {
    font-size: 24px;
    vertical-align: middle;
    margin-right: 5px
}

.qm-map-big .qm-map-foot .qm-map-left {
    float: left
}

.qm-map-big .qm-map-foot .qm-map-right {
    float: right
}

.qm-map-big .qm-map-foot::after {
    display: table;
    content: '';
    clear: both
}

.qm-map-foot {
    display: none;
    padding: 0 10px
}

.qm-container.walker .qmd-max- {
    display: none
}

.qm-range-container {
    margin: 0 10px;
    border-top: 1px solid #555555;
    padding: 6px 0 13px
}

.qm-section-range {
    margin-top: 10px
}

.qm-range-list {
    list-style: none;
    margin: 0;
    padding: 0
}

.qm-range-list li {
    height: 30px;
    line-height: 30px
}

.qm-range-list li::after {
    content: '';
    display: table;
    clear: both
}

.qm-range-list .qm-range-name {
    display: block;
    width: 36px;
    float: left;
    font-size: 12px
}

.qm-range-list .qm-section-range {
    margin: 0;
    width: 100px;
    float: left;
    padding: 7px 0
}

.qm-section-panel {
    overflow: visible
}

.qm-section-panel .qm-scroll-bar {
    overflow: visible
}

.qm-section-panel .qm-panel-body {
    overflow: visible
}

.qm-section-panel .qm-panel-container {
    overflow: visible
}

.qm-section-plane-head {
    margin: 0 10px;
    border-bottom: 1px solid #555
}

.qm-section-plane-head::after {
    content: '';
    clear: both;
    display: table
}

.qm-section-plane-head .qm-select-axial {
    margin: 8px 0;
    width: 50px;
    height: 24px;
    float: left
}

.qm-section-plane-head .qm-button {
    width: 40px;
    height: 40px;
    padding: 4px;
    float: right
}

.qm-section-plane-head .qm-section-range {
    height: 30px
}

.qm-section-plane-head .qm-section-range .qm-input-range {
    background: #ece2e2
}

.qm-mobile .qm-section-info {
    font-size: .37em
}

.qm-mobile .section-panel {
    right: 0;
    background: transparent
}

.qm-mobile .section-panel .quit {
    color: #fff
}

.qm-mobile .section-panel .qm-select-current {
    height: 100%;
    font-size: .4em;
    padding-right: 0;
    text-align: center;
    line-height: 2.4em
}

.qm-mobile .section-panel .qm-select-current::after {
    right: .1em;
    top: 20%
}

.qm-mobile .section-panel .qm-select-list {
    top: inherit;
    bottom: 1em;
    background: rgba(17, 17, 17, 0.7)
}

.qm-mobile .section-panel .qm-select-list .qm-select-option {
    height: 2.4em;
    line-height: 2.4em;
    text-align: center;
    font-size: .4em;
    padding: 0
}

.qm-mobile .section-panel .qm-section-range {
    height: 100%;
    margin: 0
}

.qm-mobile .section-panel .qm-range {
    padding: 0 .15em 0 .1em;
    font-size: 1em;
    line-height: .45em
}

.qm-mobile .section-panel .qm-range input {
    height: .6em
}

.qm-mobile .section-panel input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 1.5em;
    width: 1.5em;
    margin-top: -0.5em;
    background: #fff;
    border-radius: 1em
}

.qm-mobile .section-panel .qm-section-plane-head {
    background: rgba(17, 17, 17, 0.7);
    position: absolute;
    left: .1em;
    bottom: 0;
    height: 1.02em;
    width: 1.02em;
    padding: 0;
    margin: 0;
    border-bottom: 0
}

.qm-mobile .section-panel .qm-section-plane-head .qm-select-axial {
    margin: 0;
    display: block;
    width: 100%;
    height: 100%;
    float: left;
    font-size: 1em
}

.qm-mobile .section-panel .qm-section-plane-head .qm-button {
    float: none
}

.qm-mobile .section-panel .qm-panel-header {
    display: none
}

.qm-mobile .section-panel .qmd-axial,
.qm-mobile .section-panel .qmd-axial- {
    width: 2em;
    height: 100%;
    line-height: 2.1em;
    font-size: .5em;
    margin-left: 2.2em;
    background: rgba(17, 17, 17, 0.7);
    opacity: 1;
    padding: 0
}

.qm-mobile .section-panel .qm-title {
    display: none
}

.qm-mobile .section-panel .qm-panel-container {
    width: auto;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 2.3em;
    right: 1.25em;
    background: rgba(17, 17, 17, 0.7)
}

.qm-mobile .section-panel .qm-section-tab {
    background: rgba(17, 17, 17, 0.7);
    position: absolute;
    left: -0.08em;
    bottom: 0;
    height: 1.02em;
    width: 1.02em
}

.qm-mobile .section-panel .qm-section-tab .qm-section-tab-item {
    width: 100%;
    height: 100%;
    margin: 0;
    line-height: 1em;
    background: transparent;
    display: none
}

.qm-mobile .section-panel .qm-section-tab .qm-section-tab-item .qmd-distance,
.qm-mobile .section-panel .qm-section-tab .qm-section-tab-item .qmd-angle {
    font-size: 1em
}

.qm-mobile .section-panel .qm-section-tab .qm-active {
    display: block !important
}

.qm-mobile .section-panel .qm-close {
    top: auto;
    left: auto;
    background: rgba(17, 17, 17, 0.7);
    height: 1.02em;
    width: 1.02em;
    right: .15em;
    bottom: 0;
    line-height: .5em;
    text-align: center
}

.qm-mobile .section-panel .qm-close .quit {
    font-size: .335em
}

.qm-mobile .section-panel .qm-close:before {
    display: none
}

.qm-mobile .section-panel .qm-close:after {
    display: none
}

.qm-mobile .section-panel .qm-section-value {
    font-size: 1em
}

.qm-mobile .section-panel .qm-section-x,
.qm-mobile .section-panel .qm-section-y,
.qm-mobile .section-panel .qm-section-z {
    width: 48%;
    height: 1.8em;
    line-height: 1.8em;
    display: inline-block;
    padding-left: .6em;
    font-size: 1em
}

.qm-house {
    width: 160px;
    height: 160px;
    position: absolute;
    right: 20px;
    top: 20px;
    opacity: .6;
    outline: none;
    transition: opacity .2s ease
}

.axial-checked {
    left: auto;
}

.xk-3d-hotspot {
    position: absolute;
    z-index: 11;
    cursor: pointer;
    pointer-events: all;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.xk-3d-hotspot>div.hotspot-border {
    position: absolute;
    width: 50px;
    height: 50px;
    top: -25px;
    left: -25px;
    background-image: url(data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==)
}

.xk-3d-hotspot>div.hotspot-border>div {
    position: absolute;
    left: 20px;
    top: 20px;
    margin: 0;
    width: 10px;
    height: 10px;
    background-color: #eb6852;
    border-radius: 100%;
    border: 1px solid #fff
}

.xk-3d-hotspot>div.hotspot-border>div:nth-child(2) {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.xk-3d-hotspot>div.hotspot-border>div:nth-child(3) {
    -webkit-animation-delay: -.4s;
    animation-delay: -.4s
}

.xk-3d-hotspot .hotspot-border:before {
    content: '';
    width: 0;
    height: 0;
    border: 12px solid transparent;
    border-right-color: rgba(255, 255, 255, .7);
    position: absolute;
    left: 19px;
    top: 37px;
    margin-top: -23px
}

.xk-3d-hotspot>span {
    position: absolute;
    top: -11px;
    left: 18px;
    color: #333;
    padding: 5px 10px;
    white-space: nowrap;
    background-color: rgba(255, 255, 255, .7);
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    font-size: 12px
}