import request from '@/utils/request'
import qs from 'qs'

// 获取项目列表
export function getProjectList(data) {
  return request({
    url: `/api/project/list`,
    method: 'GET',
    data,
  })
}

// 新建项目
export function addProject(data) {
  return request({
    url: `/api/project`,
    method: 'POST',
    data
  })
}
// 获取产品类型
// export function getProductMaterialLibraryData() {
//   return request({
//     url: `/api/system/serviceConfiguration/type/select/tree/jnpf.product.entity.material.ProductMaterialLibraryData`,
//     method: 'GET',
//   })
// }

// 获取业务的数据类型
export function getServiceConfigurationList(innerName) {
  return request({
    url: `/api/system/serviceConfiguration/type/select/tree/${innerName}`,
    method: 'GET',
  })
}

// 项目详情
export function getProject(id) {
  return request({
    url: `/api/project/${id}`,
    method: 'GET',
  })
}

// 获取项目关联需求单列表
export function getProjectDemandList(data) {
  return request({
    url: `/api/demand/project`,
    method: 'GET',
    data,
  })
}

// 通过输出物类型查找项目输入列表
export function getProjectSelectList(data) {
  return request({
    url: `/api/project/input/list`,
    method: 'GET',
    data,
  })
}

// 模板导入
export function importProject(data) {
  return request({
    url: `/api/project/template/import`,
    method: 'POST',
    data
  })
}

// 修改项目
export function updateProject(data) {
  return request({
    url: `/api/project`,
    method: 'PUT',
    data
  })
}

// 需求详情
export function projectDetail(id) {
  return request({
    url: `/api/project/demand/${id}`,
    method: 'GET',
  })
}
// 项目已关联需求
export function getProjectDemand(demandCode) {
  return request({
    url: `/api/demand/project/${demandCode}`,
    method: 'GET',
  })
}

// 删除项目
export function deleteProject(id) {
  return request({
    url: `/api/project/${id}`,
    method: 'DELETE',
  })
}

// 项目概述-基本信息
export function projectOverview(id) {
  return request({
    url: `/api/project/overview/${id}`,
    method: 'GET',
  })
}
// 项目概述-历程碑时间轴
export function projectTaskMilestone(data) {
  return request({
    url: `/api/project/task/milestone`,
    method: 'GET',
    data
  })
}
// 项目概述-任务月份状态统计
export function projectTaskStatusCollect(id) {
  return request({
    url: `/api/project/task/status/collect/${id}`,
    method: 'GET',
  })
}

// 任务交付成果
export function projectTaskDeliverable(id) {
  return request({
    url: `/api/project/task/deliverable/${id}`,
    method: 'GET',
  })
}

// 交付物成果任务列表(旧)
export function getDeliveryList(data) {
  return request({
    url: `/api/project/task/tree/list`,
    method: 'GET',
    data
  })
}

// 获取项目人员
export function getProjectMember(data) {
  return request({
    url: `/api/project/member`,
    method: 'GET',
    data,
  })
}

// 添加项目成员
export function addProjectMember(data) {
  return request({
    url: `/api/project/member`,
    method: 'POST',
    data,
  })
}

// 角色列表
export function membeRoleList(data) {
  return request({
    url: `/api/project/member/role/list`,
    method: 'GET',
    data,
  })
}

// 绑定角色
export function membeBindingRole(data) {
  return request({
    url: `/api/project/member/binding/role`,
    method: 'POST',
    data,
  })
}

// 删除项目成员
export function deleteProjectMember(data) {
  return request({
    url: `/api/project/member`,
    method: 'DELETE',
    data
  })
}

// 项目输入列表
export function projectInputList(data) {
  return request({
    url: `/api/project/input`,
    method: 'GET',
    data
  })
}


// 获取项目计划列表
export function getProjectPlanList(data) {
  return request({
    url: `/api/project/task/list`,
    method: 'GET',
    data
  })
}

// 新增/拆分项目任务
export function addProjectPlan(data) {
  return request({
    url: `/api/project/task`,
    method: 'POST',
    data,
  })
}

// 修改项目任务
export function updateProjectPlan(data) {
  return request({
    url: `/api/project/task`,
    method: 'PUT',
    data,
  })
}

// 获取负责人
export function getTaskUserList(projectCode) {
  return request({
    url: `/api/project/member/list/${projectCode}`,
    method: 'GET',
  })
}

// 变更负责人
export function chengeTaskUser(data) {
  return request({
    url: `/api/project/task/change/principal`,
    method: 'PUT',
    data
  })
}

// 任务树形下拉列表
export function getProjectPlanTree(data) {
  return request({
    url: `/api/project/task/tree`,
    method: 'GET',
    data
  })
}

// 任务详情
export function getProjectPlanDetail(data) {
  return request({
    url: `/api/project/task/info`,
    method: 'GET',
    data
  })
}
// 获取交付物类型树
export function getDeliverableTypeList(data) {
  return request({
    url: `/api/system/serviceConfiguration/type/tree/list/byTypes`,
    method: 'POST',
    data
  })
}

// 任务进度汇报
export function submitProjectPlanRecord(data) {
  return request({
    url: `/api/project/task/report/record`,
    method: 'POST',
    data
  })
}

// 上传交付物
export function submitTaskDeliverable(data) {
  return request({
    url: `/api/project/task/deliverable`,
    method: 'POST',
    data
  })
}

// 删除任务
export function deleteTask(data) {
  return request({
    url: `/api/project/task`,
    method: 'DELETE',
    data
  })
}

// 完成任务
export function completeTask(data) {
  return request({
    url: `/api/project/task/complete`,
    method: 'POST',
    data
  })
}

// 变更单列表（有分页）
export function getChangeList(data) {
  return request({
    url: `/api/task/change/request/list`,
    method: 'GET',
    data
  })
}

// 变更单列表（无分页）
export function getChangeListAll(projectCode) {
  return request({
    url: `/api/task/change/request/all/${projectCode}`,
    method: 'GET'
  })
}

// 创建变更单
export function addChange(data) {
  return request({
    url: `/api/task/change/request`,
    method: 'POST',
    data
  })
}

export function projectImportMapping(data) {
  return request({
    url: `/api/project/task/file/mapping`,
    method: 'POST',
    data
  })
}

// project文件上传
export function projectImport(data) {
  return request({
    url: `/api/project/task/file/import/mapping`,
    method: 'POST',
    data
  })
}

// 作废变更单
export function invalidatedHandle(data) {
  return request({
    url: `/api/task/change/request/cancellation`,
    method: 'PUT',
    data
  })
}

// 从甘特图修改任务
export function taskUpdateItem(data) {
  return request({
    url: `/api/project/task/list/change`,
    method: 'PUT',
    data
  })
}

// 删除进度汇报

export function deleteRecord(data) {
  return request({
    url: `/api/project/task/report/record`,
    method: 'DELETE',
    data
  })
}

// 查询项目或父任务周期
export function getTaskPeriod(data) {
  return request({
    url: `/api/project/task/period/info`,
    method: 'GET',
    data
  })
}

// 项目状态转换记录
export function projectStatusRecordList(projectCode) {
  return request({
    url: `/api/project/status/record/${projectCode}`,
    method: 'GET'
  })
}

// 获取新增对象的参数
export function getObjAddParams(data) {
  return request({
    url: `/api/ProductObjectRelationController/add/obj/param`,
    method: 'POST',
    data
  })
}

// 批量删除任务
export function deleteAllTask(data) {
  return request({
    url: `/api/project/task/batch`,
    method: 'DELETE',
    data
  })
}

// 获取任务交付成果列表(新)
export function NewgetDeliveryList(data) {
  return request({
    url: `/api/project/task/deliverable/list`,
    method: 'GET',
    data
  })
}

// 终止任务
export function stopTask(data) {
  return request({
    url: `/api/project/task/termination`,
    method: 'PUT',
    data
  })
}
//拆分任务时获取父任务的成果
export function getParentDeliverable(data) {
  return request({
    url: `/api/project/task/parent/deliverable/cons`,
    method: 'GET',
    data
  })
}

//获取子任务合计工时
export function getChildsManhour(data) {
  return request({
    url: `/api/project/task/manhour/total`,
    method: 'GET',
    data
  })
}

//复制项目
export function projectCopy(data) {
  return request({
    url: `/api/project/copy`,
    method: 'POST',
    data
  })
}

//验证交付物是否全部上传
export function deliverableCheck(data) {
  return request({
    url: `/api/project/task/deliverable/check`,
    method: 'GET',
    data
  })
}
