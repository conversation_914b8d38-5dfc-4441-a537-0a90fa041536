import request from '@/utils/request'

// L-bom --- 分页查询
export function queryLBomList(params) {
  return request({
    url: `/api/ProductLbomController/queryByLike`,
    method: 'GET',
    data: params
  })
}

// L-bom --- 新增
export function addLBomList(data) {
  return request({
    url: `/api/ProductLbomController/save`,
    method: 'POST',
    data
  })
}

// L-bom --- 删除
export function deleteLBomList(id) {
  return request({
    url: `/api/ProductLbomController/${id}`,
    method: 'DELETE',
  })
}

// L-bom --- 修改
export function editLBomList(data) {
  return request({
    url: `/api/ProductLbomController/update`,
    method: 'PUT',
    data
  })
}

// L-bom --- 查看详情
export function checkLBomList(id) {
  return request({
    url: `/api/ProductLbomController/detail/${id}`,
    method: 'GET',
  })
}


// L-BOM 目录


// 新增目录
export function addDirectory(data) {
  return request({
    url: `/api/ProductLbomLibraryController/save`,
    method: 'POST',
    data
  })
}

// 删除目录
export function deleteDirectory(id) {
  return request({
    url: `/api/ProductLbomLibraryController/${id}`,
    method: 'DELETE',
  })
}

// 修改目录
export function modifyDirectory(data) {
  return request({
    url: `/api/ProductLbomLibraryController/update`,
    method: 'PUT',
    data
  })
}

// 目录查询
export function queryDirectory(data) {
  return request({
    url: `/api/ProductLbomLibraryController/queryAll?code=${data.code}&version=${data.version}&id=${data.id}`,
    method: 'GET',
  })
}




// L-BOM BOM 数据

// BOM 列表分页查询
export function queryBOMList(data) {
  return request({
    url: `/api/ProductLbomLibraryDataController/queryByLike`,
    method: 'POST',
    data
  })
}
// BOM 列表 新增
export function addBOMList(data) {
  return request({
    url: `/api/ProductLbomLibraryDataController/save`,
    method: 'POST',
    data
  })
}
// BOM 列表 修改
export function editBOMList(data) {
  return request({
    url: `/api/ProductLbomLibraryDataController/update`,
    method: 'PUT',
    data
  })
}

// BOM 列表 查询
export function queryBOMListDetails(id) {
  return request({
    url: `/api/ProductLbomLibraryDataController/${id}`,
    method: 'GET',
  })
}

// BOM 列表 删除
export function deleteBOMList(id) {
  return request({
    url: `/api/ProductLbomLibraryDataController/${id}`,
    method: 'DELETE',
  })
}


// 文档文件管理

// 文档文件批量新增
export function documenBatchAddt(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/addBatch`,
    method: 'POST',
    data
  })
}

// 文档文件查询列表
export function queryDocumen(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/getInfoByLogicCode`,
    method: 'POST',
    data
  })
}
