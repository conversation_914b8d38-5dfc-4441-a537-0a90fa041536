﻿'use strict';

function _0x9F(b) {
  return b.document.getElementById("pageWidgetContainer" + (0).toString())
}

function guid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (b) {
    var a = 16 * Math.random() | 0;
    return ("x" == b ? a : a & 3 | 8).toString(16)
  })
}

function _0x8F() {
  try {
    return document.viewerElement.querySelector("iframe").contentWindow
  } catch (b) {
    return null
  }
}

function _0x5F() {
  _0x8F();
  var b = document;
  b._nowDrawType = "";
  b._isStartDraw = !1;
  b._isStopDraw = !0;
  b._0x6FMarkId = "";
  b._nowDrawSvgRectId = ""
}

function _0x7F(b) {
  return b.document.querySelector('svg[data-pdf-annotate-page\x3d"' + document._nowPageNum.toString() + '"]')
}

function _0x6F(b) {
  var a = document;
  b.target.classList.contains("qm-checked") ? b.target.classList.remove("qm-checked") : (b.target.classList.add("qm-checked"), a._nowChecked && a._nowChecked.classList.remove("qm-checked"), a._nowChecked = b.target);
  _0x5F();
  a._nowDrawType = "_0x6F";
  a._isStopDraw = !1;
  _0x4F();
  b = _0x8F();
  var c = _0x7F(b);
  c.onmousedown = function (b) {
    if (!a.ctrlKeyCode) if ("SVG" === b.target.tagName.toUpperCase() && !0 !== a._isStopDraw) {
      a._isStartDraw = !0;
      var d = document.createElementNS("http://www.w3.org/2000/svg", "defs"),
        f = document.createElementNS("http://www.w3.org/2000/svg", "marker"),
        g = document.createElementNS("http://www.w3.org/2000/svg", "line"),
        h = document.createElementNS("http://www.w3.org/2000/svg", "path"), m = b.offsetX;
      h.id = m;
      h.setAttribute("d", "M2,2 L10,6 L2,10 L6,6 L2,2");
      f.id = "lineMarker" + m;
      f.setAttribute("markerUnits", "strokeWidth");
      f.setAttribute("markerWidth", (a.newSVGWidth / a.newPageWidth * a._strokeWidth).toString());
      f.setAttribute("markerHeight", "12");
      f.setAttribute("viewBox", "0 0 12 12");
      f.setAttribute("refX", "6");
      f.setAttribute("refY", "6");
      f.setAttribute("orient", "auto");
      g.setAttribute("stroke", a._strokeColor);
      g.setAttribute("stroke-width", (a.newSVGWidth / a.newPageWidth * a._strokeWidth).toString());
      g.setAttribute("style", "fill:transparent;");
      f.appendChild(h);
      d.appendChild(f);
      c.appendChild(d);
      c.appendChild(g);
      var l = b.offsetX, n = b.offsetY;
      1 < a._defaultHeight && (l *= a.newSVGWidth / a.newPageWidth, n *= a.newSVGWidth / a.newPageWidth);
      g.setAttribute("x1", l.toString());
      g.setAttribute("y1", n.toString());
      g.setAttribute("x2", l.toString());
      g.setAttribute("y2", n.toString());
      c.onmouseup = function (b) {
        a._nowDrawSvgRectId = guid();
        g.setAttribute("id", a._nowDrawSvgRectId);
        a._0x6FMarkId = guid();
        d.setAttribute("id", a._0x6FMarkId);
        a._isStartDraw = !1;
        h.style.fill = a._strokeColor;
        var e = b.offsetX;
        b = b.offsetY;
        1 < a._defaultHeight && (e *= a.newSVGWidth / a.newPageWidth, b *= a.newSVGWidth / a.newPageWidth);
        e -= l;
        b -= n;
        20 > Math.sqrt(e * e + b * b) && (c.removeChild(g), c.removeChild(d))
      };
      c.onmousemove = function (b) {
        var d = b.offsetX;
        b = b.offsetY;
        !0 === a._isStartDraw && (1 < a._defaultHeight && (d *= a.newSVGWidth / a.newPageWidth, b *= a.newSVGWidth / a.newPageWidth), g.setAttribute("x2", d.toString()), g.setAttribute("y2", b.toString()), g.setAttribute("marker-end", "url(#lineMarker" + m + ")"))
      };
      b.stopPropagation()
    } else if ("LINE" === b.target.tagName.toUpperCase()) {
      var k = b.target;
      if (k.getAttribute("id") === a._nowDrawSvgRectId) {
        console.log(0);
        var q = !1, p = 0, r = 0, t = 0, u = 0, v = 0, w = 0, q = !0, p = b.offsetX, r = b.offsetY;
        1 < a._defaultHeight && (p *= a.newSVGWidth / a.newPageWidth, r *= a.newSVGWidth / a.newPageWidth);
        console.log("0-1");
        t = parseFloat(k.getAttribute("x1"));
        u = parseFloat(k.getAttribute("y1"));
        v = parseFloat(k.getAttribute("x2"));
        w = parseFloat(k.getAttribute("y2"));
        b.stopPropagation();
        console.log("0-2");
        c.onmousemove = function (b) {
          if (!0 === q) {
            console.log(1);
            var d = b.offsetX, c = b.offsetY;
            console.log(2);
            1 < a._defaultHeight && (console.log(3), d *= a.newSVGWidth / a.newPageWidth, c *= a.newSVGWidth / a.newPageWidth);
            k.setAttribute("x1", (t + d - p).toString());
            k.setAttribute("y1", (u + c - r).toString());
            k.setAttribute("x2", (v + d - p).toString());
            k.setAttribute("y2", (w + c - r).toString());
            console.log(5);
            b.stopPropagation()
          }
        };
        c.onmouseup = function () {
          console.log(6);
          q = !1;
          w = v = u = t = r = p = 0
        }
      }
    }
  }
}

function _0xAA(b) {
  var a = document;
  b.target.classList.contains("qm-checked") ? b.target.classList.remove("qm-checked") : (b.target.classList.add("qm-checked"), a._nowChecked && a._nowChecked.classList.remove("qm-checked"), a._nowChecked = b.target);
  _0x5F();
  a._nowDrawType = "_0xAA";
  a._isStopDraw = !1;
  _0x4F();
  b = _0x8F();
  var c = _0x7F(b);
  c.onmousedown = function (b) {
    if (!a.ctrlKeyCode) if ("SVG" === b.target.tagName.toUpperCase() && !0 !== a._isStopDraw) {
      a._isStartDraw = !0;
      var d = document.createElementNS("http://www.w3.org/2000/svg", "path");
      d.setAttribute("stroke", a._strokeColor);
      d.setAttribute("stroke-width", (a.newSVGWidth / a.newPageWidth * a._strokeWidth).toString());
      d.setAttribute("style", "fill:transparent;");
      c.appendChild(d);
      var f = b.offsetX, g = b.offsetY;
      1 < a._defaultHeight && (f *= a.newSVGWidth / a.newPageWidth, g *= a.newSVGWidth / a.newPageWidth);
      c.onmouseup = function (b) {
        a._nowDrawSvgRectId = guid();
        console.log("thisbak._nowDrawSvgRectId:" + a._nowDrawSvgRectId);
        d.setAttribute("id", a._nowDrawSvgRectId);
        d.setAttribute("x", f);
        d.setAttribute("y", g);
        a._isStartDraw = !1
      };
      c.onmousemove = function (b) {
        var c = b.offsetX, e = b.offsetY;
        if (!0 === a._isStartDraw) {
          1 < a._defaultHeight && (c *= a.newSVGWidth / a.newPageWidth, e *= a.newSVGWidth / a.newPageWidth);
          b = [0, 0, -13, -39, 35, -42, 17, -47, 57, -21, 57, -21, 40, -21, 58, 11, 58, 11, 38, -5, 37, 37, 37, 37, 15, 36, -8, 46, -23, 50, 15, 15, -25, 23, -58, 16, -38, 23, -69, -11, -70, -12, -1, -1, -42, -6, -36, -39];
          for (var c = (c - f) / 200, e = (e - g) / 60, p = "", h = 0; 8 > h; h++) p += "c" + b[6 * h] * c + "," + b[6 * h + 1] * e + " " + b[6 * h + 2] * c + "," + b[6 * h + 3] * e + " " + b[6 * h + 4] * c + "," + b[6 * h + 5] * e;
          p += "z";
          console.log("x:" + f + "y:" + g);
          d.setAttributeNS(null, "d", "m" + f.toString() + "," + g.toString() + p)
        }
      };
      b.stopPropagation()
    } else if ("PATH" === b.target.tagName.toUpperCase()) {
      var h = b.target;
      if (h.getAttribute("id") === a._nowDrawSvgRectId) {
        console.log(0);
        var m = !1, l = 0, n = 0, k = 0, q = 0, m = !0, l = b.offsetX, n = b.offsetY;
        1 < a._defaultHeight && (l *= a.newSVGWidth / a.newPageWidth, n *= a.newSVGWidth / a.newPageWidth);
        console.log("0-1");
        var p = h.getAttribute("d").split(","), k = parseFloat(p[0].replace("m", "")), r = p[1].lastIndexOf("c"),
          t = p[1].substr(r), q = parseFloat(p[1].substring(0, r));
        b.stopPropagation();
        console.log("0-2");
        c.onmousemove = function (b) {
          if (!0 === m) {
            console.log(1);
            var c = b.offsetX, d = b.offsetY;
            console.log(2);
            1 < a._defaultHeight && (console.log(3), c *= a.newSVGWidth / a.newPageWidth, d *= a.newSVGWidth / a.newPageWidth);
            console.log(4);
            console.log("mouse:" + (k + c - l).toString());
            p[0] = "m" + (k + c - l).toString();
            p[1] = (q + d - n).toString() + t;
            h.setAttribute("d", p.join(","));
            console.log("pathD:" + p.join(","));
            b.stopPropagation()
          }
        };
        c.onmouseup = function () {
          console.log(6);
          m = !1;
          q = k = n = l = 0
        }
      }
    }
  }
}

function getAnnotation() {
  _0x4F();
  var b = _0x8F(), b = _0x7F(b), b = Base64.encode(b.innerHTML);
  console.log(b);
  var a = _0x8F().document.getElementById("DocumentViewer"), b = {
    mark: b,
    scrollTop: a.scrollTop,
    scrollLeft: a.scrollLeft,
    zoom: document.myWebViewer.getZoomLevel(),
    curFile: document.pdfFile
  };
  console.log(JSON.stringify(b));
  return JSON.stringify(b)
}

function setAnnotation(b) {
  _0x4F();
  _0x8F();
  var a = _0x8F().document.getElementById("DocumentViewer");
  b = JSON.parse(b);
  console.log(b);
  API2D_restoreAnnotation(b.mark);
  document.myWebViewer.setZoomLevel(b.zoom);
  a.scrollLeft = b.scrollLeft;
  a.scrollTop = b.scrollTop
}

function API2D_restoreAnnotation(b) {
  setTimeout(function () {
    _0x4F();
    var a = _0x8F();
    _0x7F(a).innerHTML = Base64.decode(b)
  }, 200)
}

function clearAnnotation() {
  _0x4F();
  var b = _0x8F();
  _0x7F(b).innerHTML = ""
}

function _0xAC(b) {
  var a = this;
  b.document.onkeydown = function (b) {
    46 === b.keyCode && (null != a._element && (a._element.remove(), a._element = null), null != a._svgImageElement && (a._svgImageElement.remove(), a._svgImageElement = null));
    if (189 === b.keyCode && null != a._svgImageElement) {
      var d = parseFloat(a._svgImageElement.getAttribute("width").replace("px", "")),
        c = parseFloat(a._svgImageElement.getAttribute("height").replace("px", ""));
      a._svgImageElement.setAttribute("width", .99 * d + "px");
      a._svgImageElement.setAttribute("height", .99 * c + "px")
    }
    187 === b.keyCode && null != a._svgImageElement && (d = parseFloat(a._svgImageElement.getAttribute("width").replace("px", "")), c = parseFloat(a._svgImageElement.getAttribute("height").replace("px", "")), a._svgImageElement.setAttribute("width", 1.01 * d + "px"), a._svgImageElement.setAttribute("height", 1.01 * c + "px"));
    13 === b.keyCode && null != a._svgImageElement && (parseFloat(a._svgImageElement.getAttribute("width").replace("px", "")), parseFloat(a._svgImageElement.getAttribute("height").replace("px", "")))
  }
}

function _0xAD(b) {
  var a = document;
  b.target.classList.contains("qm-checked") ? b.target.classList.remove("qm-checked") : (b.target.classList.add("qm-checked"), a._nowChecked && a._nowChecked.classList.remove("qm-checked"), a._nowChecked = b.target);
  _0x5F();
  var c = !0;
  a._nowDrawType = "_0xAD";
  a._textFontSize = 10;
  _0x4F();
  b = _0x8F();
  var d = _0x7F(b);
  _0xAC(b);
  d.onmousedown = function (b) {
    if (!a.ctrlKeyCode) {
      var e = $('\x3cinput class\x3d"drawtext" type\x3d"text"  size\x3d"10"/\x3e');
      e.css("top", (b.screenY - 70).toString() + "px");
      e.css("left", b.screenX.toString() + "px");
      e.css("position", "absolute");
      e.css("font-size", a._textFontSize.toString() + "px");
      e.css("z-index", "10000");
      e.css("color", a._textColor);
      c || (e = null);
      var g = b.offsetX, h = b.offsetY;
      document.getElementById("dwgFileList");
      c && (e.blur(function () {
        var b = $(this).val(), e = document.createElementNS("http://www.w3.org/2000/svg", "text"), f = g,
          k = h + a._textFontSize;
        1 < a._defaultHeight && (f *= a.newSVGWidth / a.newPageWidth, k *= a.newSVGWidth / a.newPageWidth);
        e.setAttribute("x", f.toString());
        e.setAttribute("y", k.toString());
        e.setAttribute("stroke", a._textColor);
        e.setAttribute("fill", a._textColor);
        e.setAttribute("style", "font-size:" + (a.newSVGWidth / a.newPageWidth * a._textFontSize * 1.3).toString() + "px");
        e.textContent = b;
        $(this).remove();
        d.appendChild(e);
        a._nowDrawSvgRectId = guid();
        e.setAttribute("id", a._nowDrawSvgRectId);
        "_0xAD" === a._nowDrawType && (c = !1);
        _0x5F();
        e.addEventListener("mouseover", function () {
          this.style.stroke = "yellow";
          this.style.fontWeight = "Bold"
        }, !1);
        e.addEventListener("mouseout", function () {
          this.style.stroke = "";
          this.style.fontWeight = ""
        }, !1);
        e.addEventListener("click", function () {
        }, !1)
      }), $(document.body).append(e));
      c = !1;
      setTimeout(function () {
        c && e.focus()
      }, 50);
      b.stopPropagation()
    }
  }
}

function _0xAF() {
  var b = document;
  null != b.viewerElement.querySelector("iframe") && (b = b.viewerElement.querySelector("iframe").contentWindow, b = _0x9F(b), null != b && (b = b.querySelector("svg"), null != b && b.remove(), this._nowDrawType = ""))
}

function _0xBA(b) {
  _0x5F();
  var a = document;
  b.target.classList.contains("qm-checked") ? b.target.classList.remove("qm-checked") : (b.target.classList.add("qm-checked"), a._nowChecked && a._nowChecked.classList.remove("qm-checked"), a._nowChecked = b.target);
  a._nowDrawType = "_0xBA";
  a._isStopDraw = !1;
  console.log("_0xBA");
  _0x4F();
  b = _0x8F();
  var c = _0x7F(b);
  c.onmousedown = function (b) {
    if (!a.ctrlKeyCode) if ("SVG" === b.target.tagName.toUpperCase() && !0 !== a._isStopDraw) {
      a._isStartDraw = !0;
      var d = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      d.setAttribute("stroke", a._strokeColor);
      d.setAttribute("stroke-width", (a.newSVGWidth / a.newPageWidth * a._strokeWidth).toString());
      d.setAttribute("style", "fill:transparent;");
      d.setAttribute("id", guid());
      c.appendChild(d);
      var f = b.offsetX, g = b.offsetY;
      1 < a._defaultHeight && (f *= a.newSVGWidth / a.newPageWidth, g *= a.newSVGWidth / a.newPageWidth);
      c.onmouseup = function (b) {
        a._nowDrawSvgRectId = guid();
        d.setAttribute("id", a._nowDrawSvgRectId);
        a._isStartDraw = !1
      };
      c.onmousemove = function (b) {
        var c = b.offsetX;
        b = b.offsetY;
        !0 === a._isStartDraw && (1 < a._defaultHeight && (c *= a.newSVGWidth / a.newPageWidth, b *= a.newSVGWidth / a.newPageWidth), d.setAttribute("r", (Math.sqrt(Math.pow(c - f, 2) + Math.pow(b - g, 2)) / 2).toString()), c = c > f ? f + (c - f) / 2 : c + (f - c) / 2, b = b > g ? g + (b - g) / 2 : b + (g - b) / 2, d.setAttribute("cx", c.toString()), d.setAttribute("cy", b.toString()))
      };
      b.stopPropagation()
    } else if ("CIRCLE" === b.target.tagName.toUpperCase()) {
      var h = b.target;
      if (h.getAttribute("id") === a._nowDrawSvgRectId) {
        console.log(0);
        var m = !1, l = 0, n = 0, k = 0, q = 0, m = !0, l = b.offsetX, n = b.offsetY;
        1 < a._defaultHeight && (l *= a.newSVGWidth / a.newPageWidth, n *= a.newSVGWidth / a.newPageWidth);
        console.log("0-1");
        k = parseFloat(h.getAttribute("cx"));
        q = parseFloat(h.getAttribute("cy"));
        b.stopPropagation();
        console.log("0-2");
        c.onmousemove = function (b) {
          if (!0 === m) {
            console.log(1);
            var c = b.offsetX, d = b.offsetY;
            console.log(2);
            1 < a._defaultHeight && (console.log(3), c *= a.newSVGWidth / a.newPageWidth, d *= a.newSVGWidth / a.newPageWidth);
            console.log(4);
            console.log("mouse:" + (k + c - l).toString());
            h.setAttribute("cx", (k + c - l).toString());
            h.setAttribute("cy", (q + d - n).toString());
            console.log(5);
            b.stopPropagation()
          }
        };
        c.onmouseup = function () {
          console.log(6);
          m = !1;
          q = k = n = l = 0
        }
      }
    }
  }
}

var Viewer2D = function (b, a, c) {
  // this.modelPath = a;//thefile
  // this.pdfFiles = c;//thepdf
  // this.net = b//new NetHandle()
  this.net = b;
  this.url = a;
};
Viewer2D.prototype = {
  init: function () {
    // Init2D(this.net, this.modelPath, this.pdfFiles)
    Init2D(this.net, this.url)
  }, getAnnotation: function () {
    return getAnnotation()
  }, setAnnotation: function (b) {
    setAnnotation(b)
  }
};

//b new NetHandle()  a thefile model= value  c thepdf  file = mode.pdf
// function Init2D(b, a, c, d) {
//   var e = document;
//   e._nowPageNum = 0;
//   e._strokeColor;
//   e._strokeWidth;
//   e._page = 1;
//   e._firstzoom = 0;
//   e._nowPageNum = 1;
//   e._numPages = 0;
//   e._strokeColor = "red";
//   e._strokeWidth = 2;
//   e._textFontSize = 16;
//   e._textColor = "red";
//   e._defaultHeight = 2;
//   e._nowChecked = null;
//   d = c.split(",");
//   a = b.getPdfPath(a, c);
//   b = document.getElementById("tzselect");
//   b.innerHTML = "";
//   b.onchange = function (a) {
//     e.myWebViewer = null;
//     a = document.getElementById("viewerElement");
//     a.innerHTML = "";
//     e.viewerElement = a;
//     var b = this.value;
//     e.pdfFile = this.options[this.options.selectedIndex].innerHTML;
//     b = b.replace(/szths/g, "#");
//     b = b.replace(/\\/g, "/");
//     a = new PDFTron.WebViewer({
//       path: "lib",
//       type: "html5",
//       l: "localhost",
//       documentType: "pdf",
//       initialDoc: b,
//       mobileRedirect: !1
//     }, a);
//     e.myWebViewer = a
//   };
//   for (c = 0; c < d.length; c++) {
//     var f = document.createElement("option");
//     f.setAttribute("value", a[c]);
//     f.innerHTML = d[c].substring(0, d[c].indexOf("."));
//     b.appendChild(f)
//   }
//   a = a[0];
//   e.pdfFile = d[0];
//   a = a.replace(/szths/g, "#");
//   d = a = a.replace(/\\/g, "/");
//   b = d.lastIndexOf("/") + 1;
//   d = d.substring(0, b);
//   a = a.substring(b);
//   a = d + a;
//   d = document.getElementById("viewerElement");
//   e.viewerElement = d;
//   d = new PDFTron.WebViewer({
//     path: "lib",
//     type: "html5",
//     l: "localhost",
//     documentType: "pdf",
//     initialDoc: a,
//     mobileRedirect: !1
//   }, d);
//   e.myWebViewer = d;
//   document.height = "100%";
//   document.body.height = "100%";
//   $(".qmd-rectangle").on("click", _0xBB);
//   $(".qmd-arrow").on("click", _0x6F);
//   $(".qmd-cloud").on("click", _0xAA);
//   $(".qmd-circle").on("click", _0xBA);
//   $(".qmd-text").on("click", _0xAD);
//   $(".qmd-save").on("click", getAnnotation);
//   $(".qmd-cancle").on("click", clearAnnotation)
// }
function getUrlQuery() {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)")
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}
function Init2D(b, a, c, d) {
  var e = document;
  e._nowPageNum = 0;
  e._strokeColor;
  e._strokeWidth;
  e._page = 1;
  e._firstzoom = 0;
  e._nowPageNum = 1;
  e._numPages = 0;
  e._strokeColor = "red";
  e._strokeWidth = 2;
  e._textFontSize = 16;
  e._textColor = "red";
  e._defaultHeight = 2;
  e._nowChecked = null;
  // d = c.split(",");
  // a = b.getPdfPath(a, c);
  // b = document.getElementById("tzselect");
  // b.innerHTML = "";
  // b.onchange = function (a) {
  //   e.myWebViewer = null;
  //   a = document.getElementById("viewerElement");
  //   a.innerHTML = "";
  //   e.viewerElement = a;
  //   var b = this.value;
  //   e.pdfFile = this.options[this.options.selectedIndex].innerHTML;
  //   b = b.replace(/szths/g, "#");
  //   b = b.replace(/\\/g, "/");
  //   a = new PDFTron.WebViewer({
  //     path: "lib",
  //     type: "html5",
  //     l: "localhost",
  //     documentType: "pdf",
  //     initialDoc: b,
  //     mobileRedirect: !1
  //   }, a);
  //   e.myWebViewer = a
  // };
  // for (c = 0; c < d.length; c++) {
  //   var f = document.createElement("option");
  //   f.setAttribute("value", a[c]);
  //   f.innerHTML = d[c].substring(0, d[c].indexOf("."));
  //   b.appendChild(f)
  // }
  // a = a[0];
  // e.pdfFile = d[0];
  e.pdfFile = c
  // a = a.replace(/szths/g, "#");
  // d = a = a.replace(/\\/g, "/");
  // b = d.lastIndexOf("/") + 1;
  // d = d.substring(0, b);
  // a = a.substring(b);
  // a = d + a;

  const url = a

  d = document.getElementById("viewerElement");
  e.viewerElement = d;
  d = new PDFTron.WebViewer({
    path: "lib",
    type: "html5",
    l: "localhost",
    documentType: "pdf",
    initialDoc: url,
    mobileRedirect: !1
  }, d);
  e.myWebViewer = d;
  document.height = "100%";
  document.body.height = "100%";
  $(".qmd-rectangle").on("click", _0xBB);
  $(".qmd-arrow").on("click", _0x6F);
  $(".qmd-cloud").on("click", _0xAA);
  $(".qmd-circle").on("click", _0xBA);
  $(".qmd-text").on("click", _0xAD);
  $(".qmd-save").on("click", getAnnotation);
  $(".qmd-cancle").on("click", clearAnnotation)
}

function _0xBB(b) {
  _0x5F();
  var a = document;
  b.target.classList.contains("qm-checked") ? b.target.classList.remove("qm-checked") : (b.target.classList.add("qm-checked"), a._nowChecked && a._nowChecked.classList.remove("qm-checked"), a._nowChecked = b.target);
  a._nowDrawType = "_0xBB";
  a._isStopDraw = !1;
  _0x4F();
  b = _0x8F();
  var c = _0x7F(b);
  c.onmousedown = function (b) {
    if (!a.ctrlKeyCode) if ("SVG" === b.target.tagName.toUpperCase() && !0 !== a._isStopDraw) {
      a._isStartDraw = !0;
      var d = document.createElementNS("http://www.w3.org/2000/svg", "rect");
      d.setAttribute("stroke", a._strokeColor);
      d.setAttribute("stroke-width", (a.newSVGWidth / a.newPageWidth * a._strokeWidth).toString());
      d.setAttribute("style", "fill:transparent;");
      d.setAttribute("id", guid());
      c.appendChild(d);
      var f = b.offsetX, g = b.offsetY;
      console.log("x:" + f + "y:" + g);
      console.log("newPageWidth:" + a.newPageWidth + "newSVGWidth:" + a.newSVGWidth);
      1 < a._defaultHeight && (f *= a.newSVGWidth / a.newPageWidth, g *= a.newSVGWidth / a.newPageWidth);
      d.setAttribute("x", f.toString());
      d.setAttribute("y", g.toString());
      console.log("x:" + f + "y:" + g);
      c.onmouseup = function (b) {
        a._nowDrawSvgRectId = guid();
        d.setAttribute("id", a._nowDrawSvgRectId);
        a._isStartDraw = !1
      };
      c.onmousemove = function (b) {
        var c = b.offsetX;
        b = b.offsetY;
        1 < a._defaultHeight && (c *= a.newSVGWidth / a.newPageWidth, b *= a.newSVGWidth / a.newPageWidth);
        !0 === a._isStartDraw && (d.setAttribute("width", Math.abs(c - f).toString()), d.setAttribute("height", Math.abs(b - g).toString()))
      };
      b.stopPropagation()
    } else if ("RECT" === b.target.tagName.toUpperCase()) {
      var h = b.target;
      if (h.getAttribute("id") === a._nowDrawSvgRectId) {
        var m = !1, l = 0, n = 0, k = 0, q = 0, m = !0, l = b.offsetX, n = b.offsetY;
        1 < a._defaultHeight && (l *= a.newSVGWidth / a.newPageWidth, n *= a.newSVGWidth / a.newPageWidth);
        k = parseFloat(h.getAttribute("x"));
        q = parseFloat(h.getAttribute("y"));
        b.stopPropagation();
        c.onmousemove = function (b) {
          if (!0 === m) {
            var c = b.offsetX, d = b.offsetY;
            1 < a._defaultHeight && (console.log(3), c *= a.newSVGWidth / a.newPageWidth, d *= a.newSVGWidth / a.newPageWidth);
            console.log("mouse:" + (k + c - l).toString());
            h.setAttribute("x", (k + c - l).toString());
            h.setAttribute("y", (q + d - n).toString());
            b.stopPropagation()
          }
        };
        c.onmouseup = function () {
          console.log(6);
          m = !1;
          q = k = n = l = 0
        }
      }
    }
  }
}

function _0xBC(b) {
  b = new RegExp("(^|\x26)" + b + "\x3d([^\x26]*)(\x26|$)", "i");
  b = window.location.search.substr(1).match(b);
  return null != b && 1 < b.length ? decodeURI(b[2]) : null
}

function _0x4F() {
  var b = document.viewerElement.querySelector("iframe").contentWindow, a = _0x9F(b), c = document, d = null;
  a && (c.newPageWidth = a.offsetWidth, c.newPageHeight = a.offsetHeight, a.setAttribute("data-page-number", c._nowPageNum.toString()), d = a.querySelector("svg"));
  if (!d) {
    c.restoreZoom = c.myWebViewer.getZoomLevel();
    c.newPageWidth = a.offsetWidth;
    c.newPageHeight = a.offsetHeight;
    d = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    d.id = "qmd-svg";
    d.setAttribute("data-pdf-annotate-page", c._nowPageNum.toString());
    d.setAttribute("class", "annotationLayer");
    d.setAttribute("width", c.newPageWidth.toString());
    d.setAttribute("height", c.newPageHeight.toString());
    d.setAttribute("viewBox", "0 0 " + (c.newPageWidth / c.restoreZoom * 10).toFixed(1) + " " + (c.newPageHeight / c.restoreZoom * 10).toFixed(1));
    a.appendChild(d);
    var e;
    d.onmouseover = function (a) {
      var d = a.target;
      "SVG" !== d.tagName.toUpperCase() && !0 !== c._isStartDraw && (e = d.getAttribute("stroke"), d.setAttribute("stroke", "yellow"), b.document.onkeydown = function (a) {
        46 === a.keyCode && d.remove()
      })
    };
    d.onmouseout = function (a) {
      a = a.target;
      "SVG" !== a.tagName.toUpperCase() && !0 !== c._isStartDraw && e && (a.setAttribute("stroke", "red"), e = void 0)
    };
    c.newSVGWidth = parseFloat((c.newPageWidth / c.restoreZoom * 10).toFixed(1))
  }
}

var Base64 = {
  _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\x3d", encode: function (b) {
    var a = "", c, d, e, f, g, h, m = 0;
    for (b = Base64._utf8_encode(b); m < b.length;) c = b.charCodeAt(m++), d = b.charCodeAt(m++), e = b.charCodeAt(m++), f = c >> 2, c = (c & 3) << 4 | d >> 4, g = (d & 15) << 2 | e >> 6, h = e & 63, isNaN(d) ? g = h = 64 : isNaN(e) && (h = 64), a = a + this._keyStr.charAt(f) + this._keyStr.charAt(c) + this._keyStr.charAt(g) + this._keyStr.charAt(h);
    return a
  }, decode: function (b) {
    var a = "", c, d, e, f, g, h = 0;
    for (b = b.replace(/[^A-Za-z0-9\+\/\=]/g, ""); h < b.length;) c = this._keyStr.indexOf(b.charAt(h++)), d = this._keyStr.indexOf(b.charAt(h++)), f = this._keyStr.indexOf(b.charAt(h++)), g = this._keyStr.indexOf(b.charAt(h++)), c = c << 2 | d >> 4, d = (d & 15) << 4 | f >> 2, e = (f & 3) << 6 | g, a += String.fromCharCode(c), 64 != f && (a += String.fromCharCode(d)), 64 != g && (a += String.fromCharCode(e));
    return a = Base64._utf8_decode(a)
  }, _utf8_encode: function (b) {
    b = b.replace(/\r\n/g, "\n");
    for (var a = "", c = 0; c < b.length; c++) {
      var d = b.charCodeAt(c);
      128 > d ? a += String.fromCharCode(d) : (127 < d && 2048 > d ? a += String.fromCharCode(d >> 6 | 192) : (a += String.fromCharCode(d >> 12 | 224), a += String.fromCharCode(d >> 6 & 63 | 128)), a += String.fromCharCode(d & 63 | 128))
    }
    return a
  }, _utf8_decode: function (b) {
    for (var a = "", c = 0, d, e, f; c < b.length;) d = b.charCodeAt(c), 128 > d ? (a += String.fromCharCode(d), c++) : 191 < d && 224 > d ? (f = b.charCodeAt(c + 1), a += String.fromCharCode((d & 31) << 6 | f & 63), c += 2) : (f = b.charCodeAt(c + 1), e = b.charCodeAt(c + 2), a += String.fromCharCode((d & 15) << 12 | (f & 63) << 6 | e & 63), c += 3);
    return a
  }
};
