
import request from '@/utils/request'
// 分页查询工作日历
export function getProjectCalendarList (data) {
  return request({
    url: `/api/work/calendar`,
    method: 'GET',
    data
  })
}

// 新建工作日历
export function addProjectCalendar (data) {
  return request({
    url: `/api/work/calendar`,
    method: 'POST',
    data
  })
}

// 修改工作日历
export function updateProjectCalendar (data) {
  return request({
    url: `/api/work/calendar`,
    method: 'PUT',
    data
  })
}

// 获取日历工期列表
export function getCalendarWorkList (data) {
  return request({
    url: `/api/work/calendar/date`,
    method: 'GET',
    data
  })
}
// 修改日历排期
export function updateCalendarDate (data) {
  return request({
    url: `/api/work/calendar/date`,
    method: 'PUT',
    data
  })
}
// 添加日历排期
export function addCalendarDate (data) {
  return request({
    url: `/api/work/calendar/date`,
    method: 'POST',
    data
  })
}

// 修改日历排期(新)
export function updateCalendarSpecialDate (data) {
  return request({
    url: `/api/work/calendar/special/turn`,
    method: 'POST',
    data
  })
}


// 日历关联人员
export function calendarRelationUser (data) {
  return request({
    url: `/api/work/calendar/relation/user`,
    method: 'POST',
    data
  })
}
// 日历关联人员列表
export function getCalendarRelationUser (data) {
  return request({
    url: `/api/work/calendar/relation/user/${data.id}`,
    method: 'GET',
  })
}
// 删除日历关联人员
export function deleteCalendarRelationUser (data) {
  return request({
    url: `/api/work/calendar/relation/user`,
    method: 'DELETE',
    data
  })
}
// 删除工作日历
export function deleteWorkCalendar (id) {
  return request({
    url: `/api/work/calendar/${id}`,
    method: 'DELETE',
  })
}
// 删除工作排期
export function deleteWorkCalendarDate (id) {
  return request({
    url: `/api/work/calendar/date/${id}`,
    method: 'DELETE',
  })
}

// 操作特殊日期日历
export function calendarSpecial (data) {
  return request({
    url: `/api/work/calendar/special`,
    method: 'POST',
    data
  })
}

// 特殊日期日历列表
export function getCalendarSpecialList (id) {
  return request({
    url: `/api/work/calendar/special/${id}`,
    method: 'GET',
  })
}

// 获取日历详情
export function getCalendarDetails (id) {
  return request({
    url: `/api/work/calendar/${id}`,
    method: 'GET',
  })
}
