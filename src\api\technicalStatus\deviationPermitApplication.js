import request from '@/utils/request'

/**
 * @name 获取所有的偏离申请单
 * @param {*} param0.permitName 偏离单名称
 * @param {*} param0.permitCode 偏离单编号
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @returns 
 */
export const getPermitList = ({
  pageSize,
  currentPage,
  permitName,
  permitCode,
  typeCodeList,
  auditStatus,
  sort,
  sidx
}) => request({
  url: '/api/deviationPermit/getPermitList',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    permitName,
    permitCode,
    typeCodeList,
    auditStatus,
    sort,
    sidx
  }
})

/**
 * @name 保存偏离申请单
 * @param {*} param0.permitId 唯一ID 
 * @param {*} param0.permitCode 偏离单编号
 * @param {*} param0.permitName 偏离单名称
 * @param {*} param0.productCode 产品编号
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.batch 批次
 * @param {*} param0.unqualifiedLevel 偏离不合格级别
 * @param {*} param0.unqualifiedContent 偏离不合格内容
 * @param {*} param0.verificationResults 验证试验结果
 * @param {*} param0.delFlag
 * @param {*} param0.batchOrSortie 批次 货号有效性
 * @param {*} param0.startDay 开始时间
 * @param {*} param0.endDay 结束时间
 * @param {*} param0.deviationImpact 受影响范围
 * @param {*} param0.deviationReasons 偏离不合格原因code拼接
 * @param {*} param0.deviationReasonsCode 偏离不合格原因文字
 * @returns 
 */
export const savePermit = ({
  permitId,
  permitCode,
  permitName,
  productCode,
  productName,
  productVersion,
  batch,
  unqualifiedLevel,
  unqualifiedContent,
  verificationResults,
  delFlag,
  batchOrSortie,
  startDay,
  endDay,
  effectiveness,
  deviationImpact,
  deviationReasons,
  deviationReasonsCode,
  category,
  materialType
}) => request({
  url: '/api/deviationPermit/saveOrUpdatePermit',
  method: 'POST',
  data: {
    permitId,
    permitCode,
    permitName,
    productCode,
    productName,
    productVersion,
    batch,
    unqualifiedLevel,
    unqualifiedContent,
    verificationResults,
    delFlag,
    batchOrSortie,
    startDay,
    endDay,
    effectiveness,
    deviationImpact,
    deviationReasons,
    deviationReasonsCode,
    category,
    materialType
  }
})

/**
 * @name 编辑偏离申请单
 * @param {*} param0.permitId 唯一ID 
 * @param {*} param0.permitCode 偏离单编号
 * @param {*} param0.permitName 偏离单名称
 * @param {*} param0.productCode 产品编号
 * @param {*} param0.productName 产品名称
 * @param {*} param0.productVersion 产品版本
 * @param {*} param0.batch 批次
 * @param {*} param0.unqualifiedLevel 偏离不合格级别
 * @param {*} param0.unqualifiedContent 偏离不合格内容
 * @param {*} param0.verificationResults 验证试验结果
 * @param {*} param0.delFlag
 * @param {*} param0.batchOrSortie 批次 货号有效性
 * @param {*} param0.startDay 开始时间
 * @param {*} param0.endDay 结束时间
 * @param {*} param0.deviationImpact 受影响范围
 * @param {*} param0.deviationReasons 偏离不合格原因code拼接
 * @param {*} param0.deviationReasonsCode 偏离不合格原因文字
 * @returns 
 */
export const updatePermit = ({
  permitId,
  permitCode,
  permitName,
  productCode,
  productName,
  productVersion,
  batch,
  unqualifiedLevel,
  unqualifiedContent,
  verificationResults,
  delFlag,
  batchOrSortie,
  startDay,
  endDay,
  effectiveness,
  deviationImpact,
  deviationReasons,
  deviationReasonsCode,
  category,
  materialType
}) => request({
  url: '/api/deviationPermit/saveOrUpdatePermit',
  method: 'PUT',
  data: {
    permitId,
    permitCode,
    permitName,
    productCode,
    productName,
    productVersion,
    batch,
    unqualifiedLevel,
    unqualifiedContent,
    verificationResults,
    delFlag,
    batchOrSortie,
    startDay,
    endDay,
    effectiveness,
    deviationImpact,
    deviationReasons,
    deviationReasonsCode,
    category,
    materialType
  }
})

/**
 * @name 物料模糊查询
 * @param {*} param0.materialName 名称 
 * @returns 
 */
export const productModelConfig = ({
  materialName
}) => request({
  url: '/api/productModelConfig/getMaterialList',
  method: 'GET',
  data: {
    materialName
  }
})

/**
 * @name 偏离单详情
 * @param {*} param0.permitCode 偏离单code
 * @returns 
 */
export const agetPermitInfo = ({
  permitCode
}) => request({
  url: '/api/deviationPermit/getPermitInfo',
  method: 'GET',
  data: {
    permitCode
  }
})

// /**
//  * @name 偏离单保存受影响产品
//  * @param {*} param0.data.formCode 表单code
//  * @param {*} param0.data.objType 对象类型
//  * @param {*} param0.data.objName 对象名称
//  * @param {*} param0.data.objCode 对象code
//  * @param {*} param0.data.objVersion 对象版本
//  * @param {*} param0.data.totalNumber 数量
//  * @returns
//  */
// export const addProductPermitList = (data) => request({
//   url: '/api/deviationPermit/addProductPermitList',
//   method: 'POST',
//   data
// })