<template>
  <div class="popupSelect-container">
    <div @click="openDialog">
      <el-input :placeholder="'请选择'+popupTitle" v-model="title" readonly :validate-event="false"
        @mouseenter.native="inputHovering = true" @mouseleave.native="inputHovering = false">
        <template slot="suffix">
          <i v-show="!showClose"
            :class="['el-select__caret', 'el-input__icon', 'el-icon-arrow-down']"></i>
          <i v-if="showClose" class="el-select__caret el-input__icon el-icon-circle-close"
            @click.stop="clear"></i>
        </template>
        <el-button @click.stop="goDataInterface()" slot="append">
          添加</el-button>
      </el-input>
    </div>
    <el-dialog :title="popupTitle" :close-on-click-modal="false" :visible.sync="visible"
      class="JNPF-dialog JNPF-dialog_center JNPF-dialog-tree-select" lock-scroll append-to-body
      width="1000px">
      <div class="JNPF-common-layout">
        <div class="JNPF-common-layout-left">
          <el-scrollbar class="JNPF-common-el-tree-scrollbar" v-loading="treeLoading">
            <el-tree ref="treeBox" :data="treeData" :props="defaultProps" default-expand-all
              highlight-current :expand-on-click-node="false" node-key="id"
              @node-click="handleNodeClick" class="JNPF-common-el-tree" />
          </el-scrollbar>
        </div>
        <div class="JNPF-common-layout-center">
          <el-row class="JNPF-common-search-box" :gutter="16">
            <el-form @submit.native.prevent>
              <el-col :span="8">
                <el-form-item label="关键词">
                  <el-input v-model="query.keyword" placeholder="请输入关键词查询" clearable
                    @keyup.enter.native="search()" class="search-input" />
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="!dataType">
                <el-form-item label="类型">
                  <el-select v-model="query.dataType" placeholder="请选择" clearable>
                    <el-option label="静态数据" value="2"></el-option>
                    <el-option label="SQL操作" value="1"></el-option>
                    <el-option label="API操作" value="3"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="search()">查询</el-button>
                  <el-button icon="el-icon-refresh-right" @click="reset()">重置</el-button>
                </el-form-item>
              </el-col>
            </el-form>
            <div class="JNPF-common-search-box-right">
              <el-tooltip effect="dark" content="刷新" placement="top">
                <el-link icon="icon-ym icon-ym-Refresh JNPF-common-head-icon" :underline="false"
                  @click="initData()" />
              </el-tooltip>
            </div>
          </el-row>
          <div class="JNPF-common-layout-main JNPF-flex-main">
            <JNPF-table v-loading="listLoading" :data="list" :border="false" highlight-current-row
              @row-click="rowClick" :hasNO="false">
              <el-table-column width="35">
                <template slot-scope="scope">
                  <el-radio :label="scope.row.id" v-model="checked">&nbsp;</el-radio>
                </template>
              </el-table-column>
              <el-table-column type="expand" width="40">
                <template slot-scope="scope">
                  <el-table :data="scope.row.templateJson" size='mini'>
                    <el-table-column prop="field" label="参数名称">
                      <template slot-scope="scope">
                        <p>
                          <span class="required-sign">{{scope.row.required?'*':''}}</span>
                          {{scope.row.field}}{{scope.row.fieldName?'('+scope.row.fieldName+')':''}}
                        </p>
                      </template>
                    </el-table-column>
                    <el-table-column prop="dataType" label="参数类型" width="200">
                      <template slot-scope="scope">
                        <span v-if="scope.row.dataType === 'varchar'">字符串</span>
                        <span v-if="scope.row.dataType === 'int'">整型</span>
                        <span v-if="scope.row.dataType === 'datetime'">日期时间</span>
                        <span v-if="scope.row.dataType === 'decimal'">浮点</span>
                        <span v-if="scope.row.dataType === 'bigint'">长整型</span>
                        <span v-if="scope.row.dataType === 'text'">文本</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="defaultValue" label="默认值" width="200" />
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column type="index" width="50" label="序号" align="center" />
              <el-table-column prop="fullName" label="名称" />
              <el-table-column prop="enCode" label="编码" />
              <el-table-column prop="type" label="类型" width="100" />
            </JNPF-table>
            <pagination :total="total" :page.sync="listQuery.currentPage"
              :limit.sync="listQuery.pageSize" @pagination="initData" />
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">{{$t('common.cancelButton')}}</el-button>
        <el-button type="primary" @click="select()">{{$t('common.confirmButton')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDataInterfaceSelectorList } from '@/api/systemData/dataInterface'
export default {
  components: {},
  props: {
    value: {
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    dataType: {
      type: String,
      default: ''
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    popupTitle: {
      type: String,
      default: '接口模板'
    },
    /**
   * sourceType
   * 1 - 过滤掉：鉴权、真分页、SQL的增加、修改、删除类型
   * 2 - 过滤掉：鉴权、SQL的增加、修改、删除类型
   * 3 - 过滤掉：鉴权、真分页、SQL的查询类型
   */
    sourceType: { type: Number, default: 1 },
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  data() {
    return {
      list: [],
      innerValue: '',
      listQuery: {
        currentPage: 1,
        pageSize: 20,
        sort: 'desc',
        sidx: ''
      },
      total: 0,
      checked: '',
      checkedRow: {},
      listLoading: false,
      defaultProps: {
        children: 'children',
        label: 'fullName'
      },
      query: {
        category: '',
        keyword: '',
        dataType: null,
      },
      treeLoading: false,
      treeData: [],
      inputHovering: false,
      visible: false,
    }
  },
  computed: {
    showClose() {
      let hasValue = this.value !== undefined && this.value !== null && this.value !== '';
      let criteria = this.clearable &&
        !this.disabled &&
        this.inputHovering &&
        hasValue;
      return criteria;
    }
  },
  methods: {
    initData() {
      this.listLoading = true
      const query = {
        ...this.listQuery,
        ...this.query,
        type: !this.dataType ? this.query.dataType : this.dataType,
        sourceType: this.sourceType
      }
      this.$emit('clearValidate')
      getDataInterfaceSelectorList(query).then(res => {
        this.list = res.data.list.map(o => {
          let templateJson = o.parameterJson ? JSON.parse(o.parameterJson) : []
          if (!templateJson) templateJson = []
          let item = { templateJson, ...o }
          return item
        })
        this.total = res.data.pagination.total
        this.listLoading = false
      }).catch(() => { this.listLoading = false })
    },
    handleNodeClick(data) {
      if (this.query.category === data.id) return
      this.query.category = data.id
      this.query.sourceType = this.sourceType;
      this.reset()
    },
    reset() {
      this.query.keyword = ''
      this.query.dataType = null
      this.search()
    },
    search() {
      this.listQuery.currentPage = 1
      this.listQuery.pageSize = 20
      this.listQuery.sort = 'desc'
      this.initData()
    },
    openDialog() {
      if (this.disabled) return
      this.checked = this.value
      this.visible = true
      this.treeLoading = true
      this.listLoading = true
      this.$store.dispatch('base/getDictionaryData', { sort: 'DataInterfaceType' }).then((res) => {
        this.treeData = res
        if (!this.treeData.length) return this.treeLoading = false
        this.$nextTick(() => {
          this.query.category = this.treeData[0].id
          this.$refs.treeBox.setCurrentKey(this.query.category)
          this.treeLoading = false
          this.reset()
        })
      })
    },
    clear() {
      this.checked = ''
      this.checkedRow = {}
      this.$emit('input', this.checked)
      this.$emit('change', this.checked, this.checkedRow)
    },
    select() {
      if (!this.checked) return
      this.$emit('input', this.checked)
      this.$emit('change', this.checked, this.checkedRow)
      this.visible = false
    },
    rowClick(row) {
      this.checked = row.id
      this.checkedRow = row
    },
    goDataInterface() {
      let src = window.location.protocol + "//" + window.location.host + "/systemData/dataInterface"
      window.open(src, "_blank")
    }
  }
}
</script>