(function(a){ArrayBuffer.prototype.slice||(ArrayBuffer.prototype.slice=function(a,b){void 0===a&&(a=0);void 0===b&&(b=this.byteLength);a=Math.floor(a);b=Math.floor(b);0>a&&(a+=this.byteLength);0>b&&(b+=this.byteLength);a=Math.min(Math.max(0,a),this.byteLength);b=Math.min(Math.max(0,b),this.byteLength);if(0>=b-a)return new ArrayBuffer(0);var d=new ArrayBuffer(b-a),f=new Uint8Array(d),h=new Uint8Array(this,a,b-a);f.set(h);return d})})(this);var createPromiseCapability=function(){var a={};a.promise=new Promise(function(k,b){a.resolve=k;a.reject=b});return a};("undefined"===typeof window?this:window).createPromiseCapability=createPromiseCapability;
(function(a){function k(b){this._status=0;this._handlers=[];try{b.call(this,this._resolve.bind(this),this._reject.bind(this))}catch(a){this._reject(a)}}if(a.Promise)"function"!==typeof a.Promise.all&&(a.Promise.all=function(b){var d=0,k=[],q,l,u=new a.Promise(function(b,a){q=b;l=a});b.forEach(function(b,a){d++;b.then(function(b){k[a]=b;d--;0===d&&q(k)},l)});0===d&&q(k);return u}),"function"!==typeof a.Promise.resolve&&(a.Promise.resolve=function(b){return new a.Promise(function(a){a(b)})}),"function"!==
typeof a.Promise.reject&&(a.Promise.reject=function(b){return new a.Promise(function(a,d){d(b)})}),"function"!==typeof a.Promise.prototype["catch"]&&(a.Promise.prototype["catch"]=function(b){return a.Promise.prototype.then(void 0,b)});else{var b="function"===typeof setImmediate&&setImmediate||function(b){setTimeout(b,1)},d={handlers:[],running:!1,unhandledRejections:[],pendingRejectionCheck:!1,scheduleHandlers:function(a){0!==a._status&&(this.handlers=this.handlers.concat(a._handlers),a._handlers=
[],this.running||(this.running=!0,b(this.runHandlers.bind(this),0)))},runHandlers:function(){for(var a=Date.now()+1;0<this.handlers.length;){var d=this.handlers.shift(),k=d.thisPromise._status,q=d.thisPromise._value;try{1===k?"function"===typeof d.onResolve&&(q=d.onResolve(q)):"function"===typeof d.onReject&&(q=d.onReject(q),k=1,d.thisPromise._unhandledRejection&&this.removeUnhandeledRejection(d.thisPromise))}catch(l){k=2,q=l}d.nextPromise._updateStatus(k,q);if(Date.now()>=a)break}0<this.handlers.length?
b(this.runHandlers.bind(this),0):this.running=!1},addUnhandledRejection:function(b){this.unhandledRejections.push({promise:b,time:Date.now()});this.scheduleRejectionCheck()},removeUnhandeledRejection:function(b){b._unhandledRejection=!1;for(var a=0;a<this.unhandledRejections.length;a++)this.unhandledRejections[a].promise===b&&(this.unhandledRejections.splice(a),a--)},scheduleRejectionCheck:function(){this.pendingRejectionCheck||(this.pendingRejectionCheck=!0,setTimeout(function(){this.pendingRejectionCheck=
!1;for(var b=Date.now(),a=0;a<this.unhandledRejections.length;a++)if(500<b-this.unhandledRejections[a].time){var d=this.unhandledRejections[a].promise._value,k="Unhandled rejection: "+d;d.stack&&(k+="\n"+d.stack);warn(k);this.unhandledRejections.splice(a);a--}this.unhandledRejections.length&&this.scheduleRejectionCheck()}.bind(this),500))}};k.all=function(b){function a(g){2!==l._status&&(n=[],q(g))}var d,q,l=new k(function(g,e){d=g;q=e}),u=b.length,n=[];if(0===u)return d(n),l;for(var t=0,p=b.length;t<
p;++t){var r=b[t],g=function(g){return function(e){2!==l._status&&(n[g]=e,u--,0===u&&d(n))}}(t);k.isPromise(r)?r.then(g,a):g(r)}return l};k.isPromise=function(b){return b&&"function"===typeof b.then};k.resolve=function(b){return new k(function(a){a(b)})};k.reject=function(b){return new k(function(a,d){d(b)})};k.prototype={_status:null,_value:null,_handlers:null,_unhandledRejection:null,_updateStatus:function(b,a){1!==this._status&&2!==this._status&&(1===b&&k.isPromise(a)?a.then(this._updateStatus.bind(this,
1),this._updateStatus.bind(this,2)):(this._status=b,this._value=a,2===b&&0===this._handlers.length&&(this._unhandledRejection=!0,d.addUnhandledRejection(this)),d.scheduleHandlers(this)))},_resolve:function(b){this._updateStatus(1,b)},_reject:function(b){this._updateStatus(2,b)},then:function(b,a){var m=new k(function(b,a){this.resolve=b;this.reject=a});this._handlers.push({thisPromise:this,onResolve:b,onReject:a,nextPromise:m});d.scheduleHandlers(this);return m},"catch":function(b){return this.then(void 0,
b)}};a.Promise=k}})("undefined"===typeof window?this:window);(function(a,k){function b(c){"function"!==typeof c&&(c=new Function(""+c));for(var b=Array(arguments.length-1),a=0;a<b.length;a++)b[a]=arguments[a+1];t[n]={callback:c,args:b};g(n);return n++}function d(g){delete t[g]}function f(g){if(p)setTimeout(f,0,g);else{var c=t[g];if(c){p=!0;try{var b=c.callback,a=c.args;switch(a.length){case 0:b();break;case 1:b(a[0]);break;case 2:b(a[0],a[1]);break;case 3:b(a[0],a[1],a[2]);break;default:b.apply(k,a)}}finally{d(g),p=!1}}}}function h(){g=function(g){process.nextTick(function(){f(g)})}}
function m(){if(a.postMessage&&!a.importScripts){var g=!0,c=a.onmessage;a.onmessage=function(){g=!1};a.postMessage("","*");a.onmessage=c;return g}}function q(){var c="setImmediate$"+Math.random()+"$",b=function(g){g.source===a&&"string"===typeof g.data&&0===g.data.indexOf(c)&&f(+g.data.slice(c.length))};a.addEventListener?a.addEventListener("message",b,!1):a.attachEvent("onmessage",b);g=function(g){a.postMessage(c+g,"*")}}function l(){var c=r.documentElement;g=function(g){var b=r.createElement("script");
b.onreadystatechange=function(){f(g);b.onreadystatechange=null;c.removeChild(b);b=null};c.appendChild(b)}}function u(){g=function(g){setTimeout(f,0,g)}}if(!a.setImmediate){var n=1,t={},p=!1,r=a.document,g,c=Object.getPrototypeOf&&Object.getPrototypeOf(a),c=c&&c.setTimeout?c:a;"[object process]"==={}.toString.call(a.process)?h():m()?q():r&&"onreadystatechange"in r.createElement("script")?l():u();c.setImmediate=b;c.clearImmediate=d}})("undefined"===typeof self?"undefined"===typeof global?this:global:
self);(function(a){var k=function(b,a){var k=function(b){b=this["catch"](b);return{cancel:a,promise:b,then:h.bind(b),"catch":k.bind(b)}},h=function(b,q){var l=this.then(b,q);return{cancel:a,promise:l,then:h.bind(l),"catch":k.bind(l)}};return{cancel:a,promise:b,then:h.bind(b),"catch":k.bind(b)}};a.CancellablePromise=function(b,a){var f=!1,h,m=new Promise(function(k,l){h=function(){f||(a(),l("cancelled"))};(new Promise(b)).then(function(b){f=!0;k(b)},function(b){f=!0;l(b)})});return k(m,h)};a.CancellablePromise.all=
function(b){var a=Promise.all(b);return k(a,function(){b.forEach(function(b,a){b.cancel&&b.cancel()})})}})("undefined"===typeof self?this:self);(function(a){a.MessageHandler=function(k,b){this.name=k;this.comObj=b;this.callbackIndex=1;this.postMessageTransfers=!0;this.callbacksCapabilities={};var d=this.actionHandler={};this.actionHandlerAsync={};this.nextAsync=null;d.console_log=[function(b){console.log(b)}];d.console_error=[function(b){a.utils.error(b)}];d.workerLoaded=[function(b){}];b.addEventListener("message",this.handleMessage.bind(this))};a.MessageHandler.prototype={on:function(k,b,d){var f=this.actionHandler;f[k]&&a.utils.error('There is already an actionName called "'+
k+'"');f[k]=[b,d]},replace:function(a,b,d){this.actionHandler[a]=[b,d]},onAsync:function(k,b,d){var f=this.actionHandlerAsync;f[k]&&a.utils.error('There is already an actionName called "'+k+'"');f[k]=[b,d]},replaceAsync:function(a,b,d){var f=this.actionHandlerAsync,h=this.actionHandler;h[a]&&delete h[a];f[a]=[b,d]},onNextAsync:function(a){this.nextAsync=a},send:function(a,b){this.postMessage({action:a,data:b})},getNextId:function(){return this.callbackIndex++},sendWithPromise:function(a,b,d){var f=
this.getNextId();a={action:a,data:b,callbackId:f,priority:d};b=createPromiseCapability();this.callbacksCapabilities[f]=b;try{this.postMessage(a)}catch(h){b.reject(h)}return b.promise},sendWithPromiseReturnId:function(a,b,d){var f=this.getNextId();a={action:a,data:b,callbackId:f,priority:d};b=createPromiseCapability();this.callbacksCapabilities[f]=b;try{this.postMessage(a)}catch(h){b.reject(h)}return{promise:b.promise,callbackId:f}},sendWithPromiseWithId:function(k,b,d,f){b>this.callbackIndex&&a.utils.error("Can't reuse callbackId "+
b+" lesser than callbackIndex "+this.callbackIndex);b in this.callbacksCapabilities&&a.utils.error("Can't reuse callbackId "+b+". There is a capability waiting to be resolved. ");k={action:k,data:d,callbackId:b};d=createPromiseCapability();this.callbacksCapabilities[b]=d;try{this.postMessage(k)}catch(h){d.reject(h)}return d.promise},sendError:function(a,b){if(a.message||a.errorData){a.message&&a.message.message&&(a.message=a.message.message);var d=a.errorData;a={type:a.type?a.type:"JavascriptError",
message:a.message};if(d)for(var f in d)d.hasOwnProperty(f)&&(a[f]=d[f])}this.postMessage({isReply:!0,callbackId:b,error:a})},getPromise:function(k){if(k in this.callbacksCapabilities)return this.callbacksCapabilities[k];a.utils.error("Cannot get promise for callback "+k)},cancelPromise:function(k){if(k in this.callbacksCapabilities){var b=this.callbacksCapabilities[k];delete this.callbacksCapabilities[k];b.reject({type:"Cancelled",message:"Request has been cancelled."});this.postMessage({action:"actionCancel",
data:{callbackId:k}})}else a.utils.warn("Cannot cancel callback "+k)},postMessage:function(a){if(this.postMessageTransfers){var b=this.getTransfersArray(a);this.comObj.postMessage(a,b)}else this.comObj.postMessage(a)},getObjectTransfers:function(a,b){if("object"===typeof a)if(a instanceof Uint8Array)b.push(a.buffer);else if(a instanceof ArrayBuffer)b.push(a);else for(var d in a)a.hasOwnProperty(d)&&this.getObjectTransfers(a[d],b)},getTransfersArray:function(a){var b=[];this.getObjectTransfers(a,b);
return 0==b.length?void 0:b},handleMessage:function(k){var b=this,d=k.data,f=this.actionHandler,h=this.actionHandlerAsync;k=this.callbacksCapabilities;if(d.isReply)f=d.callbackId,f in k?(h=k[f],delete k[f],"error"in d?h.reject(d.error):h.resolve(d.data)):a.utils.warn("Cannot resolve callback "+f);else if(d.action in f){var m=f[d.action];d.callbackId?Promise.resolve().then(function(){return m[0].call(m[1],d.data)}).then(function(a){b.postMessage({isReply:!0,callbackId:d.callbackId,data:a})},function(a){b.sendError(a,
d.callbackId)}):m[0].call(m[1],d.data)}else d.action in h?(m=h[d.action],d.callbackId?m[0].call(m[1],d).then(function(a){b.postMessage({isReply:!0,callbackId:d.callbackId,data:a});b.nextAsync()},function(a){b.sendError(a,d.callbackId);b.nextAsync()}):m[0].call(m[1],d).then(function(a){b.nextAsync()},function(a){b.nextAsync()})):a.utils.error("Unknown action from worker: "+d.action)}}})("undefined"===typeof window?this:window);(function(a){var k=a._trnDebugMode,b=a._logFiltersEnabled?a._logFiltersEnabled:{};a.utils=a.utils?a.utils:{};a.utils.warn=function(a,f){f||(f=a,a="default");k&&b[a]&&console.warn(a+": "+f)};a.utils.log=function(a,f){f||(f=a,a="default");k&&b[a]&&console.log(a+": "+f)};a.utils.error=function(a){k&&console.error(a);throw Error(a);};a.info=function(a,b){};a.warn=function(b,f){a.utils.warn(b,f)};a.error=function(b){a.utils.error(b)}})("undefined"===typeof window?this:window);(function(a){a.Module={TOTAL_MEMORY:50331648,noExitRuntime:!0,devicePixelRatio:1,cur_doc:null,cache_ptr_size:0,has_buf_ownership:!0,loaded:!1,init_cb:null,cache_ptr:null,cleanupState:null,docs:{},postEvent:function(a,b,d){Module.workerMessageHandler.send("event",{docId:a,type:b,data:d})},postPagesUpdatedEvent:function(a,b,d){a={pageDimensions:Module.GetPageDimensions(a)};if(d)for(var f=0;f<d.length;++f)d[f]in a.pageDimensions?a.pageDimensions[d[f]].contentChanged=!0:console.warn("Invalid Page Number "+
d[f]);Module.postEvent(b,"pagesUpdated",a);return a},GetIndividualPageDimensions:function(a,b,d){a=Module.PageGetPageInfo(d);a.id=Module.PageGetId(d);return a},GetPageDimensionsRange:function(a,b,d){for(var f={},h=Module.PDFDocGetPageIterator(a,b);b<d&&Module.IteratorHasNext(h);++b){var m=Module.IteratorCurrent(h);f[b]=this.GetIndividualPageDimensions(a,b,m);Module.IteratorNext(h)}return f},GetPageDimensions:function(a){try{var b=Module.Runtime.stackSave(),d=Module.GetPageCount(a);if(0===d)throw"This document has no pages.";
return Module.GetPageDimensionsRange(a,1,d+1)}finally{Module.Runtime.stackRestore(b)}},loadDoc:function(a,b){"undefined"===typeof Module&&this._main();var d=null;try{var f=Module.Runtime.stackSave();b=Module.CreateDoc(a,b);var h=Module.GetDoc(b);if(Module.PDFDocInitSecurityHandler(h))return{docId:b,pageDimensions:Module.GetPageDimensions(h)};d={type:"NeedsPassword",errorData:{docId:b},message:"This document requires a password"}}catch(m){d={type:"InvalidPDF",message:m}}finally{Module.Runtime.stackRestore(f)}throw d;
},loadCanvas:function(a,b,d,f,h,m,q,l){return new Promise(function(u,n){var t=Module.GetDoc(a),p=b+1,r=function(){u(Module.RasterizePage(t,p,d,f,m,h,q,l))},g=Module.docs[a].chunkStorage;if(g){var c=Module.GetDownloadData(t).downloader,e=g.getRequiredChunkOffsetArrays(c,p);g.keepChunks(e.have);c=function(){var c=g.getChunks(e.missing);Module.loadPromise=c.then(function(){var g=Module.loadPromise.cancelled;Module.loadPromise=null;g||r()})["catch"](function(g){"cancelled"!==g?n(g):Module.loadPromise=
null})};Module.loadPromise?Module.loadPromise.then(c,c):c()}else r()})},loadResources:function(a,b){Module.Initialize(b);Module._TRN_PDFNetSetDefaultDiskCachingEnabled(!1);var d=new Uint8Array(a);Module.PDFNetSetResourceData(d)},_main:function(){enlargeMemory=function(){var a=Math.pow(2,31);if(DYNAMICTOP>=a)return!1;for(;TOTAL_MEMORY<=DYNAMICTOP;)if(TOTAL_MEMORY<a/2)TOTAL_MEMORY=alignMemoryPage(TOTAL_MEMORY+50331648),console.log("Enlarging Emscripten Heap To: "+TOTAL_MEMORY+" bytes");else{var b=TOTAL_MEMORY;
TOTAL_MEMORY=alignMemoryPage((3*TOTAL_MEMORY+a)/4);console.log("Enlarging Emscripten Heap To: "+TOTAL_MEMORY+" bytes");if(TOTAL_MEMORY<=b)return!1}TOTAL_MEMORY=Math.max(TOTAL_MEMORY,16777216);if(TOTAL_MEMORY>=a)return!1;try{if(ArrayBuffer.transfer)buffer=ArrayBuffer.transfer(buffer,TOTAL_MEMORY);else{var d=HEAP8;buffer=new ArrayBuffer(TOTAL_MEMORY)}}catch(f){return!1}if(!_emscripten_replace_memory(buffer))return!1;Module.buffer=buffer;Module.HEAP8=HEAP8=new Int8Array(buffer);Module.HEAP16=HEAP16=
new Int16Array(buffer);Module.HEAP32=HEAP32=new Int32Array(buffer);Module.HEAPU8=HEAPU8=new Uint8Array(buffer);Module.HEAPU16=HEAPU16=new Uint16Array(buffer);Module.HEAPU32=HEAPU32=new Uint32Array(buffer);Module.HEAPF32=HEAPF32=new Float32Array(buffer);Module.HEAPF64=HEAPF64=new Float64Array(buffer);ArrayBuffer.transfer||HEAP8.set(d);return!0};"undefined"===typeof Module&&(("undefined"!==typeof window?window:self).Module={});(function(a){a.PDFDocExportXFDF=function(a,d){var f=Module.GetDoc(a),h=Module.Runtime.stackSave(),
m;try{var q=d?Module.PDFDocFDFExtract(f,d):Module.PDFDocFDFExtract(f);m=Module.FDFDocSaveAsXFDF(q);Module.Runtime.stackRestore(h)}catch(l){throw Module.Runtime.stackRestore(h),l;}return m};a.PageArrayToPageSet=function(a){var d=Module.Runtime.stackSave(),f;try{f=Module.PageSetCreate();for(var h=0;h<a.length;++h)Module.PageSetAddPage(f,a[h]);Module.Runtime.stackRestore(d)}catch(m){throw Module.Runtime.stackRestore(d),m;}return f};a.cancelCurrent=function(){var a=Module.loadPromise;return a?(a.cancel(),
a.cancelled=!0):(a=Module.cleanupState)?(clearImmediate(a.timeout),a.cleanupArr.forEach(function(a){a()}),Module.cleanupState=null,!0):!1};a.SetWorkerRestartCallback=function(a){Module.workerRestartCallback=a};a.XFDFMerge=function(a,d){if(d){var f=[];try{var h=Module.GetUStringFromJSString(d,!0);f.push(function(){Module.UStringDestroy(h)});var m=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_FDFDocCreateFromXFDF(h,m));var q=Module.getValue(m,"i8*");f.push(function(){Module.FDFDocDestroy(q)});
REX(Module._TRN_PDFDocFDFUpdate(a,q))}finally{f.forEach(function(a){a()})}}};a.MergeXFDF=function(a,d){return new Promise(function(f,h){var m=[];try{var q=Module.Runtime.stackSave();m[m.length]=function(){Module.Runtime.stackRestore(q)};var l=Module.GetDoc(a);Module.XFDFMerge(l,d);m.forEach(function(a){a()});f({})}catch(k){m.forEach(function(a){a()}),h(k)}})};a.CreateBufferFile=function(a,d){Module.MakeDev(a);for(var f=new ArrayBuffer(d),f=new Uint8Array(f),h=0;h<d;++h)f[h]=255;Module.docs[a]={buffer:f}};
a.ReadBufferFile=function(a,d){var f=Module.docs[a].buffer;if(d){var h=new Uint8Array(f.buffer.slice());Module.docs[a].buffer=h}return f};a.RemoveBufferFile=function(a){Module.docs[a]=null};a.SaveHelper=function(a,d,f){f="undefined"===typeof f?2:f;Module.allocate(4,"i8",Module.ALLOC_STACK);Module.allocate(4,"i8",Module.ALLOC_STACK);Module.MakeDev(d);a=Module._TRN_PDFDocSave(a,Module.GetUStringFromJSString(d),f,0);Module.docs[d].sink=null;REX(a);return Module.docs[d].buffer.buffer};a.SaveDoc=function(a,
d,f,h,m){return new Promise(function(q,l){var k=Module.GetDoc(a),n=[];try{var k=Module.GetDoc(a),t=Module.Runtime.stackSave();n[n.length]=function(){Module.Runtime.stackRestore(t)};Module.XFDFMerge(k,d);for(var p=Module.PDFDocGetPageIterator(k,1);Module.IteratorHasNext(p);){var r=Module.IteratorCurrent(p);try{for(var g=Module.PageGetNumAnnots(r);0<g;){var c=Module.PageGetAnnot(r,--g);Module.AnnotHasAppearance(c)||Module.AnnotRefreshAppearance(c)}}catch(y){Module.ObjErase(r,"Annots")}Module.IteratorNext(p)}p=
0;if(h){var e=Module.PDFDocGetRoot(k);(p=Module.ObjFindObj(e,"OpenAction"))&&Module.ObjPut(e,"__OpenActionBackup__",p);var x=Module.ObjPutDict(e,"OpenAction");Module.ObjPutName(x,"Type","Action");Module.ObjPutName(x,"S","JavaScript");Module.ObjPutString(x,"JS","this.print()")}var w=Module.SaveHelper(k,a,m);h&&(p?Module.ObjPut(e,"OpenAction",Module.ObjFindObj(e,"__OpenActionBackup__")):Module.ObjErase(e,"OpenAction"));n.forEach(function(g){g()});if(f)q({fileData:w});else{var v=w.slice(0);q({fileData:v})}}catch(y){n.forEach(function(g){g()}),
l(y)}})};a.SaveDocFromFixedElements=function(a,d,f){a=Module.PDFDocCreateFromLayoutEls(a);a=Module.CreateDoc({type:"ptr",value:a});return Module.SaveDoc(a,d,!0,!1,f)};a.GetCurrentCanvasData=function(a){var d=Module.currentRenderData;if(!d)return null;a&&REX(Module._TRN_PDFRasterizerUpdateBuffer(d.rast));Date.now();return{pageBuf:Module.ReadBufferFile("b",a).buffer,pageWidth:d.out_width,pageHeight:d.out_height}};a.RasterizePage=function(a,d,f,h,m,q,l,k){return new Promise(function(n,t){Module.currentRenderData=
{};var p=Module.currentRenderData;p.out_width=parseInt(f);p.out_height=parseInt(h);var r=[];r.push(function(){Module.currentRenderData=null});try{var g=Module.Runtime.stackSave();r[r.length]=function(){Module.Runtime.stackRestore(g)};var c=Module.GetPage(a,d),e=Module.PageGetPageWidth(c),x=Module.PageGetPageHeight(c);p.stride=4*p.out_width;p.buf_size=p.out_width*p.out_height*4;p.rast=Module.PDFRasterizerCreate();r.push(function(){Module._TRN_PDFRasterizerDestroy(p.rast)});if(l){var w=Module.EMSCreateUpdatedLayersContext(a,
l);0!=w&&(Module._TRN_PDFRasterizerSetOCGContext(p.rast,w),r.push(function(){Module._TRN_OCGContextDestroy(w)}))}REX(Module._TRN_PDFRasterizerSetOverprint(p.rast,k));var v=Module.PageGetRotation(c),y=1==q||3==q,v=(1==v||3==v)!=y,A,B=Module.allocate(48,"i8",Module.ALLOC_STACK),z;if(m){m.x1=m[0];m.y1=m[1];m.x2=m[2];m.y2=m[3];var D=Module.PageGetDefaultMatrix(c,0),E=Module.Matrix2DInverse(D);m=Module.Matrix2DMultBBox(E,m);var C;m.x2<m.x1&&(C=m.x1,m.x1=m.x2,m.x2=C);m.y2<m.y1&&(C=m.y1,m.y1=m.y2,m.y2=C);
A=p.out_width/(v?m.y2-m.y1:m.x2-m.x1);z=Module.GetDefaultMatrixBox(c,m,q)}else z=Module.PageGetDefaultMatrix(c,q),A=p.out_width/(y?x:e);Module.Matrix2DSet(B,A,0,0,A,0,0);Module.Matrix2DConcat(B,z);r.push(function(){Module._free(p.bufPtr)});var F=Module.allocate(4,"i8",Module.ALLOC_STACK),H=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFRasterizerSetDrawAnnotations(p.rast,0));Module.CreateBufferFile("b",p.buf_size);r.push(function(){Module.RemoveBufferFile("b")});REX(Module._TRN_PDFRasterizerGetChunkRendererPath(p.rast,
c,Module.GetUStringFromJSString("b"),p.out_width,p.out_height,!0,B,0,0,0,F));var G=Module.getValue(F,"i8*");r.splice(1,0,function(){REX(Module._TRN_ChunkRendererDestroy(G))});(new Date).getTime();var I=function(){try{for(var g=0,c=(new Date).getTime(),e=!1;200>g;){REX(Module._TRN_ChunkRendererRenderNext(G,H));if(!Module.getValue(H,"i8*")){e=!0;break}g=(new Date).getTime()-c}if(e){var a=Module.GetCurrentCanvasData(!1);r.forEach(function(g){g()});n(a)}else Module.cleanupState.timeout=setImmediate(I,
0)}catch(b){r.forEach(function(g){g()}),t(b)}},K=setImmediate(I,0);Module.cleanupState={cleanupArr:r,timeout:K};r.push(function(){Module.cleanupState=null})}catch(J){r.forEach(function(g){g()}),t(J)}})};a.GetDestinationVPosHPos=function(a){var d=0,f=0,h=!1,m=!1,q=Module.DestinationGetPage(a),l=Module.DestinationGetExplicitDestObj(a),q=Module.PageGetDefaultMatrix(q),q=Module.Matrix2DInverse(q),k=Module.Matrix2DMult(q,{x:f,y:d}),f=k.x,d=k.y,n;try{switch(Module.DestinationGetFitType(a)){case 2:case 6:n=
Module.ObjGetAt(l,2);Module.ObjIsNumber(n)&&(d=Module.ObjGetNumber(n),h=!0);break;case 0:n=Module.ObjGetAt(l,2);Module.ObjIsNumber(n)&&(f=Module.ObjGetNumber(n),m=!0);n=Module.ObjGetAt(l,3);Module.ObjIsNumber(n)&&(d=Module.ObjGetNumber(n),h=!0);break;case 4:n=Module.ObjGetAt(l,2);Module.ObjIsNumber(n)&&(f=Module.ObjGetNumber(n),m=!0);n=Module.ObjGetAt(l,5);Module.ObjIsNumber(n)&&(d=Module.ObjGetNumber(n),h=!0);break;case 3:case 7:n=Module.ObjGetAt(l,2),Module.ObjIsNumber(n)&&(f=Module.ObjGetNumber(n),
m=!0)}k=Module.Matrix2DMult(q,{x:f,y:d});f=k.x;d=k.y;h||(d=0);m||(f=0)}catch(t){f=d=0}return{hpos:f,vpos:d}};a.FillBookmarkTree=function(a,d){for(var f=0;Module.BookmarkIsValid(a);a=Module.BookmarkGetNext(a),++f)try{var h=[];if(Module.BookmarkHasChildren(a)){var m=Module.BookmarkGetFirstChild(a);Module.FillBookmarkTree(m,h)}var q=Module.BookmarkGetTitle(a),h={children:h,name:q},l=Module.BookmarkGetAction(a);if(Module.ActionIsValid(l)){var k=Module.ActionGetType(l);if(0==k){var n=Module.ActionGetDest(l);
if(Module.DestinationIsValid(n)){var t=Module.DestinationGetPage(n);if(Module.PageIsValid(t)){var p=Module.PageGetIndex(t);if(0<p){h.pageNumber=p;var r=Module.GetDestinationVPosHPos(n);h.verticalOffset=r.vpos;h.horizontalOffset=r.hpos}}}}else if(5==k){var g=Module.ObjFindObj(l,"URI");g&&(h.url=Module.ObjGetAsPDFText(g))}}d[d.length]=h}catch(c){}};a.LoadBookmarks=function(a){a=Module.GetDoc(a);var d=[],f=Module.Runtime.stackSave();try{var h=Module.PDFDocGetFirstBookmark(a);Module.BookmarkIsValid(h)&&
Module.FillBookmarkTree(h,d)}catch(m){}Module.Runtime.stackRestore(f);return{bookmarks:d}};a.UpdatePassword=function(a,d){try{var f=Module.Runtime.stackSave(),h=Module.GetDoc(a);return Module.PDFDocInitStdSecurityHandler(h,d)?(h in downloadDataMap&&REX(Module._TRN_PDFDocDownloaderInitialize(h,downloadDataMap[h].downloader)),{success:!0,pageDimensions:Module.GetPageDimensions(h)}):{success:!1}}finally{Module.Runtime.stackRestore(f)}};a.GetTextData=function(a,d){return new Promise(function(f,h){var m=
d+1,q=Module.GetDoc(a),l=[];try{var k=Module.Runtime.stackSave();l[l.length]=function(){Module.Runtime.stackRestore(k)};var n=Module.GetPage(q,m),t=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_TextExtractorCreate(t));var p=Module.getValue(t,"i8*");l[l.length]=function(){_TRN_TextExtractorDestroy(p)};REX(Module._TRN_TextExtractorBegin(p,n,0,1));var r=Module.allocate(48,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetDefaultMatrix(n,!0,1,0,r));var g=Module.allocate(4,"i8",Module.ALLOC_STACK);
REX(Module._TRN_TextExtractorGetQuads(p,r,0,g));var c=Module.getValue(g,"i8*"),e=Module._malloc(8*c);l[l.length]=function(){Module._free(e)};REX(Module._TRN_TextExtractorGetQuads(p,r,e,g));var x=Module.GetJSDoubleArrFromCore(e,c),w=Module.allocate(4,"i8",Module.ALLOC_STACK),v=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_UStringCreate(v));l[l.length]=function(){Module._TRN_UStringDestroy(y)};var y=Module.getValue(v,"i8*");REX(Module._TRN_TextExtractorGetAsTextWithOffsets(p,y,0,w));var A=
Module.GetJSStringFromUString(y),B=Module.getValue(w,"i8*"),z=Module._malloc(4*B);l[l.length]=function(){Module._free(z)};REX(Module._TRN_TextExtractorGetAsTextWithOffsets(p,y,z,w));var D=Module.GetJSIntArrayFromCore(z,B),E=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_TextExtractorCmptSemanticInfo(p,r,0,E));var C=Module.getValue(E,"i8*"),F=Module._malloc(8*C);l[l.length]=function(){Module._free(F)};REX(Module._TRN_TextExtractorCmptSemanticInfo(p,r,F,E));var H=Module.GetJSDoubleArrFromCore(F,
C);l.forEach(function(g){g()});f({str:A,quads:x,offsets:D,struct:H})}catch(G){l.forEach(function(g){g()}),h(G)}})};a.InsertBlankPages=function(a,d,f,h){return new Promise(function(m,q){var l=[],k=Module.GetDoc(a);try{var n=Module.Runtime.stackSave();l[l.length]=function(){Module.Runtime.stackRestore(n)};for(var t=d.length-1;0<=t;--t){var p=Module.PDFDocGetPageIterator(k,d[t]),r=Module.PDFDocPageCreate(k,f,h);Module.PDFDocPageInsert(k,p,r)}var g=Module.postPagesUpdatedEvent(k,a);l.forEach(function(g){g()});
m(g)}catch(c){l.forEach(function(g){g()}),q(c)}})};a.InsertPages=function(a,d,f,h,m){return new Promise(function(k,l){var u=[],n=Module.GetDoc(a);try{var t=Module.Runtime.stackSave();u[u.length]=function(){Module.Runtime.stackRestore(t)};var p;if(d instanceof ArrayBuffer){var r=Module.CreateDoc(d);p=Module.GetDoc(r);u[u.length]=function(){Module.DeleteDoc(r)}}else p=Module.GetDoc(d);for(var g=f.length,c=Module.PageSetCreate(),e=0;e<g;++e)Module.PageSetAddPage(c,f[e]);Module.PDFDocInsertPages(n,h,
p,c,m);Module.GetPageDimensions(n);var x=Module.PageSetCreateRange(h,h+g);Module.PDFDocExportXFDF(a,x);var w=Module.postPagesUpdatedEvent(n,a);u.forEach(function(g){g()});k(w)}catch(v){u.forEach(function(g){g()}),l(v)}})};a.MovePages=function(a,d,f){return new Promise(function(h,m){var k=[],l=Module.GetDoc(a);try{var u=Module.Runtime.stackSave();k[k.length]=function(){Module.Runtime.stackRestore(u)};for(var n=d.length,t=Module.PageSetCreate(),p=0;p<n;++p)Module.PageSetAddPage(t,d[p]);Module.PDFDocMovePages(l,
f,t);var r=Module.postPagesUpdatedEvent(l,a);k.forEach(function(g){g()});h(r)}catch(g){k.forEach(function(g){g()}),m(g)}})};a.RemovePages=function(a,d){return new Promise(function(f,h){var m=Module.GetDoc(a),k=[];try{var l=Module.Runtime.stackSave();k[k.length]=function(){Module.Runtime.stackRestore(l)};for(var u=d.length-1;0<=u;--u){var n=Module.PDFDocGetPageIterator(m,d[u]);Module.IteratorHasNext(n)&&Module.PDFDocPageRemove(m,n)}var t=Module.postPagesUpdatedEvent(m,a);k.forEach(function(a){a()});
f(t)}catch(p){k.forEach(function(a){a()}),h(p)}})};a.RotatePages=function(a,d,f){return new Promise(function(h,m){var k=Module.GetDoc(a),l=[];try{var u=Module.Runtime.stackSave();l[l.length]=function(){Module.Runtime.stackRestore(u)};var n=d.length,t=0,p=Module.PDFDocGetPageIterator(k,d[0]),r=[];l.push(function(){Module._TRN_IteratorDestroy(p)});for(var g=d[0];Module.IteratorHasNext(p)&&t<d[n-1];++g){if(g===d[t]){var c=Module.IteratorCurrent(p),e=(Module.PageGetRotation(c)+f)%4;Module.PageSetRotation(c,
e);r.push(g);t++}Module.IteratorNext(p)}var x=Module.postPagesUpdatedEvent(k,a,r);l.forEach(function(g){g()});h(x)}catch(w){l.forEach(function(g){g()}),m(w)}})};a.ExtractPages=function(a,d,f){return new Promise(function(h,m){var k=[];try{var l=Module.Runtime.stackSave();k[k.length]=function(){Module.Runtime.stackRestore(l)};var u=function(a){k.forEach(function(a){a()});m(a)};Module.XFDFMerge(a,f);var n=Module.CreateEmptyDoc();k[k.length]=function(){Module.DeleteDoc(n)};var t=Module.InsertPages(n,
a,d,1,!0).then(function(){return Module.SaveDoc(n,void 0,!0,!1,void 0)}).then(function(a){k.forEach(function(a){a()});return a})["catch"](u);h(t)}catch(p){u(p)}})};a.CropPages=function(a,d,f,h,m,k){return new Promise(function(l,u){var n=Module.GetDoc(a),t=[];try{var p=Module.Runtime.stackSave();t[t.length]=function(){Module.Runtime.stackRestore(p)};var r=d.length,g=0,c=Module.PDFDocGetPageIterator(n,d[0]);t.push(function(){Module._TRN_IteratorDestroy(c)});for(var e=[],x=d[0];Module.IteratorHasNext(c)&&
g<d[r-1];++x){if(x===d[g]){var w=Module.IteratorCurrent(c),v=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetCropBox(w,v));var y=Module.PageGetRotation(w),A=Module.getValue(v,"double"),B=Module.getValue(v+8,"double"),z=Module.getValue(v+16,"double"),D=Module.getValue(v+24,"double");0===y%4?(Module.setValue(v,A+m,"double"),Module.setValue(v+8,B+h,"double"),Module.setValue(v+16,z-k,"double"),Module.setValue(v+24,D-f,"double")):1===y%4?(Module.setValue(v,A+f,"double"),Module.setValue(v+
8,B+m,"double"),Module.setValue(v+16,z-h,"double"),Module.setValue(v+24,D-k,"double")):2===y%4?(Module.setValue(v,A+k,"double"),Module.setValue(v+8,B+f,"double"),Module.setValue(v+16,z-m,"double"),Module.setValue(v+24,D-h,"double")):3===y%4&&(Module.setValue(v,A+h,"double"),Module.setValue(v+8,B+k,"double"),Module.setValue(v+16,z-f,"double"),Module.setValue(v+24,D-m,"double"));Module.setValue(v+32,0,"double");REX(Module._TRN_PageSetCropBox(w,v));e.push(x);g++}Module.IteratorNext(c)}var E=Module.postPagesUpdatedEvent(n,
a,e);t.forEach(function(g){g()});l(E)}catch(C){t.forEach(function(g){g()}),u(C)}})}})("undefined"===typeof self?this.Module:self.Module);this.loaded=!0;Module.init_cb&&Module.init_cb()}}})("undefined"===typeof self?this:self);var global="undefined"!==typeof window?window:self;"undefined"===typeof Module&&(global.Module={});var crawp=Module.crap;
(function(a){a.currentFileString="/current";var k=0,b=0,d={},f=null;Module.chunkMax=200;var h=function(g,c,a,b){var d=new XMLHttpRequest;return CancellablePromise(function(f,h){d.open("GET",g,!0);d.responseType="arraybuffer";d.onerror=function(){h("Network error occurred")};d.onload=function(g){206==this.status&&d.response.byteLength==a?(g=new Int8Array(d.response),f(g)):h("Download Failed")};var l=["bytes=",c,"-",c+a-1].join("");d.setRequestHeader("Range",l);if(b)for(var k in b)d.setRequestHeader(k,
b[k]);d.send()},function(){d.abort()})},m=function(g){this.file=g;this.position=0;this.length=g.size;this.reader=new FileReaderSync};m.prototype={read:function(g,a,e){e=this.position+e<=this.length?e:this.length-this.position;g=g.subarray(a,a+e);a=new Int8Array(this.reader.readAsArrayBuffer(this.file.slice(this.position,this.position+e)));g.set(a);this.position+=e;return e},seek:function(g){this.position=g},close:function(){this.reader=this.file=null},getPos:function(){return this.position},getTotalSize:function(){return this.length}};
var q=function(g){this.data=g;this.position=0;this.length=this.data.length};q.prototype={read:function(g,a,e){e=this.position+e<=this.length?e:this.length-this.position;g=g.subarray(a,a+e);a=this.data.subarray(this.position,this.position+e);g.set(a);this.position+=e;return e},write:function(g,a,e){e=this.position+e<=this.length?e:this.length-this.position;g=g.subarray(a,a+e);this.data.subarray(this.position,this.position+e).set(g);this.position+=e;return e},seek:function(g){this.position=g},close:function(){this.data=
null},getPos:function(){return this.position},getTotalSize:function(){return this.length}};var l=function(g,a,e,b){"object"===typeof g?(this.lruList=g.lruList,this.chunkMap=g.chunkMap,this.length=g.length,this.url=g.url,this.customHeaders=g.customHeaders):(this.lruList=[],this.chunkMap={},this.chunkMap[a]=b,this.length=a,this.url=g,this.customHeaders=e)};l.prototype={lruUpdate:function(g){var a=this.lruList.lastIndexOf(g);0<=a&&this.lruList.splice(a,1);this.lruList.push(g)},getChunk:function(g){if(this.chunkMap[g])this.lruUpdate(g);
else{var a=Math.min(g+524288,this.length)-1;Date.now();var e=new XMLHttpRequest;e.open("GET",this.url,!1);e.responseType="arraybuffer";e.setRequestHeader("Range",["bytes=",g,"-",a].join(""));if(this.customHeaders)for(var b in this.customHeaders)e.setRequestHeader(b,this.customHeaders[b]);e.send();if(200==e.status||206==e.status)this.writeChunk(new Int8Array(e.response),g);else throw Error("Failed to load data from");}return this.chunkMap[g]},hadChunk:function(g){return g in this.chunkMap},hasChunk:function(g){return this.chunkMap[g]},
getCacheData:function(){return this.chunkMap[this.length]},getRequiredChunkOffsetArrays:function(g,a){var e={have:[],downloading:[],missing:[]};try{var b=Module.Runtime.stackSave(),d=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderGetRequiredChunksSize(g,a,d));var f=Module.getValue(d,"i8*");if(f){var h=Module._malloc(4*f);REX(Module._TRN_DownloaderGetRequiredChunks(g,a,h,f));for(d=0;d<f;++d){var l=Module.getValue(h+4*d,"i8*");this.hasChunk(l)?e.have.push(l):this.hadChunk(l)?e.missing.push(l):
e.downloading.push(l)}}}finally{h&&Module._free(h),Module.Runtime.stackRestore(b)}return e},keepVisibleChunks:function(g,a){for(var e=a.length,b=Module.chunkMax/2,d=0,f=0;f<e;++f){var h=this.getRequiredChunkOffsetArrays(g,a[f]),l=h.have,k=l.length,d=d+k;if(d>b){this.keepChunks(l.slice(0,k-d+b));break}this.keepChunks(h.have)}},getChunkAsync:function(g){var a=this,e=g+524288,b=524288;e>this.length&&(b-=e-this.length);return h(this.url,g,b,this.customHeaders).then(function(e){a.writeChunk(e,g)})},getChunks:function(g){for(var a=
g.length,e=Array(a),b=0;b<a;++b)e[b]=this.getChunkAsync(g[b]);return CancellablePromise.all(e)},keepChunks:function(g){for(var a=g.length,e=0;e<a;++e)this.lruUpdate(g[e])},writeChunk:function(g,a,e){e=e?e:0;var b=this.chunkMap[a],d=g.length,f=this.lruList.length>=Module.chunkMax&&!b;524288!=d||g.buffer.byteLength!=d?(f?(f=this.lruList.shift(),b=this.chunkMap[f],524288>b.length&&(b=new Int8Array(524288)),this.chunkMap[f]=null):b=b?this.chunkMap[a]:new Int8Array(524288),b.subarray(e,e+d).set(g),g=b):
f&&(f=this.lruList.shift(),this.chunkMap[f]=null);this.lruUpdate(a);this.chunkMap[a]=g}};var u=function(g){this.chunkStorage=g;this.position=0;this.length=this.chunkStorage.length};u.prototype={read:function(g,a,e){var b=this.position+e<=this.length,d=b?e:this.length-this.position;if(this.position<this.length){var f;for(f=0;f<d;){var h=this.position%524288,l=this.position-h,k=d-f;if(this.chunkStorage.hadChunk(l)){var l=this.chunkStorage.getChunk(l),m=g.subarray(a+f,a+f+k),h=l.subarray(h,h+k);m.set(h);
h=h.length;f+=h;this.position+=h}else for(this.position+=k;f<d;++f)m[f]=0}}if(!b){m=a+d;if(e-=d)a=this.chunkStorage.getCacheData(),e>a.length&&(e=a.length),b=this.position-this.length,m=g.subarray(m,m+e),h=a.subarray(b,b+e),m.set(h);this.position+=e;return d+e}return d},write:function(g,a,e){var b=this.position+e<=this.length,d=this.position+e<=this.length?e:this.length-this.position,f=g.subarray(a,a+d),h=this.position%524288;this.chunkStorage.writeChunk(f,this.position-h,h);this.position+=d;if(!b){f=
a+d;if(e-=d)a=this.chunkStorage.getCacheData(),e>a.length&&(e=a.length),b=this.position-this.length,f=g.subarray(f,f+e),a.subarray(b,b+e).set(f);this.position+=e;return d+e}return d},seek:function(g){this.position=g},close:function(){this.chunkStorage=null},getPos:function(){return this.position},getTotalSize:function(){return this.length}};var n=function(g){this.docId=g;this.length=0;this.data=new Int8Array(8192);this.position=0};n.prototype={seek:function(g){this.position=g},close:function(){var g=
new Int8Array(this.data.buffer.slice(0,this.length));Module.ChangeDocBackend(this.docId,{ptr:Module.GetDoc(this.docId),buffer:g});this.data=null},getPos:function(){return this.position},getTotalSize:function(){return this.length},read:function(g,a,e){var b=this.data.length;e=e+a<b?e:b-a;g=g.subarray(a,a+e);a=this.data.subarray(this.position,this.position+e);g.set(a);this.position+=e;return e},write:function(a,c,e){for(var b=this.position+e,d=this.data.length;b>d;){var d=Math.max(d*(16777216<d?1.5:
2),b),f=new Int8Array(d);f.set(this.data.subarray(0,this.length),0);this.data=f}a=a.subarray(c,c+e);this.data.set(a,this.position);this.position+=e;this.position>this.length&&(this.length=this.position);return e}};var t={IsSink:function(a){return 66===(a.flags&255)},open:function(a){var c=a.path.slice(1);this.IsSink(a)?(a.provider=new n(c),Module.docs[c].sink=a.provider):a.provider=Module.docs[c].sink?new q(Module.docs[c].sink.data):Module.docs[c].chunkStorage?new u(Module.docs[c].chunkStorage):Module.docs[c].buffer?
new q(Module.docs[c].buffer):new m(Module.docs[c].file)},close:function(a){a.provider.close()},read:function(a,c,e,b,d){return a.provider.read(c,e,b)},llseek:function(a,c,e){a=a.provider;1===e?c+=a.getPos():2===e&&(c=a.getTotalSize()+c);if(0>c)throw new Module.FS.ErrnoError(Module.ERRNO_CODES.EINVAL);a.seek(c);return c},write:function(a,c,e,b,d){return b?a.provider.write(c,e,b):0}};global.THROW=function(a){throw{type:"InvalidPDF",message:a};};global.REX=function(a){a&&THROW(p(a))};var p=function(a){return"Exception: \n\t Message: "+
Module.Pointer_stringify(Module._TRN_GetMessage(a))+"\n\t Filename: "+Module.Pointer_stringify(Module._TRN_GetFileName(a))+"\n\t Function: "+Module.Pointer_stringify(Module._TRN_GetFunction(a))+"\n\t Linenumber: "+Module.Pointer_stringify(Module._TRN_GetLineNum(a))};a.GetErrToString=p;a.Initialize=function(a){var c=Module.Runtime.stackSave();a=a?Module.allocate(Module.intArrayFromString(a),"i8",Module.ALLOC_STACK):0;REX(Module._TRN_PDFNetInitialize(a));REX(Module._TRN_PDFNetSetColorManagement(2));
Module.Runtime.stackRestore(c)};a.GetDoc=function(a){if(a in Module.docs)return Module.docs[a].ptr;throw{type:"InvalidDocReference",message:"Unable to access Document id="+a+". The document appears to be invalid or was deleted."};};a.clearDocBackend=function(){null!==Module.cache_ptr?(Module.has_buf_ownership&&Module._free(Module.cache_ptr),Module.cache_ptr=null):Module.docs[a.currentFileString]&&delete Module.docs[a.currentFileString]};a.MakeDev=function(a){if(!d[a]){var c=FS.makedev(3,5);FS.registerDevice(c,
t);FS.mkdev(a,511,c);d[a]=!0}};a.CreateDocFileBackend=function(a,c){Module.MakeDev(c);var e=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.docs[c]={file:a};var b=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreateFromFilePath(b,e));e=Module.getValue(e,"i8*");Module.docs[c].ptr=e};a.InsertImageIntoDoc=function(a,c,e){var b=[];try{var d=Module.ElementBuilderCreate();b.push(function(){Module.ElementBuilderDestroy(d)});var f=Module.ElementWriterCreate();
b.push(function(){Module.ElementWriterDestroy(f)});var h,l;e?(h=e.width,l=e.height):(h=Module.ImageGetImageWidth(c),l=Module.ImageGetImageHeight(c),e=h/l,1<e?(h=612,l=parseInt(h/e)):(l=792,h=parseInt(l*e)));var k=Module.ElementBuilderCreateImage(d,c,0,0,h,l),m=Module.PDFDocPageCreate(a,h,l);Module.ElementWriterBegin(f,m);Module.ElementWriterWritePlacedElement(f,k);Module.ElementWriterEnd(f);Module.PDFDocPagePushBack(a,m)}finally{b.forEach(function(a){a()})}};var r=function(a,c,e){"object"===typeof a?
(this.m_pages=a.m_pages,this.m_has_named_dests=a.m_has_named_dests,this.m_finished_download=a.m_finished_download,this.m_has_outline=a.m_has_outline,this.m_current_page=a.m_current_page,this.m_id=a.m_id,this.size=a.size,this.timeout=a.timeout,this.eventPageArray=a.eventPageArray,this.requirePageCallbacks=a.requirePageCallbacks):(this.m_pages=[],this.m_has_outline=this.m_finished_download=this.m_has_named_dests=!1,this.m_current_page=1,this.m_id=e,this.size=a,this.timeout=null,this.eventPageArray=
[],this.requirePageCallbacks={});this.downloadUserData=Module.createDownloadUserData(c,e)};r.prototype={getJSUrl:function(){return Module.extractDownloadUserData(this.downloadUserData).url},getDocId:function(){return Module.extractDownloadUserData(this.downloadUserData).docId},destroyUserData:function(){this.m_id in Module.customHeadersMap&&delete Module.customHeadersMap[this.m_id];Module.destroyDownloadUserData(this.downloadUserData)}};a.createDownloadUserData=function(a,c){var e=Module.allocate(Module.intArrayFromString(a),
"i8",Module.ALLOC_NORMAL),b=Module.allocate(8,"i8",Module.ALLOC_NORMAL);Module.setValue(b,e,"i8*");Module.setValue(b+4,parseInt(c),"i32");return this.downloadUserData=b};a.extractDownloadUserData=function(a){var c=Module.getValue(a,"i8*"),c=Module.Pointer_stringify(c);a=Module.getValue(a+4,"i32").toString();return{url:c,docId:a}};a.destroyDownloadUserData=function(a){Module._free(Module.getValue(a,"i8*"));Module._free(a)};global.downloadDataMap={};Module.customHeadersMap={};a.GetDownloadData=function(a){if(a in
downloadDataMap)return downloadDataMap[a]};a.DownloaderHint=function(a,c){var e=Module.GetDoc(a),b=downloadDataMap[e];c.currentPage&&(b.m_current_page=c.currentPage);if(c.visiblePages){for(var d=c.visiblePages,f=0;f<d.length;++f)++d[f];for(var h in this.requirePageCallbacks)this.requirePageCallbacks.hasOwnProperty(h)&&d.push(parseInt(h));(f=Module.docs[a].chunkStorage)&&f.keepVisibleChunks(b.downloader,d);b=d.length;h=Module.allocate(4*b,"i8",Module.ALLOC_STACK);for(f=0;f<b;++f)Module.setValue(h+
4*f,d[f],"i32");REX(Module._TRN_PDFDocDownloadPages(e,h,b,1,0))}};a.RequirePage=function(a,c){return new Promise(function(e,b){var d=Module.GetDoc(a),f=downloadDataMap[d];f&&!f.m_pages[c]?(c in f.requirePageCallbacks?f.requirePageCallbacks[c].push(e):f.requirePageCallbacks[c]=[e],f=Module.allocate(4,"i8",Module.ALLOC_STACK),Module.setValue(f,c,"i32"),Module._TRN_PDFDocDownloadPages(d,f,1,0,0)):e()})};a.IsLinearizationValid=function(a){a=Module.GetDoc(a);if(a=downloadDataMap[a]){var c=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderIsLinearizationValid(a.downloader,c));return 0!==Module.getValue(c,"i8")}return!1};a.ShouldRunRender=function(a,c){var e=Module.GetDoc(a);return(e=downloadDataMap[e])?e.m_pages[c]:!0};a.createCallbacksStruct=function(a){if(!f){var c=function(a){return function(g,c,b,d){var f=arguments;g in downloadDataMap?a.apply(this,f):setImmediate(function(){g in downloadDataMap&&a.apply(this,f)},0)}};f={downloadProc:Runtime.addFunction(function(a,g,c,b,d){b=Module.extractDownloadUserData(b);
var f=b.docId;h(b.url,g,c,Module.customHeadersMap[f]).then(function(b){f in Module.docs&&Module.docs[f].chunkStorage&&Module.docs[f].chunkStorage.writeChunk(b,g);Module._TRN_DownloadComplete(0,g,c,a)})}),notifyUpdatePage:Runtime.addFunction(c(function(a,g,c,b){var d=downloadDataMap[a];d.m_pages[g]=!0;var f=d.eventPageArray;if(g in d.requirePageCallbacks)for(c=d.requirePageCallbacks[g],b=0;b<c.length;++b)c[b]();d.timeout?f.push(g):(f=d.eventPageArray=[g],d.timeout=setTimeout(function(){Module.postPagesUpdatedEvent(a,
d.m_id,f);d.timeout=null},100))})),notifyUpdateOutline:Runtime.addFunction(c(function(a,g){var c=downloadDataMap[a];c.m_has_outline||(c.m_has_outline=!0,Module.postEvent(c.m_id,"bookmarksUpdated",{}))})),notifyUpdateNamedDests:Runtime.addFunction(c(function(a,g){var c=downloadDataMap[a];c.m_has_named_dests||(c.m_has_named_dests=!0)})),notifyUpdateThumb:Runtime.addFunction(c(function(a,g){})),notifyFinishedDownload:Runtime.addFunction(c(function(a,g){var c=downloadDataMap[a];c.m_finished_download||
(c.m_finished_download=!0,Module.postEvent(c.m_id,"documentComplete",{}))})),notifyDocumentError:Runtime.addFunction(function(a,g){}),getCurrentPage:Runtime.addFunction(function(a,g){return downloadDataMap[a].m_current_page})}}c=Module.allocate(40,"i8",Module.ALLOC_STACK);Module.setValue(c,f.downloadProc,"i8*");Module.setValue(c+4,a,"i8*");Module.setValue(c+8,f.notifyUpdatePage,"i8*");Module.setValue(c+12,f.notifyUpdateOutline,"i8*");Module.setValue(c+16,f.notifyUpdateNamedDests,"i8*");Module.setValue(c+
20,f.notifyUpdateThumb,"i8*");Module.setValue(c+24,f.notifyFinishedDownload,"i8*");Module.setValue(c+28,f.notifyDocumentError,"i8*");Module.setValue(c+32,f.getCurrentPage,"i8*");Module.setValue(c+36,0,"i8*");return c};a.CreateDocDownloaderBackend=function(a,c,e){var b=a.url,d=a.size,f=a.customHeaders;Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_NORMAL);f&&(Module.customHeadersMap[e]=f);var h;h=a.downloadData?new r(a.downloadData,b,e,f):new r(a.size,b,e,f);var k=Module.createCallbacksStruct(h.downloadUserData),
m=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.MakeDev(e);a.chunkStorage?b=new l(a.chunkStorage):(a=new Int8Array(new ArrayBuffer(Math.ceil((a.size+524288-1)/524288/8))),b=new l(b,d,f,a));Module.docs[e]={chunkStorage:b};REX(Module._TRN_DownloaderCreate(k,d,Module.GetUStringFromJSString(e),m));h.downloader=Module.getValue(m,"i8*");if(d=Module._TRN_PDFDocCreateFromFilter(h.downloader,c))Module._TRN_FilterDestroy(h.downloader),REX(d);c=Module.getValue(c,"i8*");Module.docs[e].ptr=c;Module.PDFDocInitSecurityHandler(c)&&
REX(Module._TRN_PDFDocDownloaderInitialize(c,h.downloader));downloadDataMap[c]=h};a.CreateDocBackend=function(g,c){var e=g.value,b=g.extension,d=g.type,f=Module.allocate(4,"i8",Module.ALLOC_STACK),h=Module.Runtime.stackSave();try{if(e)if("ptr"===d)Module.docs[c]={ptr:e};else{var l,d=b&&"pdf"!=b;if("object"===typeof e&&e.url)a.CreateDocDownloaderBackend(e,f,c);else{var m=e instanceof ArrayBuffer,t=m?"buffer":"file";if(m&&(e=new Uint8Array(e),10485760>e.length+k&&!d)){k+=e.length;var p=e.length,n=Module._malloc(e.length);
Module.HEAPU8.set(e,n);REX(Module._TRN_PDFDocCreateFromBuffer(n,p,f));l=Module.getValue(f,"i8*");Module.docs[c]={ptr:l,bufPtr:n,bufPtrSize:p,ownership:!0};Module.docs[c].extension=b;return}Module.MakeDev(c);m={};m[t]=e;Module.docs[c]=m;if(d){var q;g.pageSizes&&g.pageSizes.length?q=g.pageSizes[0]:g.defaultPageSize&&(q=g.defaultPageSize);var u=Module.GetUStringFromJSString(c);REX(Module._TRN_PDFDocCreate(f));l=Module.getValue(f,"i8*");var r=Module.ImageCreateFromFile(l,u);Module.InsertImageIntoDoc(l,
r,q)}else{var G=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreateFromFilePath(G,f));l=Module.getValue(f,"i8*")}Module.docs[c].extension=b;Module.docs[c].ptr=l}}else REX(Module._TRN_PDFDocCreate(f)),l=Module.getValue(f,"i8*"),Module.docs[c]={ptr:l},Module.docs[c].extension=b}finally{Module.Runtime.stackRestore(h)}};a.ChangeDocBackend=function(g,c){var e=Module.docs[g];e?(e.bufPtr&&e.ownership&&(Module._free(e.bufPtr),k-=e.bufPtrSize),delete Module.docs[g]):
a.utils.warn("Trying to delete document "+g+" that does not exist.");Module.docs[g]=c};a.DeleteDoc=function(g){var c=Module.docs[g];c?(c.ptr&&(c.ptr in downloadDataMap&&(clearTimeout(downloadDataMap[c.ptr].timeout),downloadDataMap[c.ptr].destroyUserData(),delete downloadDataMap[c.ptr]),Module.PDFDocDestroy(c.ptr)),c.bufPtr&&c.ownership&&(Module._free(c.bufPtr),k-=c.bufPtrSize),delete Module.docs[g]):a.utils.warn("Trying to delete document "+g+" that does not exist.")};a.CreateDoc=function(g,c){if("id"===
g.type)return Module.docPtrStringToIdMap[g.value];if(!c){do c=(++b).toString();while(c in Module.docs)}Module.has_buf_ownership=!0;a.CreateDocBackend(g,c);return c};a.CreateEmptyDoc=function(){var a=(++b).toString(),c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreate(c));c=Module.getValue(c,"i8*");Module.docs[a]={ptr:c};return a};a.PDFDocCreateFromLayoutEls=function(a){var c=new Uint8Array(a);a=Module._malloc(c.length);var e=Module.Runtime.stackSave(),b=Module.allocate(4,"i8",
Module.ALLOC_STACK);Module.HEAPU8.set(c,a);c=Module._TRN_PDFDocCreateFromLayoutEls(a,c.length,b);b=Module.getValue(b,"i8*");Module._free(a);Module.Runtime.stackRestore(e);REX(c);return b};a.GetPageCount=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPageCount(a,c));return Module.getValue(c,"i8*")};a.GetPage=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPage(a,c,e));e=Module.getValue(e,"i8*");Module.PageIsValid(e)||THROW("Trying to access page that doesn't exist at index "+
c);return e};a.PageGetPageWidth=function(a){var c=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageWidth(a,1,c));return Module.getValue(c,"double")};a.PageGetPageHeight=function(a){var c=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageHeight(a,1,c));return Module.getValue(c,"double")};a.PageGetDefaultMatrix=function(a,c){var e=Module.allocate(48,"i8",Module.ALLOC_STACK);c||(c=0);REX(Module._TRN_PageGetDefaultMatrix(a,!0,1,c,e));return e};a.GetMatrixAsArray=
function(a){for(var c=[],e=0;6>e;++e)c[e]=Module.getValue(a+8*e,"double");return c};a.PageGetPageInfo=function(a){var c=Module.allocate(48,"i8",Module.ALLOC_STACK),e=Module.allocate(8,"i8",Module.ALLOC_STACK),b=Module.allocate(8,"i8",Module.ALLOC_STACK),d=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageInfo(a,!0,1,0,e,b,c,d));return{rotation:Module.getValue(d,"i8*"),width:Module.getValue(e,"double"),height:Module.getValue(b,"double"),matrix:Module.GetMatrixAsArray(c)}};a.GetUStringFromJSString=
function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK),b=2*(a.length+1),d=Module.allocate(b,"i8",c?Module.ALLOC_NORMAL:Module.ALLOC_STACK);Module.stringToUTF16(a,d,b);b=Module._TRN_UStringCreateFromString(d,e);c&&Module._free(d);REX(b);return Module.getValue(e,"i8*")};a.GetJSStringFromUString=function(a){var c=Module.allocate(4,"i16*",Module.ALLOC_STACK);REX(Module._TRN_UStringCStr(a,c));return Module.UTF16ToString(Module.getValue(c,"i16*"))};a.GetJSStringFromCString=function(a){return Module.Pointer_stringify(a)};
a.PDFNetSetResourceData=function(a){Module.res_ptr=Module._malloc(a.length);Module.HEAPU8.set(a,Module.res_ptr);REX(Module._TRN_PDFNetSetResourceData(Module.res_ptr,a.length));Module.res_ptr_size=a.length};a.PDFDocDestroy=function(a){REX(Module._TRN_PDFDocDestroy(a))};a.PDFRasterizerCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFRasterizerCreate(0,a));return Module.getValue(a,"i8*")};a.PageGetRotation=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);
REX(Module._TRN_PageGetRotation(a,c));return Module.getValue(c,"i8*")};a.PageGetId=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetSDFObj(a,c));c=Module.getValue(c,"i8*");a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetObjNum(c,a));a=Module.getValue(a,"i32");var e=Module.allocate(2,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetGenNum(c,e));e=Module.getValue(e,"i16");return a+"-"+e};a.PageSetRotation=function(a,c){REX(Module._TRN_PageSetRotation(a,
c))};a.GetDefaultMatrixBox=function(a,c,e){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetRotation(a,b));a=(Module.getValue(b,"i32")+e)%4;e=Module.allocate(48,"i8",Module.ALLOC_STACK);switch(a){case 0:return REX(Module._TRN_Matrix2DSet(e,1,0,0,-1,-c.x1,c.y2)),e;case 1:return REX(Module._TRN_Matrix2DSet(e,0,1,1,0,-c.y1,-c.x1)),e;case 2:return REX(Module._TRN_Matrix2DSet(e,-1,0,0,1,c.x2,-c.y1)),e;case 3:return REX(Module._TRN_Matrix2DSet(e,0,-1,-1,0,c.y2,c.x2)),e}throw Error("Yikes, we don't support that rotation type");
};a.Matrix2DMultBBox=function(a,c){var e=Module.allocate(8,"i8",Module.ALLOC_STACK),b=Module.allocate(8,"i8",Module.ALLOC_STACK);Module.setValue(e,c.x1,"double");Module.setValue(b,c.y1,"double");REX(Module._TRN_Matrix2DMult(a,e,b));c.x1=Module.getValue(e,"double");c.y1=Module.getValue(b,"double");Module.setValue(e,c.x2,"double");Module.setValue(b,c.y2,"double");REX(Module._TRN_Matrix2DMult(a,e,b));c.x2=Module.getValue(e,"double");c.y2=Module.getValue(b,"double");return c};a.Matrix2DMult=function(a,
c){var e=Module.allocate(8,"i8",Module.ALLOC_STACK),b=Module.allocate(8,"i8",Module.ALLOC_STACK);Module.setValue(e,c.x,"double");Module.setValue(b,c.y,"double");REX(Module._TRN_Matrix2DMult(a,e,b));c.x=Module.getValue(e,"double");c.y=Module.getValue(b,"double");return c};a.Matrix2DConcat=function(a,c){var e=Module.getValue(c,"double"),b=Module.getValue(c+8,"double"),d=Module.getValue(c+16,"double"),f=Module.getValue(c+24,"double"),h=Module.getValue(c+32,"double"),l=Module.getValue(c+40,"double");
REX(Module._TRN_Matrix2DConcat(a,e,b,d,f,h,l))};a.Matrix2DSet=function(a,c,e,b,d,f,h){REX(Module._TRN_Matrix2DSet(a,c,e,b,d,f,h))};a.IteratorHasNext=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_IteratorHasNext(a,c));return 0!=Module.getValue(c,"i8")};a.IteratorCurrent=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_IteratorCurrent(a,c));return Module.getValue(Module.getValue(c,"i8*"),"i8*")};a.IteratorNext=function(a){REX(Module._TRN_IteratorNext(a))};
a.PageGetNumAnnots=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetNumAnnots(a,c));return Module.getValue(c,"i32")};a.PageGetAnnot=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetAnnot(a,c,e));return Module.getValue(e,"i8*")};a.PageAnnotRemove=function(a,c){REX(Module._TRN_PageAnnotRemoveByIndex(a,c))};a.AnnotGetType=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_AnnotGetType(a,c));return Module.getValue(c,
"i32")};a.AnnotHasAppearance=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_AnnotGetAppearance(a,0,0,c));return 0!=Module.getValue(c,"i8*")};a.AnnotRefreshAppearance=function(a){REX(Module._TRN_AnnotRefreshAppearance(a))};a.ObjErase=function(a,c){var e=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjEraseFromKey(a,e))};a.GetJSDoubleArrFromCore=function(a,c){for(var e=Array(c),b=a,d=0;d<c;++d)e[d]=Module.getValue(b,"double"),b+=
8;return e};a.GetJSIntArrayFromCore=function(a,c){for(var e=Array(c),b=a,d=0;d<c;++d)e[d]=Module.getValue(b,"i32"),b+=4;return e};a.BookmarkIsValid=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkIsValid(a,c));return 0!=Module.getValue(c,"i8")};a.BookmarkGetNext=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetNext(a,c));return Module.getValue(c,"i8*")};a.BookmarkGetFirstChild=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);
REX(Module._TRN_BookmarkGetFirstChild(a,c));return Module.getValue(c,"i8*")};a.BookmarkHasChildren=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkHasChildren(a,c));return 0!=Module.getValue(c,"i8")};a.BookmarkGetAction=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetAction(a,c));return Module.getValue(c,"i8*")};a.BookmarkGetTitle=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetTitle(a,
c));a=Module.getValue(c,"i8*");return Module.GetJSStringFromUString(a)};a.ActionIsValid=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionIsValid(a,c));return 0!=Module.getValue(c,"i8")};a.ActionGetType=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionGetType(a,c));return Module.getValue(c,"i32")};a.ActionGetDest=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionGetDest(a,c));return Module.getValue(c,
"i8*")};a.DestinationIsValid=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationIsValid(a,c));return 0!=Module.getValue(c,"i8")};a.DestinationGetPage=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetPage(a,c));return Module.getValue(c,"i8*")};a.PageIsValid=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageIsValid(a,c));return 0!=Module.getValue(c,"i8")};a.PageGetIndex=function(a){var c=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetIndex(a,c));return Module.getValue(c,"i32")};a.ObjGetAsPDFText=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetAsPDFText(a,c));a=Module.getValue(c,"i8*");return Module.GetJSStringFromUString(a)};a.ObjFindObj=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK),b=allocate(intArrayFromString(c),"i8",ALLOC_STACK);REX(Module._TRN_ObjFindObj(a,b,e));return Module.getValue(e,"i8*")};a.PDFDocGetFirstBookmark=function(a){var c=
Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetFirstBookmark(a,c));return Module.getValue(c,"i8*")};a.DestinationGetExplicitDestObj=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetExplicitDestObj(a,c));return Module.getValue(c,"i8*")};a.DestinationGetFitType=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetFitType(a,c));return Module.getValue(c,"i32")};a.ObjIsNumber=function(a){var c=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjIsNumber(a,c));return 0!=Module.getValue(c,"i8")};a.ObjGetNumber=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetNumber(a,c));return Module.getValue(c,"double")};a.PDFDocGetRoot=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetRoot(a,c));return Module.getValue(c,"i8*")};a.ObjPutName=function(a,c,e){c=allocate(intArrayFromString(c),"i8",ALLOC_STACK);e=allocate(intArrayFromString(e),"i8",
ALLOC_STACK);var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPutName(a,c,e,b));return Module.getValue(b,"i8*")};a.ObjPutDict=function(a,c){var e=allocate(intArrayFromString(c),"i8",ALLOC_STACK),b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPutDict(a,e,b));return Module.getValue(b,"i8*")};a.ObjPutString=function(a,c,e){c=allocate(intArrayFromString(c),"i8",ALLOC_STACK);e=allocate(intArrayFromString(e),"i8",ALLOC_STACK);var b=Module.allocate(4,"i8",Module.ALLOC_STACK);
REX(Module._TRN_ObjPutString(a,c,e,b));return Module.getValue(b,"i8*")};a.ObjPut=function(a,c,e){c=allocate(intArrayFromString(c),"i8",ALLOC_STACK);var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPut(a,c,e,b));return Module.getValue(b,"i8*")};a.ObjGetAt=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetAt(a,c,e));return Module.getValue(e,"i8*")};a.Matrix2DInverse=function(a){var c=Module.allocate(48,"i8",Module.ALLOC_STACK);REX(Module._TRN_Matrix2DInverse(a,
c));return c};a.PDFDocInitSecurityHandler=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocInitSecurityHandler(a,0,c));return 0!=Module.getValue(c,"i8")};a.PDFDocInitStdSecurityHandler=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK),b=Module.intArrayFromString(c),d=Module.allocate(b,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocInitStdSecurityHandler(a,d,b.length-1,e));return 0!=Module.getValue(e,"i8")};a.PDFDocFDFExtract=function(a,c){var e=Module.allocate(4,
"i8",Module.ALLOC_STACK);c?REX(Module._TRN_PDFDocFDFExtractPageSet(a,c,2,e)):REX(Module._TRN_PDFDocFDFExtract(a,2,e));return Module.getValue(e,"i8*")};a.PDFDocInsertPages=function(a,c,e,b,d){REX(Module._TRN_PDFDocInsertPageSet(a,c,e,b,d?1:0,0))};a.PDFDocMovePages=function(a,c,e){REX(Module._TRN_PDFDocMovePageSet(a,c,a,e,0,0))};a.PDFDocGetPageIterator=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPageIterator(a,c,e));return Module.getValue(e,"i8*")};a.PDFDocPageRemove=
function(a,c){REX(Module._TRN_PDFDocPageRemove(a,c))};a.PDFDocPageCreate=function(a,c,e){var b=Module.allocate(40,"i8",Module.ALLOC_STACK);Module.setValue(b,0,"double");Module.setValue(b+8,0,"double");Module.setValue(b+16,c,"double");Module.setValue(b+24,e,"double");Module.setValue(b+32,0,"double");c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocPageCreate(a,b,c));return Module.getValue(c,"i8*")};a.PDFDocPageInsert=function(a,c,e){REX(Module._TRN_PDFDocPageInsert(a,c,e))};a.PageSetCreate=
function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageSetCreate(a));return Module.getValue(a,"i8*")};a.PageSetCreateRange=function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageSetCreateRange(e,a,c));return Module.getValue(e,"i8*")};a.PageSetAddPage=function(a,c){REX(Module._TRN_PageSetAddPage(a,c))};a.FDFDocSaveAsXFDF=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(_TRN_FDFDocSaveAsXFDFAsString(a,c));a=Module.getValue(c,"i8*");
return Module.GetJSStringFromUString(a)};a.ElementBuilderCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementBuilderCreate(a));return Module.getValue(a,"i8*")};a.ElementBuilderDestroy=function(a){REX(Module._TRN_ElementBuilderDestroy(a))};a.ElementBuilderCreateImage=function(a,c,e,b,d,f){var h=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementBuilderCreateImageScaled(a,c,e,b,d,f,h));return Module.getValue(h,"i8*")};a.ElementWriterCreate=function(){var a=
Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementWriterCreate(a));return Module.getValue(a,"i8*")};a.ElementWriterDestroy=function(a){REX(Module._TRN_ElementWriterDestroy(a))};a.ElementWriterBegin=function(a,c){REX(Module._TRN_ElementWriterBeginOnPage(a,c,1))};a.ElementWriterWritePlacedElement=function(a,c){REX(Module._TRN_ElementWriterWritePlacedElement(a,c))};a.ElementWriterEnd=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementWriterEnd(a,c))};
a.ImageGetImageWidth=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageGetImageWidth(a,c));return Module.getValue(c,"i32")};a.ImageGetImageHeight=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageGetImageHeight(a,c));return Module.getValue(c,"i32")};a.ImageCreateFromMemory2=function(a,c,e){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageCreateFromMemory2(a,c,e,0,b));return Module.getValue(b,"i8*")};a.ImageCreateFromFile=
function(a,c){var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageCreateFromFile(a,c,0,e));return Module.getValue(e,"i8*")};a.PDFDocPagePushBack=function(a,c){REX(Module._TRN_PDFDocPagePushBack(a,c))};a.PDFDocHasOC=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocHasOC(a,c));return 0!=Module.getValue(c,"i8")};a.PDFDocGetOCGConfig=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetOCGConfig(a,c));return Module.getValue(c,
"i8*")};a.OCGContextCreate=function(a){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_OCGContextCreateFromConfig(a,c));return Module.getValue(c,"i8*")};a.UStringDestroy=function(a){REX(Module._TRN_UStringDestroy(a))};a.FDFDocDestroy=function(a){REX(Module._TRN_FDFDocDestroy(a))}})(global.Module);!function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var k;"undefined"!=typeof window?k=window:"undefined"!=typeof global?k=global:"undefined"!=typeof self&&(k=self);k.PriorityQueue=a()}}(function(){return function k(b,d,f){function h(l,q){if(!d[l]){if(!b[l]){var n="function"==typeof require&&require;if(!q&&n)return n(l,!0);if(m)return m(l,!0);n=Error("Cannot find module '"+l+"'");throw n.code="MODULE_NOT_FOUND",
n;}n=d[l]={exports:{}};b[l][0].call(n.exports,function(d){var f=b[l][1][d];return h(f?f:d)},n,n.exports,k,b,d,f)}return d[l].exports}for(var m="function"==typeof require&&require,q=0;q<f.length;q++)h(f[q]);return h}({1:[function(k,b,d){var f={}.hasOwnProperty,h=function(b,d){function h(){this.constructor=b}for(var k in d)f.call(d,k)&&(b[k]=d[k]);h.prototype=d.prototype;b.prototype=new h;b.__super__=d.prototype;return b};d=k("./PriorityQueue/AbstractPriorityQueue");k=k("./PriorityQueue/ArrayStrategy");
d=function(b){function d(b){b||(b={});b.strategy||(b.strategy=BinaryHeapStrategy);b.comparator||(b.comparator=function(b,d){return(b||0)-(d||0)});d.__super__.constructor.call(this,b)}h(d,b);return d}(d);d.ArrayStrategy=k;b.exports=d},{"./PriorityQueue/AbstractPriorityQueue":2,"./PriorityQueue/ArrayStrategy":3}],2:[function(k,b,d){b.exports=function(){function b(d){if(null==(null!=d?d.strategy:void 0))throw"Must pass options.strategy, a strategy";if(null==(null!=d?d.comparator:void 0))throw"Must pass options.comparator, a comparator";
this.priv=new d.strategy(d);this.length=0}b.prototype.queue=function(b){this.length++;this.priv.queue(b)};b.prototype.dequeue=function(b){if(!this.length)throw"Empty queue";this.length--;return this.priv.dequeue()};b.prototype.peek=function(b){if(!this.length)throw"Empty queue";return this.priv.peek()};b.prototype.remove=function(b){this.priv.remove(b)&&--this.length};b.prototype.find=function(b){return 0<=this.priv.find(b)};b.prototype.removeAllMatching=function(b,d){var f=this.priv.removeAllMatching(b,
d);this.length-=f};return b}()},{}],3:[function(k,b,d){var f;f=function(b,d,f){var l,k,n;k=0;for(l=b.length;k<l;)n=k+l>>>1,0<=f(b[n],d)?k=n+1:l=n;return k};b.exports=function(){function b(d){var f;this.options=d;this.comparator=this.options.comparator;this.data=(null!=(f=this.options.initialValues)?f.slice(0):void 0)||[];this.data.sort(this.comparator).reverse()}b.prototype.queue=function(b){var d;d=f(this.data,b,this.comparator);this.data.splice(d,0,b)};b.prototype.dequeue=function(){return this.data.pop()};
b.prototype.peek=function(){return this.data[this.data.length-1]};b.prototype.find=function(b){var d=f(this.data,b,this.comparator)-1;return 0<=d&&!this.comparator(this.data[d],b)?d:-1};b.prototype.remove=function(b){b=this.find(b);return 0<=b?(this.data.splice(b,1),!0):!1};b.prototype.removeAllMatching=function(b,d){for(var f=0,h=this.data.length-1;0<=h;--h)if(b(this.data[h])){var k=this.data.splice(h,1)[0];d&&d(k);++f}return f};return b}()},{}]},{},[1])(1)});(function(a){a.SetupPDFNetFunctions=function(k){Module._IB_=[];for(var b=function(a){if("object"===typeof a&&null!==a)if("undefined"!==typeof a.byteLength){var d=Module._IB_.length;Module._IB_[d]=new Uint8Array(a);a={handle:d,isArrayBufferRef:!0}}else for(prop in a)a.hasOwnProperty(prop)&&(a[prop]=b(a[prop]));return a},d=function(a){if("object"===typeof a&&null!==a)if(a.buffer)a=a.buffer.slice(a.byteOffset,a.byteOffset+a.length);else if(a.isArrayBufferRef)a=Module._IB_[a.handle].buffer;else for(prop in a)a.hasOwnProperty(prop)&&
(a[prop]=d(a[prop]));return a},f=Module._TRN_EMSCreateSharedWorkerInstance(),h,m=Module._TRN_EMSWorkerInstanceGetFunctionIterator(f),q=function(a,h){return new Promise(function(l,g){a=b(a);var c=a.docId,c=c?Module.GetDoc(c):0;(c=Module.EMSCallSharedFunction(f,h,c))?g({type:"InvalidPDF",message:Module.GetErrToString(c)}):(c=Module.EMSGetLastResponse(f),c=d(c),l(c));Module._IB_=[]})};h=Module._TRN_EMSFunctionIteratorGetNextCommandName(m);)h=Module.GetJSStringFromCString(h),a.queue.onAsync(h,q);Module._TRN_EMSFunctionIteratorDestroy(m);
if(Module._TRN_EMSCreatePDFNetWorkerInstance){var l={},m=function(a,b){k.on(a,b);l[a]=!0};Module.docPtrStringToIdMap={};var u=function(a){if(a in Module.docPtrStringToIdMap)return Module.docPtrStringToIdMap[a];throw Error("Couldn't find document "+a);};a.queue.onAsync("PDFDoc.RequirePage",function(a,b){return Module.RequirePage(u(a.docId),a.pageNum)});m("pdfDocCreateFromBuffer",function(a){a=Module.CreateDoc({type:"array",value:a.buf});var b=Module.GetDoc(a).toString(16);Module.docPtrStringToIdMap[b]=
a;return b});m("PDFDoc.destroy",function(a){a=u(a.auto_dealloc_obj);Module.DeleteDoc(a)});m("PDFDoc.saveMemoryBuffer",function(a){var b=u(a.doc);return Module.SaveHelper(Module.GetDoc(b),b,a.flags).slice(0)});m("pdfDocCreate",function(a){a=Module.CreateDoc({type:"new"});var b=Module.GetDoc(a).toString(16);Module.docPtrStringToIdMap[b]=a;return b});m("GetPDFDoc",function(a){a=a.docId;var b=Module.GetDoc(a).toString(16);Module.docPtrStringToIdMap[b]=a;return b});m("ExtractPDFNetLayersContext",function(a){var b=
a.layers;a=Module.GetDoc(a.docId);var d=0;b?d=Module.EMSCreateUpdatedLayersContext(a,b):Module.PDFDocHasOC(a)&&(b=Module.PDFDocGetOCGConfig(a),d=Module.OCGContextCreate(b));return d.toString(16)});for(var n=Module._TRN_EMSCreatePDFNetWorkerInstance(),m=Module._TRN_EMSWorkerInstanceGetFunctionIterator(n),q=function(a){return new Promise(function(f,h){a=b(a);var g=Module.EMSCallPDFNetFunction(n,a);g?h({type:"InvalidPDF",message:Module.GetErrToString(g)}):(g=Module.EMSGetLastResponse(n),g=d(g),f(g))})};h=
Module._TRN_EMSFunctionIteratorGetNextCommandName(m);)if(h=Module.GetJSStringFromCString(h),!l[h])k.onAsync(h,q);Module._TRN_EMSFunctionIteratorDestroy(m)}}})("undefined"===typeof self?this:self);(function(a){a.instantiateCachedURL=function(k,b,d,f){function h(a){return new Promise(function(d,f){var h=a.transaction(["wasm-cache"]).objectStore("wasm-cache").get(b);h.onsuccess=function(a){h.result?d(h.result):f("Module "+b+" was not found in wasm cache")};h.onerror=f.bind(null,"Error getting wasm module "+b)})}function m(a,d){var f=a.transaction(["wasm-cache"],"readwrite").objectStore("wasm-cache").put(d,b);f.onerror=function(a){console.log("Failed to store in wasm cache: "+a)};f.onsuccess=
function(a){console.log("Successfully stored "+b+" in wasm cache")}}function q(){return a.loadURLWithBrotliPriority(b,f,!0,!0).then(function(a){Date.now();return WebAssembly.instantiate(a,d)})}return function(){return new Promise(function(a,b){var d=indexedDB.open("wasm-cache",k);d.onerror=b.bind(null,"Error opening wasm cache database");d.onsuccess=function(){a(d.result)};d.onupgradeneeded=function(a){var b=d.result;b.objectStoreNames.contains("wasm-cache")&&(console.log("Clearing out version "+
a.oldVersion+" wasm cache"),b.deleteObjectStore("wasm-cache"));console.log("Creating version "+a.newVersion+" wasm cache");b.createObjectStore("wasm-cache")}})}().then(function(a){return h(a).then(function(a){return WebAssembly.instantiate(a,d)},function(b){return q().then(function(b){try{m(a,b.module)}catch(d){}return b.instance})})},function(a){console.log(a);return q().then(function(a){return a.instance})})}})(this);(function(a){var k=function(a){if("string"===typeof a){for(var b=new Uint8Array(a.length),e=a.length,d=0;d<e;d++)b[d]=a.charCodeAt(d);return b}return a},b=function(a){if("string"!==typeof a){for(var b="",e=0,d=a.length,f;e<d;)f=a.subarray(e,e+1024),e+=1024,b+=String.fromCharCode.apply(null,f);return b}return a},d=!1,f=function(f,c){d||(importScripts(a.basePath+"external/decode.min.js"),d=!0);var e=self.BrotliDecode(k(f));return c?e:b(e)},h=!1,m=function(d,c){h||(importScripts(a.basePath+"external/rawinflate.js",
a.basePath+"external/pako_inflate.min.js"),h=!0);var e=10;if("string"===typeof d){if(d.charCodeAt(3)&8){for(;0!==d.charCodeAt(e);++e);++e}}else if(d[3]&8){for(;0!==d[e];++e);++e}if(c)return d=k(d),d=d.subarray(e,d.length-8),a.pako.inflate(d,{windowBits:-15});d=b(d);d=d.substring(e,d.length-8);return a.RawDeflate.inflate(d)},q=function(a,c){return c?a:b(result)},l=function(a){var c=!a.shouldOutputArray,e=new XMLHttpRequest;e.open("GET",a.url,a.isAsync);var d=c&&e.overrideMimeType;e.responseType=d?
"text":"arraybuffer";d&&e.overrideMimeType("text/plain; charset=x-user-defined");e.send();var f=function(){Date.now();var f;f=d?e.responseText:new Uint8Array(e.response);f.length<a.compressedMaximum?(f=a.decompressFunction(f,a.shouldOutputArray),console.warn("Your server has not been configured to serve .gz and .br files with the expected Content-Encoding. See http://www.pdftron.com/kb_content_encoding for instructions on how to resolve this.")):c&&(f=b(f));return f};if(a.isAsync)result=new Promise(function(b,
c){e.onload=function(e){200==this.status?b(f()):c("Download Failed "+a.url)};e.onerror=function(){c("Network error occurred "+a.url)}});else{if(200==e.status)return f();throw Error("Failed to load "+url);}return result},u=function(a,b){b.url=a.replace(".",".gz.");b.decompressFunction=m;return l(b)},n=function(a,b){b.url=a.replace(".",".br.");b.decompressFunction=f;return l(b)},t=function(a,b){b.url=a;b.decompressFunction=q;return l(b)},p=function(a,b,e,d){return a["catch"](function(a){console.warn(a);
return d(b,e)})},r=function(a,b,e){if(e.isAsync){for(var d=b[0](a,e),f=1;f<b.length;++f)d=p(d,a,e,b[f]);return d}for(f=0;f<b.length;++f)try{return b[f](a,e)}catch(h){console.warn(h.message)}};a.loadURLWithBrotliPriority=function(a,b,e,d){var f={};f.compressedMaximum=b;f.isAsync=e;f.shouldOutputArray=d;return r(a,[n,u,t],f)};a.loadURLWithGzipPriority=function(a,b,e,d){var f={};f.compressedMaximum=b;f.isAsync=e;f.shouldOutputArray=d;return r(a,[u,n,t],f)}})("undefined"===typeof window?this:window);(function(a){a.Uint8ClampedArray||(a.Uint8ClampedArray=a.Uint8Array);var k=!(!self.WebAssembly||!self.WebAssembly.validate);-1<a.navigator.userAgent.indexOf("Edge")&&(k=!1);var b=function(a){var b=this;this.promise=a.then(function(a){b.response=a;b.status=200})};b.prototype={addEventListener:function(a,b){this.promise.then(b)}};a.loadCompiledBackend=function(a,f,h,m){if(k&&!m)Module.instantiateWasm=function(b,k){return self.instantiateCachedURL(f,a+"Wasm.wasm",b,h["Wasm.wasm"]).then(function(a){k(a)})},
m=loadURLWithBrotliPriority(a+"Wasm.js",h["Wasm.js"],!1,!1);else{m=loadURLWithGzipPriority((Module.asmjsPrefix?Module.asmjsPrefix:"")+a+".js",h[".js"],!1);var q=loadURLWithGzipPriority((Module.memoryInitializerPrefixURL?Module.memoryInitializerPrefixURL:"")+a+".js.mem",h[".js.mem"],!0,!0);Module.memoryInitializerRequest=new b(q)}eval.call(self,m)}})("undefined"===typeof window?this:window);var wasmCacheVersion=2;
(function(a){a.basePath="../";var k=function(){a.loadCompiledBackend("PDFNetC",wasmCacheVersion,{"Wasm.wasm":4E6,"Wasm.js":1E5,".js":6E6,".js.mem":3E5})};self.shouldResize||k();a.EmscriptenPDFManager=function(){};a.EmscriptenPDFManager.prototype={OnInitialized:function(b){Module.loaded?b():(Module.init_cb=function(){b()},a.utils.log("worker","PDFNet is not initialized yet!"))},NewDoc:function(a,d){return new Promise(function(f,h){try{f(Module.loadDoc(a,d))}catch(k){h(k)}})},LoadAnnotations:function(a,
d){var f;d?(f=Module.PageArrayToPageSet(d),f=Module.PDFDocExportXFDF(a,f)):f=Module.PDFDocExportXFDF(a);return f},Initialize:function(b,d,f,h){b&&(Module.TOTAL_MEMORY=b);Module.memoryInitializerPrefixURL=d;Module.asmjsPrefix=f;a.basePath=h;k()},shouldRunRender:function(a){return Module.ShouldRunRender(a.docId,a.pageIndex+1)}};(function(b){function d(b){var c=b.data,d=b.action,f;f="GetCanvas"==d||"GetCanvasPartial"==d?q.shouldRunRender(c):!0;if(f){p=b;var g=b.asyncCallCapability;a.utils.log("Memory",
"Worker running command: "+d);l.actionMap[d](c,b).then(function(a){g.resolve(a)},function(a){g.reject(a)})}else deferredQueue.queue(b),k()}function f(a){a.asyncCallCapability=createPromiseCapability();p?l.queue(a):d(a);return a.asyncCallCapability.promise}function h(c){self.shouldResize&&q.Initialize(c.options.workerHeapSize,c.options.pdfResourcePath,c.options.pdfAsmPath,c.options.parentUrl);Module.chunkMax=c.options.chunkMax;if(c.array instanceof Uint8Array){var d=255===c.array[0];b.postMessageTransfers=
d;"response"in new XMLHttpRequest?q.OnInitialized(function(){a.SetupPDFNetFunctions(b);t();b.send("test",{supportTypedArray:!0,supportTransfers:d})}):b.send("test",!1)}else b.send("test",!1)}function k(){setImmediate(function(){if(!p||"BeginOperation"!=p.action)if(l.length){if(!p||p.priority!=c){var a=l.dequeue();d(a)}}else p=null})}var q=new a.EmscriptenPDFManager,l,u=!1,n=!1;Module.workerMessageHandler=b;var t=function(){u?n||(b.send("workerLoaded",{}),n=!0):u=!0};q.OnInitialized(t);(function(){a.queue=
l=new PriorityQueue({strategy:PriorityQueue.ArrayStrategy,comparator:function(a,b){return a.priority===b.priority?a.callbackId-b.callbackId:b.priority-a.priority}});a.deferredQueue=new PriorityQueue({strategy:PriorityQueue.ArrayStrategy,comparator:function(a,b){return a.priority===b.priority?a.callbackId-b.callbackId:b.priority-a.priority}});l.actionMap={};l.onAsync=function(a,c){b.onAsync(a,f);l.actionMap[a]=c}})();var p=null;b.on("test",h);b.on("InitWorker",h);var r=function(a){p&&a(p)&&(Module.cancelCurrent(),
p=null);l.removeAllMatching(a,function(a){a.asyncCallCapability.reject({type:"Cancelled",message:"Operation was cancelled due to document deletion."})})},g=function(a){r(function(b){return b.data&&b.data.docId===a})};b.on("UpdatePassword",function(a){return Module.UpdatePassword(a.docId,a.password)});b.on("LoadRes",function(a){Module.loadResources(a.array,a.l);return{}});b.on("DownloaderHint",function(a){Module.DownloaderHint(a.docId,a.hint)});b.on("IsLinearized",function(a){return Module.IsLinearizationValid(a.docId)});
b.onNextAsync(k);l.onAsync("NewDoc",function(a){return q.NewDoc(a)});l.onAsync("GetCanvas",function(b){a.utils.log("workerdetails","Run GetCanvas PageIdx: "+b.pageIndex+" Width: "+b.width);a.utils.log("Memory","loadCanvas with potential memory usage "+b.width*b.height*8);return Module.loadCanvas(b.docId,b.pageIndex,b.width,b.height,b.rotation,null,b.layers,b.overprintMode)});l.onAsync("GetCanvasPartial",function(b){a.utils.log("Memory","GetCanvasPartial with potential memory usage "+b.width*b.height*
8);return Module.loadCanvas(b.docId,b.pageIndex,b.width,b.height,b.rotation,b.bbox,b.layers,b.overprintMode)});l.onAsync("LoadText",function(a){return Module.GetTextData(a.docId,a.pageIndex)});l.onAsync("SaveDoc",function(a){return Module.SaveDoc(a.docId,a.xfdfString,a.finishedWithDocument,a.printDocument,a.flags)});l.onAsync("SaveDocFromFixedElements",function(a){return Module.SaveDocFromFixedElements(a.bytes,a.xfdfString,a.flags)});l.onAsync("MergeXFDF",function(a){return Module.MergeXFDF(a.docId,
a.xfdf)});l.onAsync("InsertPages",function(a){return Module.InsertPages(a.docId,a.doc,a.pageArray,a.destPos,a.insertBookmarks)});l.onAsync("MovePages",function(a){return Module.MovePages(a.docId,a.pageArray,a.destPos)});l.onAsync("RemovePages",function(a){return Module.RemovePages(a.docId,a.pageArray)});l.onAsync("RotatePages",function(a){return Module.RotatePages(a.docId,a.pageArray,a.rotation)});l.onAsync("ExtractPages",function(a){return Module.ExtractPages(a.docId,a.pageArray,a.xfdfString)});
l.onAsync("CropPages",function(a){return Module.CropPages(a.docId,a.pageArray,a.topMargin,a.botMargin,a.leftMargin,a.rightMargin)});l.onAsync("InsertBlankPages",function(a){return Module.InsertBlankPages(a.docId,a.pageArray,a.width,a.height)});l.onAsync("BeginOperation",function(a){return Promise.resolve()});l.onAsync("RequirePage",function(a,b){return Module.RequirePage(a.docId,a.pageNum)});var c=99999;b.on("FinishOperation",function(a){if("BeginOperation"==p.action)b.onNextAsync(k),Module.cancelCurrent(),
p=null;else throw{message:"Operation has not started."};});b.on("DeleteDocument",function(a){a=a.docId;g(a);Module.DeleteDoc(a)});b.on("LoadBookmarks",function(a){return Module.LoadBookmarks(a.docId)});b.on("GetCanvasProgressive",function(a){var b;if(p&&p.callbackId===a.callbackId)b=Module.GetCurrentCanvasData(!0);else{if(l.find({priority:0,callbackId:a.callbackId}))throw{type:"Queued",message:"Rendering has not started yet."};if(deferredQueue.find({priority:0,callbackId:a.callbackId}))throw{type:"Queued",
message:"Rendering has not started yet."};}if(!b)throw{type:"Unavailable",message:"Rendering is complete or was cancelled."};return b});b.on("LoadAnnotations",function(a){return{xfdfString:q.LoadAnnotations(a.docId,a.pages)}});b.on("actionCancel",function(b){p&&p.callbackId===b.callbackId?(a.utils.log("workerdetails","Cancelled Current Operation"),Module.cancelCurrent()&&k()):(a.utils.log("workerdetails","Cancelled queued operation"),l.remove({priority:0,callbackId:b.callbackId}),deferredQueue.remove({priority:0,
callbackId:b.callbackId}))})})(new MessageHandler("worker_processor",self))})("undefined"===typeof window?this:window);
