import request from '@/utils/request'

/*根据ID获取详情*/
export function getDetail(id) {
  return request({
    method: 'get',
    url: `/apm/baseAppt/${id}`,
  })
}

/*获取列表*/
export function list(query) {
  return request({
    method: 'get',
    url: `/apm/baseAppt/list`,
    data: query,
  })
}
/*获取列表-分页*/
export function listPage(query) {
  return request({
    method: 'get',
    url: `/apm/baseAppt/listPage`,
    data: query,
  })
}
/*工艺路线删除*/
export function remove(id) {
  return request({
    method: 'get',
    url: `/apm/baseAppt/remove/${id}`,
  })
}
/*新增*/
export function save(query) {
  return request({
    method: 'post',
    url: `/apm/baseAppt/save`,
    data: query,
  })
}
/*修改*/
export function update(query) {
  return request({
    method: 'post',
    url: `/apm/baseAppt/update`,
    data: query,
  })
}
export function getDeviceType() {
  return request({
    method: 'get',
    url: `device/category/list`,
  })
}
export function getDeviceByType(typeId) {
  return request({
    method: 'get',
    url: `device/deviceProduct/page?pageSize=1000&pageNum=1&categoryId=` + typeId,
  })
}
export function getWorkEquit(typeId) {
  return request({
    method: 'get',
    url: `/equit/equitAssist/list?pageNum=1&pageSize=1000&type=` + typeId,
  })
}

