import request from '@/utils/request'

// 获取类型树
export function getTypeTree() {
  return request({
    url: '/api/system/serviceConfiguration/type/tree/list',
    method: 'GET'
  })
}

// 获取编码规则下拉列表
export function getCodeRule() {
  return request({
    url: '/api/system/codeRule/selector',
    method: 'GET'
  })
}
// 获取生命周期模板
export function getWorkflowEngine() {
  return request({
    url: '/api/workflow/Engine/flowTemplate/Selector',
    method: 'GET'
  })
}

// 添加类型
export function addType(data) {
  return request({
    url: `/api/system/serviceConfiguration/type`,
    method: 'PUT',
    data
  })
}

// 修改类型
export function updateType(data) {
  return request({
    url: `/api/system/serviceConfiguration/type`,
    method: 'POST',
    data
  })
}

// 删除类型
export function deleteType(id) {
  return request({
    url: `/api/system/serviceConfiguration/type/${id}`,
    method: 'DELETE'
  })
}

// 查询属性列表
export const getAttributeList = (data) => {
  return request({
    url: `/api/system/serviceConfiguration/extend/list`,
    method: 'GET',
    data
  })
}

// 添加属性
export function addAttribute(data) {
  return request({
    url: `/api/system/serviceConfiguration/extend`,
    method: 'PUT',
    data
  })
}

// 修改属性
export function updateAttribute(data) {
  return request({
    url: `/api/system/serviceConfiguration/extend`,
    method: 'POST',
    data
  })
}

// 删除属性
export function deleteAttribute(data) {
  return request({
    url: `/api/system/serviceConfiguration/extend`,
    method: 'DELETE',
    data
  })
}

// 获取所属视图字典

export function getBelongViewList(innerName) {
  return request({
    url: `/api/system/DictionaryData/${innerName}`,
    method: 'GET'
  })
}

// 根据code获取扩展属性详细信息（用于部分表单的扩展属性）
export function getFormItem(data) {
  return request({
    url: `/api/system/serviceConfiguration/extend/selector/extendDetail`,
    method: 'GET',
    data
  })
}