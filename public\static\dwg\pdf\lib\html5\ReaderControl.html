<!doctype html>
<html lang="en" moznomarginboxes mozdisallowselectionprint>
    <head>
        <meta charset="utf-8" />
        <script>
            try {
                var bgColor = window.frameElement && window.frameElement.getAttribute('data-bgcolor');
                if (bgColor) {
                    var sheet = (function() {
                        var style = document.createElement("style");
                        style.appendChild(document.createTextNode(""));
                        document.head.appendChild(style);
                        return style.sheet;
                    })();
                    sheet.insertRule('body { background-color: ' + bgColor + ' !important; }', 0);
                }
            } catch(e) {
                console.warn('Cannot set background color with cross origin viewer');
            }
        </script>
        <link href="external/jquery-ui/themes/cloud/jquery-ui-1.11.1.custom.min.css" rel="stylesheet">
        <!-- build:icons-css -->
        <link href="Resources/icons/css/glyphicons.css" rel="stylesheet">
        <link href="Resources/icons/css/customicons.css" rel="stylesheet">
        <link href="Resources/icons/css/controls.css" rel="stylesheet">
        <!-- endbuild -->

        <!-- build:viewer-css -->
        <link href="external/context.standalone.css" rel="stylesheet"/>
        <link href="external/jquery.treeview.css" rel="stylesheet"/>
        <link href="ReaderControl.css" rel="stylesheet"/>
        <link href="docViewer.css" rel="stylesheet"/>
        <link href="AnnotationEdit.css" rel="stylesheet"/>
        <link href="KeyRequest.css" rel="stylesheet"/>
        <link href="external/drop/css/drop-theme-basic.css" rel="stylesheet"/>
        <link href="external/drop/css/drop-theme-arrows.css" rel="stylesheet"/>
        <link href="external/drop/css/drop-theme-arrows-bounce.css" rel="stylesheet"/>
        <!-- endbuild -->

    </head>
    <body>
        <div id="ui-display">
            <div id="sidePanel" class="left-content" style="display:none">
                <div id="tabs" class="ui-tabs-hide">
                    <ul id="tabMenu">
                        <li><a href="#tabs-1"><span data-i18n="[title]sidepanel.thumbnails" class="glyphicons picture active"></span></a></li>
                        <li><a href="#tabs-2"><span data-i18n="[title]sidepanel.outline" class="glyphicons customicons bookmark"></span></a></li>
                        <li><a href="#tabs-3"><span data-i18n="[title]sidepanel.fullDocumentSearch" class="glyphicons search"></span></a></li>
                    </ul>
                    <div id="tabs-1" style="padding:10px">
                        <div id="thumbnailView" class="tab-panel-stretch"></div>
                    </div>
                    <div id="tabs-2">
                        <ul id="bookmarkView" class="filetree treeview ui-widget-content tab-panel-stretch"></ul>
                    </div>
                    <div id="tabs-3" >
                        <div id="searchView" class="tab-panel-stretch">
                            <div id="searchOptions" class="tab-panel-item-fixed">
                                <input type="text" name="fullSearch" id="fullSearchBox" />
                                <button id="fullSearchButton" style="position:relative; top:2px" type="button" data-i18n="sidepanel.searchTab.buttonGo"></button>
                            </div>
                            <div id="searchInfo" class="tab-panel-item-fixed">
                                <span id="prevSearchResult" class="glyphicons chevron-left"></span>
                                <span id="nextSearchResult" class="glyphicons chevron-right"></span>
                                <span id="clearSearchResults" class="glyphicons remove"></span>
                                <span id="searchResultCount"></span>
                            </div>
                            <div class="tab-panel-item-fixed">
                                <input id="wholeWordSearch" type="checkbox"><span class="ui-label" data-i18n="sidepanel.searchTab.wholeWordOnly"></span></div>
                            <div class="tab-panel-item-fixed">
                                <input id="caseSensitiveSearch" type="checkbox"><span class="ui-label" data-i18n="sidepanel.searchTab.caseSensitive"></span>
                            </div>
                            <div class="tab-panel-item-fixed">
                                <input id="wildCardSearch" type="checkbox"><span class="ui-label" data-i18n="sidepanel.searchTab.wildCardMode"></span>
                            </div>
                            <div id="fullSearchView" class="tab-panel-item-stretch">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="right-content">
                <textarea id="clipboard" readonly></textarea>
                <div id="control" class="ui-widget-header toolbar">
                    <div class="left-aligned">
                        <span id="toggleSidePanel" class="glyphicons collapse" data-i18n="[title]controlbar.toggleSidePanel"></span>
                        <span id="backPage" class="hidden-md glyphicons left_arrow back-forward-disabled" data-i18n="[title]controlbar.backPage;"></span>
                        <span id="forwardPage" class="hidden-md glyphicons right_arrow back-forward-disabled" data-i18n="[title]controlbar.forwardPage;"></span>
                    </div>

                    <div class="right-aligned">
                        <div class="group">
                            <span id="prevPage" class="glyphicons chevron-left" data-i18n="[title]controlbar.previousPage;"></span>
                            <input type="text" pattern="[0-9]*" name="pageNumberBox" id="pageNumberBox"/>
                            <div class="ui-label" id="totalPages">/0</div>
                            <span id="nextPage" class="glyphicons chevron-right" data-i18n="[title]controlbar.nextPage;"></span>
                        </div>

                        <div class="group">
                            <span id="zoomOut" class="glyphicons zoom_out" data-i18n="[title]controlbar.zoomOut"></span>
                            <div class="hidden-md" id="slider" data-i18n="[title]controlbar.zoom"></div>
                            <input type="text" name="zoomBox" id="zoomBox" data-i18n="[title]controlbar.zoom"/>
                            <span id="zoomIn" class="glyphicons zoom_in" data-i18n="[title]controlbar.zoomIn"></span>
                        </div>

                        <div class="drop-content group">
                            <div id="layoutModeDropDown">
                                <span class="current-layout drop-target glyphicons customicons page_gear"></span>
                                <div class="hidden">
                                    <div class="content">
                                        <span class="heading">
                                            <span data-i18n="[title]controlbar.pageLayoutModes;controlbar.pageLayoutModes"></span>:
                                        </span>
                                        <ul id="layoutModes" class="icon-picker">
                                            <li data-layout-mode="Single" data-layout-icon="single_page"
                                                data-i18n="[title]controlbar.layoutMode.single"
                                                class="glyphicons customicons single_page active"><span></span></li>
                                            <li data-layout-mode="Continuous" data-layout-icon="single_continuous"
                                                data-i18n="[title]controlbar.layoutMode.continuous"
                                                class="glyphicons customicons single_continuous"><span></span></li>
                                            <li data-layout-mode="Facing" data-layout-icon="facing_page"
                                                data-i18n="[title]controlbar.layoutMode.facing"
                                                class="glyphicons customicons facing_page"><span></span></li>
                                            <li data-layout-mode="FacingContinuous" data-layout-icon="facing_continuous"
                                                data-i18n="[title]controlbar.layoutMode.facingContinuous"
                                                class="glyphicons customicons facing_continuous"><span></span></li>
                                            <li data-layout-mode="CoverFacing" data-layout-icon="cover_page"
                                                data-i18n="[title]controlbar.layoutMode.cover"
                                                class="glyphicons customicons cover_page"><span></span></li>
                                            <li data-layout-mode="Cover" data-layout-icon="cover_continuous"
                                                data-i18n="[title]controlbar.layoutMode.coverContinuous"
                                                class="glyphicons customicons cover_continuous"><span></span></li>
                                        </ul>
                                           <span class="heading">
                                            <span data-i18n="[title]controlbar.pageRotation.title;controlbar.pageRotation.title"></span>:
                                        </span>
                                        <ul id="rotateGroup" class="icon-picker">
                                            <li data-rotate="ccw"  data-i18n="[title]controlbar.pageRotation.ccw90"
                                                class="glyphicons unshare"><span></span></li>
                                            <li data-rotate="cc" data-i18n="[title]controlbar.pageRotation.cc90"
                                                class="glyphicons share"><span></span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="group">
                            <div id="fitModes" class="hidden-xs toggleControl">
                                <span id="fitWidth" class="glyphicons customicons arrow_ew"  data-i18n="[title]controlbar.fitWidth;"></span>
                                <span id="fitPage" class="glyphicons customicons arrow_nsew"  data-i18n="[title]controlbar.fitPage"></span>
                            </div>

                            <div id="toolList" class="toggleControl" style="margin-left: 6px">
                                <span data-toolmode="AnnotationEdit" class="annotTool glyphicons select" data-i18n="[title]annotations.tooltips.edit">
                                    <svg class="svg" width="17px" height="26px" viewBox="0 0 17 26">
                                        <g id="iconbase" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="icon" transform="translate(-412.000000, -1.000000)">
                                                <g id="ic_select" transform="translate(406.000000, 0.000000)">
                                                    <rect id="bounds" x="0" y="0" width="28" height="28"></rect>
                                                    <polygon id="Shape" points="9.54581898 1.28698923 6.28929245 22.2409077 11.3778373 18.6274 13.5751557 26.9492154 18.7004773 25.5555692 16.503159 17.2337538 22.6902994 17.7813154"></polygon>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </span>
                                <span data-toolmode="AnnotationCreateTextHighlight" class="annotTool glyphicons customicons text_highlight" data-i18n="[title]annotations.tooltips.highlight"></span>
                                <span data-toolmode="AnnotationCreateSticky" class="annotTool glyphicons comments" data-i18n="[title]annotations.tooltips.stickyNote"></span>
                                <span data-toolmode="AnnotationCreateFreeHand" id="freeHand" class="annotTool glyphicons brush" data-i18n="[title]annotations.tooltips.freeHand"></span>
                                <span data-toolmode="AnnotationCreateFreeText" id="freeText" class="annotTool glyphicons customicons text_ibeam" data-i18n="[title]annotations.tooltips.freetext"></span>
                                <span id="overflowTools" class="moreButton glyphicons down_arrow" data-i18n="[title]annotations.tooltips.more">
                                    <div id="overflowToolsContainer" class="toolsContainer">
                                        <span data-toolmode="AnnotationCreateTextUnderline" class="annotTool glyphicons text_underline" data-i18n="[title]annotations.tooltips.underline"></span>
                                        <span data-toolmode="AnnotationCreateTextStrikeout" class="annotTool glyphicons text_strike" data-i18n="[title]annotations.tooltips.strikeout"></span>
                                        <span data-toolmode="AnnotationCreateTextSquiggly" class="annotTool glyphicons customicons text_squiggly" data-i18n="[title]annotations.tooltips.squiggly"></span>
                                        <span data-toolmode="AnnotationCreateLine" class="annotTool glyphicons vector_path_line" data-i18n="[title]annotations.tooltips.line"></span>
                                        <span data-toolmode="AnnotationCreateArrow" class="annotTool glyphicons customicons vector_arrow" data-i18n="[title]annotations.tooltips.arrow"></span>
                                        <span data-toolmode="AnnotationCreateSignature" class="annotTool glyphicons customicons signature" data-i18n="[title]annotations.tooltips.signature"></span>
                                        <span data-toolmode="AnnotationCreateCallout" class="annotTool glyphicons customicons callout" data-i18n="[title]annotations.tooltips.callout"></span>
                                        <span data-toolmode="AnnotationCreateStamp" class="annotTool glyphicons picture" data-i18n="[title]annotations.tooltips.stamp"></span>
                                        <span data-toolmode="AnnotationCreateRectangle" class="annotTool glyphicons vector_path_square" data-i18n="[title]annotations.tooltips.rectangle"></span>
                                        <span data-toolmode="AnnotationCreateEllipse" class="annotTool glyphicons vector_path_circle" data-i18n="[title]annotations.tooltips.ellipse"></span>
                                        <span data-toolmode="AnnotationCreatePolygon" class="annotTool glyphicons vector_path_polygon" data-i18n="[title]annotations.tooltips.polygon"></span>
                                        <span data-toolmode="AnnotationCreatePolyline" class="annotTool glyphicons customicons vector_path_polyline" data-i18n="[title]annotations.tooltips.polyline"></span>
                                        <span data-toolmode="AnnotationCreatePolygonCloud" class="annotTool glyphicons cloud" data-i18n="[title]annotations.tooltips.polygoncloud"></span>
                                    </div>
                                </span>
                            </div>
                        </div>

                        <div id="searchControlGroup" class="group hidden-sm">
                            <span id="searchControl" class="search-component">
                                <input id="searchBox" type="text" class="toolbar-input-text search-component" name="search" data-i18n="[title]controlbar.search"/>
                                <span id="searchButton" class="search-component glyphicons search" data-i18n="[title]controlbar.search"></span>
                            </span>
                        </div>

                        <div class="group">
                            <span id="fullScreenButton" class="glyphicons fullscreen" data-i18n="[title]controlbar.fullScreen"></span>
                            <span id="printButton" class="glyphicons print" data-i18n="[title]controlbar.print"></span>
                        </div>

                        <div class="group hidden-md">
                            <span id="toggleNotesPanel" class="glyphicons comments" data-i18n="[title]controlbar.toggleNotesPanel"></span>
                        </div>
                    </div>
                </div>

                <ul id="displayModeMenuList" class="ui-widget ui-menu-dropdown" style="display: none">
                    <li data-display-mode="single"><a href="javascript:void(0)"><span class="ui-icon ui-icon-custom-page-single"></span><div data-i18n="controlbar.layoutMode.single"></div></a></li>
                    <li data-display-mode="single-cont"><a href="javascript:void(0)"><span class="ui-icon ui-icon-custom-page-single-cont"></span><div data-i18n="controlbar.layoutMode.continuous"></div></a></li>
                    <li data-display-mode="facing"><a href="javascript:void(0)"><span class="ui-icon ui-icon-custom-page-facing"></span><div data-i18n="controlbar.layoutMode.facing"></div></a></li>
                    <li data-display-mode="facing-cont"><a href="javascript:void(0)"><span class="ui-icon ui-icon-custom-page-facing-cont"></span><div data-i18n="controlbar.layoutMode.facingContinuous"></div></a></li>
                    <li data-display-mode="cover"><a href="javascript:void(0)"><span class="ui-icon ui-icon-custom-page-cover"></span><div data-i18n="controlbar.layoutMode.cover"></div></a></li>
                    <li data-display-mode="cover-cont"><a href="javascript:void(0)"><span class="ui-icon ui-icon-custom-page-cover-cont"></span><div data-i18n="controlbar.layoutMode.coverContinuous"></div></a></li>
                </ul>


                <div id="notesPanelWrapper" class="hidden" style="display:none">
                    <div id="notesBar">
                        <span class="glyphicons chevron-left toggleNotesButton" data-i18n="[title]controlbar.toggleNotesPanel"></span>
                        <div id="annotLines" style="display:none"></div>
                    </div>
                    <div id="noteOptionsButton" class="glyphicons expand"></div>
                    <div id="noteOptions" class="panelElement panelElementFont">
                        <span id="noteOptionsCloseButton" class="glyphicons remove_2"></span>
                            <div data-i18n="notesPanel.orderBy" style="font-weight:bold;text-shadow: 1px 1px 1px rgb(240, 240, 240); margin: 0px 0 4px 0;color: #555;"></div>
                        <div id="noteTypeRadio">
                            <input type="radio" id="documentRadio" name="noteTypeRadio" checked="checked">
                                <label id="documentSortButton" for="documentRadio" data-i18n="[title]notesPanel.orderDocumentDesc">
                                    <span data-i18n="notesPanel.orderDocument"></span>
                                </label>
                            <input type="radio" id="timeRadio" name="noteTypeRadio">
                                <label id="timeSortButton" for="timeRadio" data-i18n="[title]notesPanel.orderTimeDesc">
                                    <span data-i18n="notesPanel.orderTime"></span>
                                </label>
                        </div>

                        <input type="text" id="commentSearchBox" data-i18n="[placeholder]notesPanel.searchComments">

                        <button type="button" id="toggleAnnotationsButton" class="roundedCornerButton" data-i18n="annotations.buttonHide"></button>
                        <button type="button" id="saveAnnotationsButton" class="roundedCornerButton" data-i18n="annotations.buttonSave"></button>
                    </div>
                    <div id="notesPanel" class="hidden"></div>
                </div>

                <div id="overlayMessage" class="overlayMessage" style="display:none"></div>

                <div id="annotEditDialog" style="display:none">
                    <input type="hidden" autofocus="autofocus" />
                    <div id="annotEditButtons" class="horizontalButtons">
                        <button type="button" id="annotEditDone" class="roundedCornerButton" data-i18n="annotationPopup.buttonDone"></button>
                        <button type="button" id="annotEditDelete" class="roundedCornerButton" data-i18n="annotationPopup.buttonDelete"></button>
                        <button type="button" id="annotEditStyle" class="roundedCornerButton" data-i18n="annotationPopup.buttonStyle"></button>
                        <button type="button" id="annotEditNote" class="roundedCornerButton" data-i18n="annotationPopup.buttonNote"></button>
                    </div>

                    <div id="annotEditProperties" style="display:none">
                        <div id="colorType">
                            <div id="colorButton" class="colorButton"><span class="glyphicons brush"></span></div>
                            <div id="fillColorButton" class="colorButton"><span class="glyphicons customicons fill"></span></div>
                            <div id="textColorButton" class="colorButton"><span class="glyphicons customicons text_ibeam"></span></div>
                        </div>

                        <div id="basicProperties">
                            <div id="colorPicker">
                                <ul class="colorPicker">
                                    <li id="addColorButton"><div></div></li>
                                    <li id="removeColorButton"><div></div></li>
                                </ul>
                            </div>
                            <div class="annotPropertyContainer">
                                <canvas id="annotPreviewCanvas" height="48" width="48"></canvas>
                            </div>
                            <div id="basicPropertyContainer" class="annotPropertyContainer">
                                <div class="fontSizePicker propertyButtonContainer">
                                    <div id="fontSizeRadio">
                                        <input type="radio" id="fontSizeRadio8" data-value="8" name="fontSizeRadio"><label for="fontSizeRadio8">8</label>
                                        <input type="radio" id="fontSizeRadio12" data-value="12" name="fontSizeRadio"><label for="fontSizeRadio12">12</label>
                                        <input type="radio" id="fontSizeRadio24" data-value="24" name="fontSizeRadio"><label for="fontSizeRadio24">24</label>
                                        <input type="radio" id="fontSizeRadio36" data-value="36" name="fontSizeRadio"><label for="fontSizeRadio36">36</label>
                                        <span class="propertyValue"></span>
                                    </div>
                                </div>
                                <div class="thicknessPicker propertyButtonContainer">
                                    <div id="thicknessRadio">
                                        <input type="radio" id="thicknessRadio1" data-value="1" name="thicknessRadio"><label for="thicknessRadio1">1</label>
                                        <input type="radio" id="thicknessRadio3" data-value="3" name="thicknessRadio"><label for="thicknessRadio3">3</label>
                                        <input type="radio" id="thicknessRadio7" data-value="7" name="thicknessRadio"><label for="thicknessRadio7">7</label>
                                        <input type="radio" id="thicknessRadio12" data-value="12" name="thicknessRadio"><label for="thicknessRadio12">12</label>
                                        <span class="propertyValue"></span>
                                    </div>
                                </div>
                                <div class="opacityPicker propertyButtonContainer">
                                    <div id="opacityRadio">
                                        <input type="radio" id="opacityRadio25" data-value="25" name="opacityRadio"><label for="opacityRadio25">25</label>
                                        <input type="radio" id="opacityRadio50" data-value="50" name="opacityRadio"><label for="opacityRadio50">50</label>
                                        <input type="radio" id="opacityRadio75" data-value="75" name="opacityRadio"><label for="opacityRadio75">75</label>
                                        <input type="radio" id="opacityRadio100" data-value="100" name="opacityRadio"><label for="opacityRadio100">100</label>
                                        <span class="propertyValue"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="advancedProperties" style="display:none">
                            <ul class="colorPicker advanced">
                            </ul>
                            <div id="advancedPropertyContainer" class="annotPropertyContainer">
                                <div class="fontSizePicker">
                                    <h4 data-i18n="annotations.properties.fontSize"></h4>
                                    <div id="advancedFontSizeSlider" class="slider"></div>
                                </div>
                                <div class="thicknessPicker">
                                    <h4 data-i18n="annotations.properties.thickness"></h4>
                                    <div id="advancedThicknessSlider" class="slider"></div>
                                </div>
                                <div class="opacityPicker">
                                    <h4 data-i18n="annotations.properties.opacity"></h4>
                                    <div id="advancedOpacitySlider" class="slider"></div>
                                </div>
                            </div>
                        </div>
                        <div id="colorButtons">
                            <button type="button" id="basicPropertyEdit" class="roundedCornerButton leftRounded selected" data-i18n="annotations.properties.basic"></button>
                            <button type="button" id="advancedPropertyEdit" class="roundedCornerButton rightRounded" data-i18n="annotations.properties.advanced"></button>
                        </div>
                    </div>

                    <div id="addNewColor" style="display:none">
                        <ul class="colorPicker advanced">
                        </ul>
                        <span id="cancelAddColor" class="glyphicons remove_2"></span>
                        <span id="selectAddColor" class="glyphicons ok_2 disabled" style="float:right"></span>
                    </div>

                    <div id="signatureSelectionContainer" class="horizontalButtons" style="display:none">
                        <button type="button" id="mySignatureButton" class="roundedCornerButton leftRounded" data-i18n="signatureDialog.mySignature"></button>
                        <button type="button" id="newSignatureButton" class="roundedCornerButton rightRounded" data-i18n="signatureDialog.newSignature"></button>
                    </div>

                    <div id="textSelectionContainer" class="horizontalButtons" style="display:none">
                        <button type="button" id="copyButton" class="roundedCornerButton leftRounded" data-i18n="textSelection.copy"></button>
                        <button type="button" id="selectHighlightButton" class="roundedCornerButton" data-i18n="annotations.types.highlight"></button>
                        <button type="button" id="selectStrikeoutButton" class="roundedCornerButton" data-i18n="annotations.types.strikeout"></button>
                        <button type="button" id="selectUnderlineButton" class="roundedCornerButton" data-i18n="annotations.types.underline"></button>
                        <button type="button" id="selectSquigglyButton" class="roundedCornerButton rightRounded" data-i18n="annotations.types.squiggly"></button>
                    </div>
                </div>

                <div id="signatureDialog" style="display:none">
                    <canvas id="signatureCanvas"></canvas>

                    <div>
                        <div class="signatureButtonContainer">
                            <button type="button" id="signatureCancelButton" class="roundedCornerButton" data-i18n="signatureDialog.cancel"></button>
                        </div>

                        <div class="signatureButtonContainer noIbar" style="float:right">
                            <input id="makeDefaultSignature" type="checkbox" class = "noIbar" />
                            <span id="makeDefaultSignatureText" data-i18n="signatureDialog.makeDefault" style="padding-right:5px" class = "noselect noIbar"></span>
                            <button type="button" id="signatureClearButton" class="roundedCornerButton noIbar" data-i18n="signatureDialog.clear"></button>
                            <button type = "button" id="signatureAddButton" class="roundedCornerButton unclickableButton" data-i18n="signatureDialog.add"></button>
                        </div>
                    </div>
                </div>

                <div id="printDialog" style="display:none">
                    <div id="printPageOptions">
                        <span data-i18n="print.pagesToPrint"></span><br>
                        <input type="radio" id="allPages" name="pageSelectionType" checked="checked">
                        <label id="printAllPagesButton" for="allPages"
                               data-i18n="[title]print.allPagesRadioTitle">
                            <span data-i18n="print.all"></span>
                        </label>
                        <br>
                        <input type="radio" id="currentPage" name="pageSelectionType">
                        <label for="currentPage"><span data-i18n="print.currentPage"></span></label>
                        <br>
                        <input type="radio" id="selectedPages" name="pageSelectionType"/>
                        <input type="text" id="selectedPagesInput" data-i18n="[placeholder]print.hint"/>
                    </div><br/>
                    <div id="numberPagesToPrint">
                        <span class="numberPagesLabel"></span>
                    </div>
                    <div id="invalidPageSelectionError" class="error-message">
                        <span data-i18n="print.invalidPageSelectionMsg"></span>
                    </div>
                    <div id="printProgress"><div class="progressLabel"></div></div>
                </div>

                <div id="DocumentViewer" style="overflow:auto;background-color:black">
                    <div id="docpad">
                    </div>
                    <div id="viewer">
                    </div>
                </div>

                <div style="display:none" id="unsupportedErrorMessage" class="ui-widget center-screen-error">
                    <div class="ui-state-error">
                        <div class="ui-state-error-text">
                            <span class="ui-icon ui-icon-alert" style="float: left;margin: 5px;"></span>
                            <div data-i18n="unsupportedBrowser"></div>
                        </div>
                        <ul>
                            <li>Internet Explorer 9+</li>
                            <li>Google Chrome</li>
                            <li>Firefox</li>
                            <li>Safari</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="print-display" style="display:none">
        </div>

        <!-- build:qt-reader-js -->
        
        <!-- endbuild -->

        <!-- build:wk-reader-js -->
        
        <!-- endbuild -->

        <!-- build:cordova-reader-js -->
        
        <!-- endbuild -->

        <!-- build:external-js -->
        <script src="external/jquery-3.2.1.min.js"></script>
        <script src="external/jquery-ui/jquery-ui-1.11.1.custom.min.js"></script>
        <script src="external/jquery.treeview.js"></script>
        <script src="external/jquery.mousewheel.js"></script>
        <script src="external/jquery.flexibleArea.js"></script>
        <script src="external/Autolinker.min.js"></script>
        <script src="external/moment.min.js"></script>
        <script src="external/modernizr.custom.js"></script>
        <script src="external/drop/drop.min.js"></script>
        <script src="external/context.js"></script>
        <!-- endbuild -->

        <!-- build:corecontrols-js-include -->
        
        <!-- endbuild -->

        <!-- build:corecontrols-js -->
        <script src="CoreControls.js"></script>
        <!-- endbuild -->

        <!-- build:readercontrol-js -->
        <script src="WebViewerInterface.js"></script>
        <script src="ControlUtils.js"></script>
        <script src="AnnotationEdit.js"></script>
        <script src="NotesPanel.js"></script>
        <script src="BaseReaderControl.js"></script>
        <script src="ReaderControl.js"></script>
        <script src="ReaderControlConfig.js"></script>
        <!-- endbuild -->

    </body>
</html>
