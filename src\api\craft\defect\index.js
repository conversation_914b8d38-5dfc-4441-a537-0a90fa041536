import request from '@/utils/request'

/*根据id获取详情*/
export function getDetail(id) {
  return request({
    method: 'get',
    url: `/apm/baseBug/${id}`,
  })
}
/*获取基础缺陷信息列表*/
export function list(query) {
  return request({
    method: 'get',
    url: '/apm/baseBug/list',
    data: query,
  })
}
/*获取基础缺陷信息列表-分页*/
export function listPage(query) {
  return request({
    method: 'get',
    url: '/apm/baseBug/listPage',
    data: query,
  })
}
/*根据id删除*/
export function remove(id) {
  return request({
    method: 'get',
    url: `/apm/baseBug/remove/${id}`,
  })
}
/*新增*/
export function save(query) {
  return request({
    method: 'post',
    url: '/apm/baseBug/save',
    data: query,
  })
}
/*修改*/
export function update(query) {
  return request({
    method: 'post',
    url: '/apm/baseBug/update',
    data: query,
  })
}
