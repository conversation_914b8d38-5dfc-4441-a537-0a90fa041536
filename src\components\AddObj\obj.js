export default [
  {
    code: "blueprint",
    name: "图纸",
    url: '/productData/document',
    link: true
  },
  {
    code: "文档",
    name: "文档",
    url: '/productData/document',
    link: false,
    type: 'bom',
  },
  {
    code: "BBOM",
    name: "BBOM",
    value: "BBOM",
    link: false,
    type: 'bom',
    url: ''
  },
  {
    code: "LBOM",
    name: "LBOM",
    value: "LBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/LBOM'
  },
  {
    code: "OBOM",
    name: "OBOM",
    value: "DBOM",
    link: false,
    type: 'bom',
    url: ''
  },
  {
    code: "MBOM",
    name: "<PERSON><PERSON>",
    value: "MBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/MBOM'
  },
  {
    code: "PBOM",
    name: "PBOM",
    value: "PBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/PBOM'
  },
  {
    code: "DBOM",
    name: "DBOM",
    value: "DBOM",
    link: false,
    type: 'bom',
    url: ''
  },
  {
    code: "FBOM",
    name: "FBO<PERSON>",
    value: "FBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/FBOM'
  },
  {
    code: "TBOM",
    name: "TBOM",
    value: "TBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/TBOM'
  },
  {
    code: "DFBOM",
    name: "DFBOM",
    value: "DFBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/dfBOM'
  },
  {
    code: "EBOM",
    name: "EBOM",
    value: "EBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/EBOM'
  },
  {
    code: "RBOM",
    name: "RBOM",
    value: "RBOM",
    link: false,
    type: 'bom',
    url: '/productData/xBom/rBOM'
  },
  {
    code: "selectionRecord",
    name: "选型记录",
    link: false,
    type: 'ComponentSelection'
  },
  {
    code: "工艺路线",
    name: "工艺路线",
    link: true,
    url: '/productData/processRoute',
    type: 'bom'
  },
  {
    code: "物料",
    name: "物料",
    url: '/productData/materials',
    type: 'bom',
    link: true,
  },
  {
    code: '需求单',
    name: "需求单",
    url: '/demand/pool',
    type: 'bom',
    link: true,
  },
  {
    code: '选型记录',
    name: "选型记录",
    url: '/demand/pool',
    type: 'bom',
    link: true,
  },
  {
    code: "产品基线",
    name: "产品基线",
    link: true,
    url: '/technicalStatus/productModelFile',
  }
]