import request from '@/utils/request'

// 获取类别类型字典
export function getCategoryTypeDict() {
  return request({
    url: '/api/ProductObjectRelationController/queryType',
    method: 'get'
  })
}

// 获取分页列表
export function getTableData(data) {
  return request({
    url: '/api/ProductCategoryRelationController/queryByLike',
    method: 'post',
    data
  })
}

// 获取关系树所有类型
export function getAllRelateTypeList() {
  return request({
    url: '/api/RelationTreeController/chose/enums',
    method: 'get'
  })
}

//根据关系树类型获取关系树
export function getTreeDataByType(data) {
  return request({
    url: '/api/RelationTreeController/chose/tree',
    method: 'get',
    data
  })
}
//点击节点树获取表格信息
export function getTableDataByTreeCode(data) {
  return request({
    url: '/api/RelationTreeController/chose/page',
    method: 'post',
    data
  })
}

//新增绑定映射
export function addMapRelation(data) {
  return request({
    url: '/api/ProductCategoryRelationController/add',
    method: 'post',
    data
  })
}
//编辑绑定映射
export function updateMapRelation(data) {
  return request({
    url: '/api/ProductCategoryRelationController/update',
    method: 'post',
    data
  })
}
//删除绑定映射
export function delMapRelation(id) {
  return request({
    url: `/api/ProductCategoryRelationController/${id}`,
    method: 'delete'
  })
}