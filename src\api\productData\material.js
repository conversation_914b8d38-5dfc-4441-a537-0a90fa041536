import request from '@/utils/request'

// 获取物料库目录
export function getMaterialCatalogue() {
  return request({
    url: `/api/system/serviceConfiguration/type/select/tree/jnpf.product.entity.material.ProductMaterialLibraryData`,
    method: 'GET',
  })
}
// 根据条件分页获取物料数据
export function getMaterialList(params) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/queryByLike`,
    method: 'GET',
    data: params,
  })
}

// 根据条件分页获取物料归档数据
export function getMaterialHisList(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataHisController/queryByLike`,
    method: 'POST',
    data,
  })
}



// 根据物料编码获取物料数据
export function getMaterialCode(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/getInfoByCode`,
    method: 'GET',
    data,
  })
}

//解除绑定
export function relieveBinApi(ids) {
  return request({
    url: `/api/ProductObjectRelationController/delete/${ids}`,
    method: 'post',
  })
}



// 获取已关联的特征项
export function getFeautureItems(data) {
  return request({
    url: `/api/ProductMaterialFeatureRelationController/queryByTypeCode?extendCode=${data.extendCode}&categoryPadCharacter=${data.categoryPadCharacter}`,
    method: 'GET',
  })
}


// 保存关联的特征项
export function saveFeautures(data) {
  return request({
    url: `/api/ProductMaterialFeatureRelationController/addBatch`,
    method: 'POST',
    data
  })
}



// 添加时查询特征项列表
export function addFeauturesList(data) {
  return request({
    url: `/api/ProductMaterialFeatureDataController/queryFeatureByTypeCode?extendCode=${data.extendCode}&categoryPadCharacter=${data.categoryPadCharacter}&materialCode=${data.materialCode}&typeCode=${data.typeCode}&materialId=${data.materialId}`,
    method: 'GET',
  })

}
// 新增 --- 保存暂存
export function addMaterialSave(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/saveEntity`,
    method: 'POST',
    data
  })
}


// 新增 --- 保存暂存
export function editMaterialSave(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/saveEntity`,
    method: 'PUT',
    data
  })
}


// 新增 --- 提交 进入流程
export function addMaterialSubmit(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/addMaterial`,
    method: 'POST',
    data
  })
}


// 物料文档 --- 文档关联批量新增
export function batchAdddList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/addBatch`,
    method: 'POST',
    data
  })
}

// 物料文档 --- 文档批量提交
export function batchSubmitDocument(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/submitBatch`,
    method: 'POST',
    data
  })
}


// 物料文档 --- 根据物料编号版本查询文档信息 文档表格数据
export function queryDocumnetList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/getInfoByMaterialCode`,
    method: 'POST',
    data
  })
}

// 物料文档 --- 文档文件上传(文件上传接口)
export function uploadDocumnet(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/update`,
    method: 'POST',
    data
  })
}


// 物料文档 --- 删除单个文档关联的数据
export function deleteDocumnet(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/delete`,
    method: 'POST',
    data
  })
}


// 企标物料库 --- 删除 单条数据
export function deleteMaterial(code) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/deleteByCode?code=${code}`,
    method: 'DELETE',
  })
}

// 企标物料库文档 --- 查看
export function checkDocument(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/detail`,
    method: 'GET',
    data
  })
}


// 企标物料库文档 --- 根据文档编码查询(所有版本)
export function checkVersion(code) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/queryByFileCode/${code}`,
    method: 'GET',
  })
}

// 对象绑定-获取关系名下拉列表
export function getRelationListDict(data) {
  return request({
    url: `/api/ProductCategoryRelationController/queryRelationName`,
    method: 'GET',
    data
  })
}

// 对象绑定-根据关系名获取对象类型下拉列表
export function getObjectTypeListDict(data) {
  return request({
    url: `/api/ProductCategoryRelationController/queryRelationTypeCodeByType`,
    method: 'GET',
    data
  })
}

// 对象绑定-根据对象类型获取列表数据
export function getTableDataByObType(data) {
  return request({
    url: `/api/ProductCategoryRelationController/queryObjectByType`,
    method: 'POST',
    data
  })
}

// 对象绑定-对象绑定保存
export function submitObjectBind(data) {
  return request({
    url: `/api/ProductObjectRelationController/add`,
    method: 'post',
    data
  })
}

// 获取对象关系所有类型
export function getRelationTreeController() {
  return request({
    url: `/api/RelationTreeController/chose/enums`,
    method: 'GET'
  })
}

// 获取对象关系列表
export function getObjectRelationship(data) {
  return request({
    url: `/api/ProductObjectRelationController/queryByLike`,
    method: 'POST',
    data
  })
}

// 查看详情-获取详情地址和请求参数
export function getQueryParameters(data) {
  return request({
    url: `/api/ProductObjectRelationController/queryParameters`,
    method: 'POST',
    data
  })
}

// 跳转 EPM-BOM
export function materialToBom(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/toEpm`,
    method: 'POST',
    data
  })
}



// E-BOM 反查（根据物料编号查询 BOM 信息）
export function queryEBOM(data) {
  return request({
    url: `/api/ProductEbomController/peggingTree`,
    method: 'POST',
    data
  })
}

// P-BOM 反查（根据物料编号查询 BOM 信息）
export function queryPBOM(data) {
  return request({
    url: `/api/ProductPbomController/peggingTree`,
    method: 'POST',
    data
  })
}

// M-BOM 反查（根据物料编号查询 BOM 信息）
export function queryMBOM(data) {
  return request({
    url: `/api/mbom/peggingTree`,
    method: 'POST',
    data
  })
}


// E-BOM 查看 左侧的树形结构 反查
export function peggingEbomTree(data) {
  return request({
    url: `/api/ProductEbomController/peggingTree`,
    method: 'POST',
    data
  })
}


// P-BOM 查看 左侧的树形结构 反查
export function peggingPbomTree(data) {
  return request({
    url: `/api/ProductPbomController/peggingTree`,
    method: 'POST',
    data
  })
}

// M-BOM 查看 左侧的树形结构 反查
export function peggingMbomTree(data) {
  return request({
    url: `/api/mbom/peggingTree`,
    method: 'POST',
    data
  })
}


export function importDataClassify(data) {
  return request({
    url: `/api/system/serviceConfiguration/type/importDataToMaterial`,
    method: 'POST',
    data
  })
}

export function downloadTemplateClassify() {
  return request({
    method: 'get',
    url: `/api/system/serviceConfiguration/type/downloadTemplate`,
    responseType: 'blob',
  })
}

export function importData(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/importData`,
    method: 'POST',
    data
  })
}

export function downloadTemplate() {
  return request({
    method: 'get',
    url: `/api/ProductMaterialLibraryDataController/downloadTemplate`,
    responseType: 'blob',
  })
}


export function exportData(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataController/exportData`,
    method: 'POST',
    responseType: 'blob',
    data
  })
}