import request from '@/utils/request'



// E-BOM 结构 表格数据查询
export function queryStructureList(data) {
  return request({
    url: `/api/ProductEbomConstructController/searchHis`,
    method: 'POST',
    data
  })
}

/**
 * @name E-bom变更
 * @param {*} bomcode  bom 编码
 * @param {*} version  bom 版本
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function getBomInfo(data) {
  return request({
    url: `/api/ProductEbomController/getInfoByCode`,
    method: 'POST',
    data
  })
}



/**
 * @name E-bom变更
 * @param {*} constructs  修订的表格数据
 * @param {*} id  主键ID
 * @param {*} code  bom编码
 * @param {*} version  bom版本
 * @param {*} materialCode  物料编码
 * @param {*} materialVersion  物料版本
 * @param {*} materialName  物料名称
 * @param {*} category  归属目录
 * @param {*} materialSpecs  物料规格型号
 * @param {*} preChangeContent  变更前内容
 * @param {*} revisedContent  变更后内容
 * @param {*} specificReasons  具体内容
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function saveRevision({
  constructs,
  id,
  code,
  version,
  materialCode,
  materialVersion,
  materialName,
  category,
  materialSpecs,
  preChangeContent,
  revisedContent,
  specificReasons
}) {
  return request({
    url: `/api/ProductEbomController/updateInfoByCodeAndVersion`,
    method: 'POST',
    data: {
      constructs,
      id,
      code,
      version,
      materialCode,
      materialVersion,
      materialName,
      category,
      materialSpecs,
      preChangeContent,
      revisedContent,
      specificReasons
    }
  })
}




// P-BOM 结构 表格数据查询
export function queryPbomStructureList(data) {
  return request({
    url: `/api/ProductPbomConstructController/searchHis`,
    method: 'POST',
    data
  })
}


/**
 * @name P-bom变更
 * @param {*} bomcode  bom 编码
 * @param {*} version  bom 版本
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function getpBomInfo(data) {
  return request({
    url: `/api/ProductPbomController/getInfoByCode`,
    method: 'POST',
    data
  })
}



/**
 * @name P-bom变更
 * @param {*} constructs  修订的表格数据
 * @param {*} id  主键ID
 * @param {*} code  bom编码
 * @param {*} version  bom版本
 * @param {*} materialCode  物料编码
 * @param {*} materialVersion  物料版本
 * @param {*} materialName  物料名称
 * @param {*} category  归属目录
 * @param {*} materialSpecs  物料规格型号
 * @param {*} preChangeContent  变更前内容
 * @param {*} revisedContent  变更后内容
 * @param {*} specificReasons  具体内容
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function savePbomRevision({
  constructs,
  id,
  code,
  version,
  materialCode,
  materialVersion,
  materialName,
  category,
  materialSpecs,
  preChangeContent,
  revisedContent,
  specificReasons
}) {
  return request({
    url: `/api/ProductPbomController/updateInfoByCodeAndVersion`,
    method: 'POST',
    data: {
      constructs,
      id,
      code,
      version,
      materialCode,
      materialVersion,
      materialName,
      category,
      materialSpecs,
      preChangeContent,
      revisedContent,
      specificReasons
    }
  })
}


// M-BOM 结构 表格数据查询
export function queryMbomStructureList(data) {
  return request({
    url: `/api/mbomConstruct/searchHis`,
    method: 'POST',
    data
  })
}



/**
 * @name M-bom变更
 * @param {*} bomcode  bom 编码
 * @param {*} version  bom 版本
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function getMBomInfo(data) {
  return request({
    url: `/api/mbom/getInfoByCode`,
    method: 'POST',
    data
  })
}



/**
 * @name M-bom变更
 * @param {*} constructs  修订的表格数据
 * @param {*} id  主键ID
 * @param {*} code  bom编码
 * @param {*} version  bom版本
 * @param {*} materialCode  物料编码
 * @param {*} materialVersion  物料版本
 * @param {*} materialName  物料名称
 * @param {*} category  归属目录
 * @param {*} materialSpecs  物料规格型号
 * @param {*} preChangeContent  变更前内容
 * @param {*} revisedContent  变更后内容
 * @param {*} specificReasons  具体内容
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function saveMbomRevision({
  constructs,
  id,
  code,
  version,
  materialCode,
  materialVersion,
  materialName,
  category,
  materialSpecs,
  preChangeContent,
  revisedContent,
  specificReasons
}) {
  return request({
    url: `/api/mbom/updateInfoByCodeAndVersion`,
    method: 'POST',
    data: {
      constructs,
      id,
      code,
      version,
      materialCode,
      materialVersion,
      materialName,
      category,
      materialSpecs,
      preChangeContent,
      revisedContent,
      specificReasons
    }
  })
}



/**
 * @name 签出
 * @param {*} objCode bom 编码
 * @returns 
 */
// 签出
export function checkInToOut({
  objCode
}) {
  return request({
    url: `/api/repository/obj/checkInToOut/${objCode}`,
    method: 'POST',
  })
}


/**
 * @name 校验是否有权限修改
 * @param {*} objCode  bom 编码
 * @returns 
 */
export function checkIsCheckOutBySelf({
  objCode
}) {
  return request({
    url: `/api/repository/obj/checkIsCheckOutBySelf/${objCode}`,
    method: 'POST',
  })
}



/**
 * @name  获取当前对象签出信息
 * @param {*} objCode  bom 编码
 * @returns 
 */
export function getCheckOutObj({
  objCode
}) {
  return request({
    url: `/api/repository/obj/getCheckOutObj/${objCode}`,
    method: 'GET',
  })
}

/**
 * @pathUrl  url前缀
 * @code  参数
 * @returns 
 */
export function getVersionList({ pathUrl, code }) {
  return request({
    url: `/api/${pathUrl}/getVersionList/${code}`,
    method: 'GET',
  })
}


/**
 * @name E,P,M模版下载
 */
export function downloadTemplate(data) {
  return request({
    url: `/api/ProductEbomController/downloadTemplate`,
    method: 'POST',
    data,
    responseType: 'blob'
  })
}


export function downloadTemplateToInsert(data) {
  return request({
    url: `/api/ProductEbomController/downloadTemplateToInsert`,
    method: 'POST',
    data,
    responseType: 'blob'
  })
}


export function downloadData(data) {
  return request({
    url: `/api/ProductEbomConstructController/downloadData`,
    method: 'POST',
    data,
    responseType: 'blob'
  })
}
