import request from '@/utils/request'

export const getBasicStepList = (query) => {
  return request({
    method: 'get',
    url: '/apm/esop-info/list',
    data: query,
  })
}
export const addBasicStep = (params) => {
  return request({
    method: 'post',
    url: '/apm/esop-info',
    data: params,
  })
}
export const deleteBasicStep = (id) => {
  return request({
    method: "delete",
    url: `/apm/esop-info/${id}`
  })
}
export const getBasicStepInfo = (id) => {
  return request({
    method: "get",
    url: `/apm/esop-info/${id}`
  })
}
export const modifyBasicStep = (params) => {
  return request({
    method: 'put',
    url: '/apm/esop-info',
    data: params,
  })
}
export const getDefaultSteps = (routeId) => {
  //分页接口，预设条数不超过1000
  return request({
    method: 'get',
    url: '/apm/esop-info/list',
    data: {
      baseOperationOp: routeId,
      pageNum: 1,
      pageSize: 1000
    },
  })
}
export const setBasicStepEnabled = (id, val) => {
  return request({
    method: 'put',
    url: `/apm/esop-info/${id}/${val}`,
    data: params,
  })
}
export const getStepConfig = (routeId, operationId) => {
  return request({
    method: "get",
    url: `/apm/esop-config/${routeId}/${operationId}`
  })
}
export const getMaterialList = () => {
  return request({
    method: "get",
    url: "/apm/operationItem/list"
  })
}
export const saveESOP = (params) => {
  return request({
    method: "post",
    url: "/apm/esop-config",
    data: params,
  })
}
