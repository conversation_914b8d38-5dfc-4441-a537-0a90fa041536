<template>
  <div v-if="buttonList.length">
    <el-button v-for="(item, index) in defaultButtonList" :key="index" size="mini" type="text" @click="handleClick(item.callback)">{{ item.label }}</el-button>
    <el-dropdown v-if="moreButtonList.length" trigger="click">
      <el-button type="text" size="mini">更多<i class="el-icon-arrow-down el-icon--right" /> </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item, index) in moreButtonList" @click.native="handleClick(item.callback)" :key="index">{{ item.label }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
export default {
  props: {
    buttonList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultButtonList: [],
      moreButtonList: []
    }
  },
  watch: {
    buttonList: {
      handler(newVal, oldVal) {
        this.defaultButtonList = this.buttonList.filter((item, index) => index < 2)
        this.moreButtonList = this.buttonList.length > 2 ? this.buttonList.filter((item, index) => index > 1) : []
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleClick(callback) {
      this.$emit("callback", callback)
    }
  }
}
</script>
