import request from '@/utils/request'
import qs from 'qs'

// 获取需求单列表
export function getDemandList(data) {
  return request({
    url: `/api/demand`,
    method: 'GET',
    data,
  })
}

// 保存需求单基础信息
export function addDefaultDemandInfo(data) {
  return request({
    url: `/api/demand`,
    method: 'PUT',
    data
  })
}

// 保存需求单全量信息
export function submitAllDemandInfo(data) {
  return request({
    url: `/api/demand/all`,
    method: 'PUT',
    data
  })
}

// 修改需求单全量信息
export function updateAllDemandInfo(data) {
  return request({
    url: `/api/demand`,
    method: 'POST',
    data
  })
}

//复制需求档案

export function copyDemand(id) {
  return request({
    url: `/api/demand/copy/${id}`,
    method: 'POST',
  })
}


// 修改需求单状态
export function updateDemandStatus(data) {
  return request({
    url: `/api/demand/updateStatus`,
    method: 'GET',
    data
  })
}

// 删除需求单
export function deleteDemand(data) {
  return request({
    url: `/api/demand`,
    method: 'DELETE',
    data
  })
}

// 查询详情
export function getDemandInfo(data) {
  return request({
    url: `/api/demand/info`,
    method: 'GET',
    data
  })
}

// 需求单历史版本列表
export function getDemandHistory(demandCode) {
  return request({
    url: `/api/demand/history/${demandCode}`,
    method: 'GET',
  })
}

// 版本对比
export function getVersionComparison(data) {
  return request({
    url: `/api/demand/versionComparison`,
    method: 'POST',
    data
  })
}

// 元器件列表
export function getDemandModelSelection(data) {
  return request({
    url: `/api/demandModelSelection`,
    method: 'GET',
    data
  })
}
// 保存元器件选型信息
export function AddDemandModelSelection(data) {
  return request({
    url: `/api/demandModelSelection`,
    method: 'POST',
    data
  })
}

// 获取元器件详情
export function getDemandModelSelectionDetail(id) {
  return request({
    url: `/api/demandModelSelection/detail/${id}`,
    method: 'GET',
  })
}
// 修改元器件选型信息
export function updateDemandModelSelection(data) {
  return request({
    url: `/api/demandModelSelection`,
    method: 'PUT',
    data
  })
}

// 物料类型
export function getProductEntity() {
  return request({
    url: `/api/system/serviceConfiguration/type/select/tree/jnpf.product.entity.material.ProductMaterialLibraryData`,
    method: 'GET',
  })
}
// 获取产业需求
export function getProductDemand(data) {
  return request({
    url: `/api/demand/modelSelector/tree`,
    method: 'GET',
    data
  })
}
// 筛选可选择的零部件
export function getFilterMaterial(data) {
  return request({
    url: `/api/demandModelSelection/filterMaterial`,
    method: 'POST',
    data
  })
}
// 删除元器件选型信息
export function deleteDemandModelSelection(ids) {
  return request({
    url: `/api/demandModelSelection/batchDelete/${ids}`,
    method: 'DELETE',
  })
}

// 元器件详情
export function getMaterial(data) {
  return request({
    url: `/api/demandModelSelection/getMaterial`,
    method: 'GET',
    data
  })
}