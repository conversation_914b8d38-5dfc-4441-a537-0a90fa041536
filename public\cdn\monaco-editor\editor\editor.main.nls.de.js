/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.21.2(67b5a8116f3c0bace36b180e524e05bb750a16d8)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.de",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["Eingabe"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Groß-/<PERSON>schre<PERSON>ung beachten","Nur ganzes Wort suchen","Reg<PERSON>ären Ausdruck verwenden"],"vs/base/browser/ui/findinput/replaceInput":["Eingabe","Groß-/Kleinschreibung beibehalten"],"vs/base/browser/ui/inputbox/inputBox":["Fehler: {0}","Warnung: {0}","Info: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Ungebunden"],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Löschen","Typfilter deaktivieren","Typfilter aktivieren","Keine Elemente gefunden","{0} von {1} Elementen stimmen überein"],
"vs/base/common/errorMessage":["{0}: {1}","Ein Systemfehler ist aufgetreten ({0}).","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll.","{0} ({1} Fehler gesamt)","Ein unbekannter Fehler ist aufgetreten. Weitere Details dazu finden Sie im Protokoll."],"vs/base/common/keybindingLabels":["STRG","UMSCHALTTASTE","ALT","Windows","STRG","UMSCHALTTASTE","ALT","Super","Steuern","UMSCHALTTASTE","ALT","Befehl","Steuern","UMSCHALTTASTE","ALT","Windows","Steuern","UMSCHALTTASTE","ALT","Super"],"vs/base/parts/quickinput/browser/quickInput":["Zurück","{0}/{1}","Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.","{0} Ergebnisse","{0} ausgewählt","OK","Benutzerdefiniert","Zurück ({0})","Zurück"],"vs/base/parts/quickinput/browser/quickInputList":["Schnelleingabe"],"vs/editor/browser/controller/coreCommands":["Auch bei längeren Zeilen am Ende bleiben","Auch bei längeren Zeilen am Ende bleiben"],
"vs/editor/browser/controller/textAreaHandler":["Editor","Auf den Editor kann derzeit nicht zugegriffen werden. Drücken Sie {0}, um die Optionen anzuzeigen."],"vs/editor/browser/editorExtensions":["&&Rückgängig","Rückgängig","&&Wiederholen","Wiederholen","&&Alles auswählen","Alle auswählen"],"vs/editor/browser/widget/codeEditorWidget":["Die Anzahl der Cursors wurde auf {0} beschränkt."],"vs/editor/browser/widget/diffEditorWidget":["Kann die Dateien nicht vergleichen, da eine Datei zu groß ist."],"vs/editor/browser/widget/diffReview":["Schließen","keine geänderten Zeilen","1 Zeile geändert","{0} Zeilen geändert","Unterschied {0} von {1}: ursprüngliche Zeile {2}, {3}, geänderte Zeile {4}, {5}","leer","{0}: unveränderte Zeile {1}","{0} ursprüngliche Zeile {1} geänderte Zeile {2}","+ {0} geänderte Zeile(n) {1}","– {0} Originalzeile {1}","Zum nächsten Unterschied wechseln","Zum vorherigen Unterschied wechseln"],
"vs/editor/browser/widget/inlineDiffMargin":["Gelöschte Zeilen kopieren","Gelöschte Zeile kopieren","Gelöschte Zeile kopieren ({0})","Diese Änderung rückgängig machen","Gelöschte Zeile kopieren ({0})"],
"vs/editor/common/config/commonEditorConfig":["Editor",'Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn "#editor.detectIndentation#" aktiviert ist.','Fügt beim Drücken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn "#editor.detectIndentation#" aktiviert ist.','Steuert, ob "#editor.tabSize#" und "#editor.insertSpaces#" automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt geöffnet wird.',"Nachfolgende automatisch eingefügte Leerzeichen entfernen","Spezielle Behandlung für große Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.","Steuert, ob Vervollständigungen auf Grundlage der Wörter im Dokument berechnet werden sollen.","Die semantische Hervorhebung ist für alle Farbdesigns aktiviert.","Die semantische Hervorhebung ist für alle Farbdesigns deaktiviert.",'Die semantische Hervorhebung wird durch die Einstellung "semanticHighlighting" des aktuellen Farbdesigns konfiguriert.',"Steuert, ob die semantische Hervorhebung für die Sprachen angezeigt wird, die sie unterstützen.","Peek-Editoren geöffnet lassen, auch wenn auf den Inhalt doppelgeklickt oder die ESC-TASTE gedrückt wird.","Zeilen, die diese Länge überschreiten, werden aus Leistungsgründen nicht tokenisiert","Timeout in Millisekunden, nach dem die Diff-Berechnung abgebrochen wird. Bei 0 wird kein Timeout verwendet.","Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.","Wenn aktiviert, ignoriert der Diff-Editor Änderungen an voran- oder nachgestellten Leerzeichen.",'Steuert, ob der Diff-Editor die Indikatoren "+" und "-" für hinzugefügte/entfernte Änderungen anzeigt.',"Steuert, ob der Editor CodeLens anzeigt."],
"vs/editor/common/config/editorOptions":["Der Editor verwendet Plattform-APIs, um zu erkennen, wenn eine Sprachausgabe angefügt wird.","Der Editor wird durchgehend für die Verwendung mit einer Sprachausgabe optimiert.","Der Editor wird nie für die Verwendung mit einer Sprachausgabe optimiert.","Steuert, ob der Editor in einem Modus ausgeführt werden soll, in dem er für die Sprachausgabe optimiert wird.","Steuert, ob beim Kommentieren ein Leerzeichen eingefügt wird.","Steuert, ob leere Zeilen bei Umschalt-, Hinzufügungs- oder Entfernungsaktionen für Zeilenkommentare ignoriert werden sollen.","Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.","Steuert, ob der Cursor bei der Suche nach Übereinstimmungen während der Eingabe springt.",'Steuert, ob für die Suchzeichenfolge im Widget "Suche" ein Seeding aus der Auswahl des Editors ausgeführt wird.','"In Auswahl suchen" niemals automatisch aktivieren (Standard)','"In Auswahl suchen" immer automatisch aktivieren','"In Auswahl suchen" automatisch aktivieren, wenn mehrere Inhaltszeilen ausgewählt sind','Steuert die Bedingung zum automatischen Aktivieren von "In Auswahl suchen".','Steuert, ob das Widget "Suche" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.','Steuert, ob das Suchwidget zusätzliche Zeilen im oberen Bereich des Editors hinzufügen soll. Wenn die Option auf "true" festgelegt ist, können Sie über die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.',"Steuert, ob die Suche automatisch am Anfang (oder am Ende) neu gestartet wird, wenn keine weiteren Übereinstimmungen gefunden werden.","Aktiviert/deaktiviert Schriftartligaturen.","Explizite font-feature-settings-Eigenschaft","Konfiguriert Schriftartligaturen oder Schriftartfeatures.","Legt die Schriftgröße in Pixeln fest.",'Es sind nur die Schlüsselwörter "normal" und "bold" sowie Zahlen zwischen 1 und 1000 zulässig.','Steuert die Schriftbreite. Akzeptiert die Schlüsselwörter "normal" und "bold" sowie Zahlen zwischen 1 und 1000.',"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)","Zum Hauptergebnis gehen und Vorschauansicht anzeigen","Wechseln Sie zum primären Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie "editor.editor.gotoLocation.multipleDefinitions" oder "editor.editor.gotoLocation.multipleImplementations".','Legt das Verhalten des Befehls "Gehe zu Definition" fest, wenn mehrere Zielpositionen vorhanden sind','Legt das Verhalten des Befehls "Gehe zur Typdefinition" fest, wenn mehrere Zielpositionen vorhanden sind.','Legt das Verhalten des Befehls "Gehe zu Deklaration" fest, wenn mehrere Zielpositionen vorhanden sind.','Legt das Verhalten des Befehls "Gehe zu Implementierungen", wenn mehrere Zielspeicherorte vorhanden sind','Legt das Verhalten des Befehls "Gehe zu Verweisen" fest, wenn mehrere Zielpositionen vorhanden sind','Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von "Gehe zu Definition" die aktuelle Position ist.','Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von "Gehe zu Typdefinition" die aktuelle Position ist.','Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von "Gehe zu Deklaration" der aktuelle Speicherort ist.','Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von "Gehe zu Implementatierung" der aktuelle Speicherort ist.','Die alternative Befehls-ID, die ausgeführt wird, wenn das Ergebnis von "Gehe zu Verweis" die aktuelle Position ist.',"Steuert, ob die Hovermarkierung angezeigt wird.","Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.","Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger darüber bewegt wird.","Aktiviert das Glühbirnensymbol für Codeaktionen im Editor.","Steuert die Zeilenhöhe. Verwenden Sie 0, um die Zeilenhöhe aus der Schriftgröße zu berechnen.","Steuert, ob die Minimap angezeigt wird.","Die Minimap hat die gleiche Größe wie der Editor-Inhalt (und kann scrollen).","Die Minimap wird bei Bedarf vergrößert oder verkleinert, um die Höhe des Editors zu füllen (kein Scrollen).","Die Minimap wird bei Bedarf verkleinert, damit sie nicht größer als der Editor ist (kein Scrollen).","Legt die Größe der Minimap fest.","Steuert die Seite, wo die Minimap gerendert wird.","Steuert, wann der Schieberegler für die Minimap angezeigt wird.","Maßstab des in der Minimap gezeichneten Inhalts: 1, 2 oder 3.","Die tatsächlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbblöcken.","Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.","Steuert den Abstand zwischen dem oberen Rand des Editors und der ersten Zeile.","Steuert den Abstand zwischen dem unteren Rand des Editors und der letzten Zeile.","Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt während Sie tippen.","Steuert, ob das Menü mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schließt.","Schnellvorschläge innerhalb von Zeichenfolgen aktivieren.","Schnellvorschläge innerhalb von Kommentaren aktivieren.","Schnellvorschläge außerhalb von Zeichenfolgen und Kommentaren aktivieren.","Steuert, ob Vorschläge automatisch während der Eingabe angezeigt werden sollen.","Zeilennummern werden nicht dargestellt.","Zeilennummern werden als absolute Zahl dargestellt.","Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.","Zeilennummern werden alle 10 Zeilen dargestellt.","Steuert die Anzeige von Zeilennummern.","Anzahl der Zeichen aus Festbreitenschriftarten, ab der dieses Editor-Lineal gerendert wird.","Farbe dieses Editor-Lineals.","Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte für mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.","Vorschlag einfügen, ohne den Text auf der rechten Seite des Cursors zu überschreiben","Vorschlag einfügen und Text auf der rechten Seite des Cursors überschreiben","Legt fest, ob Wörter beim Akzeptieren von Vervollständigungen überschrieben werden. Beachten Sie, dass dies von Erweiterungen abhängt, die für dieses Features aktiviert sind.","Steuert, ob Filter- und Suchvorschläge geringfügige Tippfehler berücksichtigen.","Steuert, ob bei der Suche Wörter eine höhere Trefferquote erhalten, die in der Nähe des Cursors stehen.",'Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (dafür ist "#editor.suggestSelection#" erforderlich).','Steuert, ob ein aktiver Ausschnitt verhindert, dass der Bereich "Schnelle Vorschläge" angezeigt wird.',"Steuert, ob Symbole in Vorschlägen ein- oder ausgeblendet werden.","Steuert, wie viele Vorschläge IntelliSense anzeigt, bevor eine Scrollleiste eingeblendet wird (maximal 15).",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen separate Einstellungen wie "editor.suggest.showKeywords" oder "editor.suggest.showSnippets".','Wenn aktiviert, zeigt IntelliSense "method"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "funktions"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "constructor"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "field"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "variable"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "class"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "struct"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "interface"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "module"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "property"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "event"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "operator"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "unit"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "value"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "constant"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "enum"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "enumMember"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "keyword"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "text"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "color"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "file"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "reference"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "customcolor"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "folder"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "typeParameter"-Vorschläge an.','Wenn aktiviert, zeigt IntelliSense "snippet"-Vorschläge an.',"Wenn aktiviert, zeigt IntelliSense user-Vorschläge an.","Wenn aktiviert, zeigt IntelliSense issues-Vorschläge an.","Steuert die Sichtbarkeit der Statusleiste unten im Vorschlagswidget.",'Steuert, ob Vorschläge über Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (";") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.',"Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine Änderung am Text vornimmt.","Steuert, ob Vorschläge mit der EINGABETASTE (zusätzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einfügen neuer Zeilen oder dem Annehmen von Vorschlägen.","Legt die Anzahl der Zeilen im Editor fest, die von der Sprachausgabe ausgelesen werden können. Warnung: Es gibt eine Leistungsimplikation für Zahlen, die größer als die Standardeinstellung sind.","Editor-Inhalt","Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.","Schließe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor automatisch Klammern schließen soll, nachdem der Benutzer eine öffnende Klammer hinzugefügt hat.","Schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.","Steuert, ob der Editor schließende Anführungszeichen oder Klammern überschreiben soll.","Verwende die Sprachkonfiguration, um zu ermitteln, wann Anführungsstriche automatisch geschlossen werden.","Schließende Anführungszeichen nur dann automatisch ergänzen, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor Anführungszeichen automatisch schließen soll, nachdem der Benutzer ein öffnendes Anführungszeichen hinzugefügt hat.","Der Editor fügt den Einzug nicht automatisch ein.","Der Editor behält den Einzug der aktuellen Zeile bei.","Der Editor behält den in der aktuellen Zeile definierten Einzug bei und beachtet für Sprachen definierte Klammern.","Der Editor behält den Einzug der aktuellen Zeile bei, beachtet von Sprachen definierte Klammern und ruft spezielle onEnterRules-Regeln auf, die von Sprachen definiert wurden.","Der Editor behält den Einzug der aktuellen Zeile bei, beachtet die von Sprachen definierten Klammern, ruft von Sprachen definierte spezielle onEnterRules-Regeln auf und beachtet von Sprachen definierte indentationRules-Regeln.","Legt fest, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einfügen, verschieben oder einrücken","Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.","Mit Anführungszeichen, nicht mit Klammern umschließen.","Mit Klammern, nicht mit Anführungszeichen umschließen.","Steuert, ob der Editor eine Auswahl automatisch umschließen soll.","Steuert, ob der Editor CodeLens anzeigt.","Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.","Zulassen, dass die Auswahl per Maus und Tasten die Spaltenauswahl durchführt.","Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.","Steuert den Cursoranimationsstil.","Steuert, ob die weiche Cursoranimation aktiviert werden soll.","Steuert den Cursor-Stil.",'Steuert die Mindestanzahl sichtbarer vorangehender und nachfolgender Zeilen, die den Cursor umgeben. Wird in anderen Editoren als "scrollOff" oder "scrollOffset" bezeichnet.','"cursorSurroundingLines" wird nur erzwungen, wenn die Auslösung über die Tastatur oder API erfolgt.','"cursorSurroundingLines" wird immer erzwungen.',"Legt fest, wann cursorSurroundingLines erzwungen werden soll","Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.","Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zulässt.","Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.","Steuert, ob Codefaltung im Editor aktiviert ist.","Verwenden Sie eine sprachspezifische Faltstrategie, falls verfügbar. Andernfalls wird eine einzugsbasierte verwendet.","Einzugsbasierte Faltstrategie verwenden.","Steuert die Strategie für die Berechnung von Faltbereichen.","Steuert, ob der Editor eingefaltete Bereiche hervorheben soll.","Steuert, ob eine Zeile aufgefaltet wird, wenn nach einer gefalteten Zeile auf den leeren Inhalt geklickt wird.","Steuert die Schriftfamilie.","Steuert, ob der Editor den eingefügten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.","Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.","Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird hauptsächlich zum Debuggen verwendet.","Steuert, ob der Cursor im Übersichtslineal ausgeblendet werden soll.","Steuert, ob der Editor die aktive Einzugsführungslinie hevorheben soll.","Legt den Abstand der Buchstaben in Pixeln fest.","Steuert, ob der Editor Links erkennen und anklickbar machen soll.","Passende Klammern hervorheben",'Ein Multiplikator, der für die Mausrad-Bildlaufereignisse "deltaX" und "deltaY" verwendet werden soll.',"Schriftart des Editors vergrößern, wenn das Mausrad verwendet und die STRG-TASTE gedrückt wird.","Mehrere Cursor zusammenführen, wenn sie sich überlappen.","Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",'Der Modifizierer, der zum Hinzufügen mehrerer Cursor mit der Maus verwendet wird. Die Mausbewegungen "Gehe zu Definition" und "Link öffnen" werden so angepasst, dass kein Konflikt mit dem Multi-Cursor-Modifizierer entsteht. [Weitere Informationen](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).',"Jeder Cursor fügt eine Textzeile ein.","Jeder Cursor fügt den vollständigen Text ein.","Steuert das Einfügen, wenn die Zeilenanzahl des Einfügetexts der Cursor-Anzahl entspricht.","Steuert, ob der Editor das Vorkommen semantischer Symbole hervorheben soll.","Steuert, ob um das Übersichtslineal ein Rahmen gezeichnet werden soll.","Struktur beim Öffnen des Peek-Editors fokussieren","Editor fokussieren, wenn Sie den Peek-Editor öffnen","Steuert, ob der Inline-Editor oder die Struktur im Peek-Widget fokussiert werden soll.",'Steuert, ob die Mausgeste "Gehe zu Definition" immer das Vorschauwidget öffnet.',"Steuert die Verzögerung in Millisekunden nach der Schnellvorschläge angezeigt werden.","Steuert, ob der Editor bei Eingabe automatisch eine Umbenennung vornimmt.","Steuert, ob der Editor Steuerzeichen rendern soll.","Steuert, ob der Editor Einzugsführungslinien rendern soll.","Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.","Hebt den Bundsteg und die aktuelle Zeile hervor.","Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.","Legt fest, ob der Editor die aktuelle Zeilenhervorhebung nur dann rendern soll, wenn der Editor fokussiert ist","Leerraumzeichen werden gerendert mit Ausnahme der einzelnen Leerzeichen zwischen Wörtern.","Hiermit werden Leerraumzeichen nur für ausgewählten Text gerendert.","Nur nachstehende Leerzeichen rendern","Steuert, wie der Editor Leerzeichen rendern soll.","Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.","Steuert die Anzahl der zusätzlichen Zeichen, nach denen der Editor horizontal scrollt.","Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.","Nur entlang der vorherrschenden Achse scrollen, wenn gleichzeitig vertikal und horizontal gescrollt wird. Dadurch wird ein horizontaler Versatz beim vertikalen Scrollen auf einem Trackpad verhindert.","Steuert, ob die primäre Linux-Zwischenablage unterstützt werden soll.","Steuert, ob der Editor Übereinstimmungen hervorheben soll, die der Auswahl ähneln.","Steuerelemente für die Codefaltung immer anzeigen.","Steuerelemente für die Codefaltung nur anzeigen, wenn sich die Maus über dem Bundsteg befindet.","Steuert, wann die Steuerungselemente für die Codefaltung am Bundsteg angezeigt werden.","Steuert das Ausblenden von nicht verwendetem Code.","Steuert durchgestrichene veraltete Variablen.","Zeige Snippet Vorschläge über den anderen Vorschlägen.","Snippet Vorschläge unter anderen Vorschlägen anzeigen.","Zeige Snippet Vorschläge mit anderen Vorschlägen.","Keine Ausschnittvorschläge anzeigen.","Steuert, ob Codeausschnitte mit anderen Vorschlägen angezeigt und wie diese sortiert werden.","Legt fest, ob der Editor Bildläufe animiert ausführt.",'Schriftgröße für das vorgeschlagene Widget. Bei Festlegung auf 0 wird der Wert von "#editor.fontSize#" verwendet.','Zeilenhöhe für das vorgeschlagene Widget. Bei Festlegung auf 0 wird der Wert von "#editor.lineHeight#" verwendet.',"Steuert, ob Vorschläge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.","Immer den ersten Vorschlag auswählen.",'Wählen Sie die aktuellsten Vorschläge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgewählt, z.B. "console.| -> console.log", weil "log" vor Kurzem abgeschlossen wurde.','Wählen Sie Vorschläge basierend auf früheren Präfixen aus, die diese Vorschläge abgeschlossen haben, z.B. "co -> console" und "con ->" const".',"Steuert, wie Vorschläge bei Anzeige der Vorschlagsliste vorab ausgewählt werden.","Die Tab-Vervollständigung fügt den passendsten Vorschlag ein, wenn auf Tab gedrückt wird.","Tab-Vervollständigungen deaktivieren.",'Codeausschnitte per Tab vervollständigen, wenn die Präfixe übereinstimmen. Funktioniert am besten, wenn "quickSuggestions" deaktiviert sind.',"Tab-Vervollständigungen aktivieren.","Ungewöhnliche Zeilenabschlusszeichen werden ignoriert.","Zum Entfernen ungewöhnlicher Zeilenabschlusszeichen wird eine Eingabeaufforderung angezeigt.","Ungewöhnliche Zeilenabschlusszeichen werden automatisch entfernt.","Entfernen Sie unübliche Zeilenabschlusszeichen, die Probleme verursachen können.","Das Einfügen und Löschen von Leerzeichen erfolgt nach Tabstopps.","Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden.","Zeilenumbrüche erfolgen nie.","Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",'Der Zeilenumbruch erfolgt bei "#editor.wordWrapColumn#".','Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und "#editor.wordWrapColumn".',"Steuert, wie der Zeilenumbruch durchgeführt werden soll.",'Steuert die umschließende Spalte des Editors, wenn "#editor.wordWrap#" den Wert "wordWrapColumn" oder "bounded" aufweist.',"Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.","Umbrochene Zeilen erhalten den gleichen Einzug wie das übergeordnete Element.","Umbrochene Zeilen erhalten + 1 Einzug auf das übergeordnete Element.","Umgebrochene Zeilen werden im Vergleich zum übergeordneten Element +2 eingerückt.","Steuert die Einrückung der umbrochenen Zeilen.","Es wird angenommen, dass alle Zeichen gleich breit sind. Dies ist ein schneller Algorithmus, der für Festbreitenschriftarten und bestimmte Alphabete (wie dem lateinischen), bei denen die Glyphen gleich breit sind, korrekt funktioniert.","Delegiert die Berechnung von Umbruchpunkten an den Browser. Dies ist ein langsamer Algorithmus, der bei großen Dateien Code Freezes verursachen kann, aber in allen Fällen korrekt funktioniert.","Steuert den Algorithmus, der Umbruchpunkte berechnet."],
"vs/editor/common/model/editStack":["Eingabe..."],"vs/editor/common/modes/modesRegistry":["Nur-Text"],
"vs/editor/common/standaloneStrings":["Keine Auswahl","Zeile {0}, Spalte {1} ({2} ausgewählt)","Zeile {0}, Spalte {1}","{0} Auswahlen ({1} Zeichen ausgewählt)","{0} Auswahlen",'Die Einstellung "accessibilitySupport" wird jetzt in "on" geändert.',"Die Dokumentationsseite zur Barrierefreiheit des Editors wird geöffnet.","in einem schreibgeschützten Bereich eines Diff-Editors.","in einem Bereich eines Diff-Editors.","in einem schreibgeschützten Code-Editor","in einem Code-Editor","Drücken Sie BEFEHLSTASTE + E, um den Editor für eine optimierte Verwendung mit Sprachausgabe zu konfigurieren.","Drücken Sie STRG + E, um den Editor für eine optimierte Verwendung mit Sprachausgabe zu konfigurieren.","Der Editor ist auf eine optimale Verwendung mit Sprachausgabe konfiguriert.","Der Editor ist so konfiguriert, dass er nie auf die Verwendung mit Sprachausgabe hin optimiert wird. Dies ist zu diesem Zeitpunkt nicht der Fall.","Durch Drücken der TAB-TASTE im aktuellen Editor wird der Fokus in das nächste Element verschoben, das den Fokus erhalten kann. Schalten Sie dieses Verhalten um, indem Sie {0} drücken.","Durch Drücken der TAB-TASTE im aktuellen Editor wird der Fokus in das nächste Element verschoben, das den Fokus erhalten kann. Der {0}-Befehl kann zurzeit nicht durch eine Tastenzuordnung ausgelöst werden.","Durch Drücken der TAB-TASTE im aktuellen Editor wird das Tabstoppzeichen eingefügt. Schalten Sie dieses Verhalten um, indem Sie {0} drücken.","Durch Drücken der TAB-TASTE im aktuellen Editor wird das Tabstoppzeichen eingefügt. Der {0}-Befehl kann zurzeit nicht durch eine Tastenzuordnung ausgelöst werden.","Drücken Sie BEFEHLSTASTE + H, um ein Browserfenster mit weiteren Informationen zur Barrierefreiheit des Editors zu öffnen.","Drücken Sie STRG + H, um ein Browserfenster mit weiteren Informationen zur Barrierefreiheit des Editors zu öffnen.","Sie können diese QuickInfo schließen und durch Drücken von ESC oder UMSCHALT+ESC zum Editor zurückkehren.","Hilfe zur Barrierefreiheit anzeigen","Entwickler: Token überprüfen","Gehe zu Zeile/Spalte...","Alle Anbieter für den Schnellzugriff anzeigen","Befehlspalette","Befehle anzeigen und ausführen","Gehe zu Symbol...","Gehe zu Symbol nach Kategorie...","Editor-Inhalt","Drücken Sie ALT + F1, um die Barrierefreiheitsoptionen aufzurufen.","Zu Design mit hohem Kontrast umschalten","{0} Bearbeitungen in {1} Dateien durchgeführt"],
"vs/editor/common/view/editorColorRegistry":["Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.","Hintergrundfarbe für den Rahmen um die Zeile an der Cursorposition.","Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe für den Rahmen um hervorgehobene Bereiche.",'Hintergrundfarbe des hervorgehobenen Symbols, z. B. "Gehe zu Definition" oder "Gehe zu nächster/vorheriger". Die Farbe darf nicht undurchsichtig sein, um zugrunde liegende Dekorationen nicht zu verbergen.',"Hintergrundfarbe des Rahmens um hervorgehobene Symbole","Farbe des Cursors im Editor.","Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.","Farbe der Leerzeichen im Editor.","Farbe der Führungslinien für Einzüge im Editor.","Farbe der Führungslinien für Einzüge im aktiven Editor.","Zeilennummernfarbe im Editor.","Zeilennummernfarbe der aktiven Editorzeile.",'Die ID ist veraltet. Verwenden Sie stattdessen "editorLineNumber.activeForeground".',"Zeilennummernfarbe der aktiven Editorzeile.","Farbe des Editor-Lineals.","Vordergrundfarbe der CodeLens-Links im Editor","Hintergrundfarbe für zusammengehörige Klammern","Farbe für zusammengehörige Klammern","Farbe des Rahmens für das Übersicht-Lineal.","Hintergrundfarbe des Übersichtslineals im Editor. Wird nur verwendet, wenn die Minimap aktiviert ist und auf der rechten Seite des Editors platziert wird.","Hintergrundfarbe der Editorleiste. Die Leiste enthält die Glyphenränder und die Zeilennummern.","Rahmenfarbe unnötigen (nicht genutzten) Quellcodes im Editor.",'Deckkraft des unnötigen (nicht genutzten) Quellcodes im Editor. "#000000c0" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie für Designs mit hohem Kontrast das Farbdesign "editorUnnecessaryCode.border", um unnötigen Code zu unterstreichen statt ihn abzublenden.',"Übersichtslinealmarkerfarbe für das Hervorheben von Bereichen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Übersichtslineal-Markierungsfarbe für Fehler.","Übersichtslineal-Markierungsfarbe für Warnungen.","Übersichtslineal-Markierungsfarbe für Informationen."],
"vs/editor/contrib/anchorSelect/anchorSelect":["Auswahlanker",'Anker festgelegt bei "{0}:{1}"',"Auswahlanker festlegen","Zu Auswahlanker wechseln","Auswahl von Anker zu Cursor","Auswahlanker abbrechen"],"vs/editor/contrib/bracketMatching/bracketMatching":["Übersichtslineal-Markierungsfarbe für zusammengehörige Klammern.","Gehe zu Klammer","Auswählen bis Klammer","Gehe zu &&Klammer"],"vs/editor/contrib/caretOperations/caretOperations":["Ausgewählten Text nach links verschieben","Ausgewählten Text nach rechts verschieben"],"vs/editor/contrib/caretOperations/transpose":["Buchstaben austauschen"],"vs/editor/contrib/clipboard/clipboard":["&&Ausschneiden","Ausschneiden","Ausschneiden","&&Kopieren","Kopieren","Kopieren","&&Einfügen","Einfügen","Einfügen","Mit Syntaxhervorhebung kopieren"],
"vs/editor/contrib/codeAction/codeActionCommands":["Art der auszuführenden Codeaktion","Legt fest, wann die zurückgegebenen Aktionen angewendet werden","Die erste zurückgegebene Codeaktion immer anwenden","Die erste zurückgegebene Codeaktion anwenden, wenn nur eine vorhanden ist","Zurückgegebene Codeaktionen nicht anwenden","Legt fest, ob nur bevorzugte Codeaktionen zurückgegeben werden sollen","Beim Anwenden der Code-Aktion ist ein unbekannter Fehler aufgetreten","Schnelle Problembehebung …","Keine Codeaktionen verfügbar",'Keine bevorzugten Codeaktionen für "{0}" verfügbar','Keine Codeaktionen für "{0}" verfügbar',"Keine bevorzugten Codeaktionen verfügbar","Keine Codeaktionen verfügbar","Refactoring durchführen...",'Keine bevorzugten Refactorings für "{0}" verfügbar','Keine Refactorings für "{0}" verfügbar',"Keine bevorzugten Refactorings verfügbar","Keine Refactorings verfügbar","Quellaktion…",'Keine bevorzugten Quellaktionen für "{0}" verfügbar','Keine Quellaktionen für "{0}" verfügbar',"Keine bevorzugten Quellaktionen verfügbar","Keine Quellaktionen verfügbar","Importe organisieren","Keine Aktion zum Organisieren von Importen verfügbar","Alle korrigieren",'Aktion "Alle korrigieren" nicht verfügbar',"Automatisch korrigieren...","Keine automatischen Korrekturen verfügbar"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Fixes anzeigen. Bevorzugter Fix verfügbar ({0})","Korrekturen anzeigen ({0})","Korrekturen anzeigen"],"vs/editor/contrib/codelens/codelensController":["CodeLens-Befehle für aktuelle Zeile anzeigen"],"vs/editor/contrib/comment/comment":["Zeilenkommentar umschalten","Zeilenkommen&&tar umschalten","Zeilenkommentar hinzufügen","Zeilenkommentar entfernen","Blockkommentar umschalten","&&Blockkommentar umschalten"],"vs/editor/contrib/contextmenu/contextmenu":["Editor-Kontextmenü anzeigen"],"vs/editor/contrib/cursorUndo/cursorUndo":["Mit Cursor rückgängig machen","Wiederholen mit Cursor"],
"vs/editor/contrib/documentSymbols/outlineTree":["Die Vordergrundfarbe für Arraysymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für boolesche Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Klassensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Farbsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für konstante Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Konstruktorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Enumeratorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Enumeratormembersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Ereignissymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Feldsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Dateisymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Ordnersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Funktionssymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Schnittstellensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Schlüsselsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Schlüsselwortsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Methodensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Modulsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Namespacesymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für NULL-Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Zahlensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Objektsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Operatorsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Paketsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Eigenschaftensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Referenzsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Codeausschnittsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Zeichenfolgensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Struktursymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Textsymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Typparametersymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für Einheitensymbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt.","Die Vordergrundfarbe für variable Symbole. Diese Symbole werden in den Widgets für Gliederung, Breadcrumbs und Vorschläge angezeigt."],
"vs/editor/contrib/find/findController":["Suchen","&&Suchen","Mit Auswahl suchen","Weitersuchen","Weitersuchen","Vorheriges Element suchen","Vorheriges Element suchen","Nächste Auswahl suchen","Vorherige Auswahl suchen","Ersetzen","&&Ersetzen"],"vs/editor/contrib/find/findWidget":["Suchen","Suchen","Vorheriger Treffer","Nächste Übereinstimmung","In Auswahl suchen","Schließen","Ersetzen","Ersetzen","Ersetzen","Alle ersetzen","Ersetzen-Modus wechseln","Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgeführt.","{0} von {1}","Keine Ergebnisse","{0} gefunden",'{0} für "{1}" gefunden','{0} für "{1}" gefunden, bei {2}','{0} für "{1}" gefunden','STRG+EINGABE fügt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie können die Tastenzuordnung für "editor.action.replaceAll" ändern, um dieses Verhalten außer Kraft zu setzen.'],
"vs/editor/contrib/folding/folding":["Auffalten","Faltung rekursiv aufheben","Falten","Einklappung umschalten","Rekursiv falten","Alle Blockkommentare falten","Alle Regionen falten","Alle Regionen auffalten","Alle falten","Alle auffalten","Faltebene {0}","Hintergrundfarbe hinter gefalteten Bereichen. Die Farbe darf nicht deckend sein, sodass zugrunde liegende Dekorationen nicht ausgeblendet werden.","Farbe des Faltsteuerelements im Editor-Bundsteg."],"vs/editor/contrib/fontZoom/fontZoom":["Editorschriftart vergrößern","Editorschriftart verkleinern","Editor Schriftart Vergrößerung zurücksetzen"],"vs/editor/contrib/format/format":["1 Formatierung in Zeile {0} vorgenommen","{0} Formatierungen in Zeile {1} vorgenommen","1 Formatierung zwischen Zeilen {0} und {1} vorgenommen","{0} Formatierungen zwischen Zeilen {1} und {2} vorgenommen"],"vs/editor/contrib/format/formatActions":["Dokument formatieren","Auswahl formatieren"],
"vs/editor/contrib/gotoError/gotoError":["Gehe zu nächstem Problem (Fehler, Warnung, Information)","Gehe zu vorigem Problem (Fehler, Warnung, Information)","Gehe zu dem nächsten Problem in den Dateien (Fehler, Warnung, Info)","Nächstes &&Problem","Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)","Vorheriges &&Problem"],"vs/editor/contrib/gotoError/gotoErrorWidget":["Fehler","Warnung","Info","Hinweis","{0} bei {1}. ","{0} von {1} Problemen","{0} von {1} Problemen","Editormarkierung: Farbe bei Fehler des Navigationswidgets.","Editormarkierung: Farbe bei Warnung des Navigationswidgets.","Editormarkierung: Farbe bei Information des Navigationswidgets.","Editormarkierung: Hintergrund des Navigationswidgets."],
"vs/editor/contrib/gotoSymbol/goToCommands":["Vorschau","Definitionen",'Keine Definition gefunden für "{0}".',"Keine Definition gefunden","Gehe zu Definition","Gehe &&zu Definition","Definition an der Seite öffnen","Definition einsehen","Deklarationen",'Keine Deklaration für "{0}" gefunden.',"Keine Deklaration gefunden.","Zur Deklaration wechseln","Gehe zu &&Deklaration",'Keine Deklaration für "{0}" gefunden.',"Keine Deklaration gefunden.","Vorschau für Deklaration anzeigen","Typdefinitionen",'Keine Typendefinition gefunden für "{0}"',"Keine Typendefinition gefunden","Zur Typdefinition wechseln","Zur &&Typdefinition wechseln","Vorschau der Typdefinition anzeigen","Implementierungen",'Keine Implementierung gefunden für "{0}"',"Keine Implementierung gefunden","Gehe zu Implementierungen","Gehe zu &&Implementierungen","Vorschau für Implementierungen anzeigen",'Für "{0}" wurden keine Verweise gefunden.',"Keine Referenzen gefunden","Gehe zu Verweisen","Gehe zu &&Verweisen","Verweise","Vorschau für Verweise anzeigen","Verweise","Gehe zu beliebigem Symbol","Speicherorte",'Keine Ergebnisse für "{0}"',"Verweise"],
"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Klicken Sie, um {0} Definitionen anzuzeigen."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Wird geladen...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["{0} Verweise","{0} Verweis","Verweise"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["Keine Vorschau verfügbar.","Keine Ergebnisse","Verweise"],"vs/editor/contrib/gotoSymbol/referencesModel":["Symbol in {0} in Zeile {1}, Spalte {2}","1 Symbol in {0}, vollständiger Pfad {1}","{0} Symbole in {1}, vollständiger Pfad {2}","Es wurden keine Ergebnisse gefunden.","1 Symbol in {0} gefunden","{0} Symbole in {1} gefunden","{0} Symbole in {1} Dateien gefunden"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["Symbol {0} von {1}, {2} für nächstes","Symbol {0} von {1}"],"vs/editor/contrib/hover/hover":["Hovern anzeigen","Definitionsvorschauhover anzeigen"],
"vs/editor/contrib/hover/modesContentHover":["Wird geladen...","Vorschauproblem","Es wird nach Schnellkorrekturen gesucht...","Keine Schnellkorrekturen verfügbar","Schnelle Problembehebung …"],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Durch vorherigen Wert ersetzen","Durch nächsten Wert ersetzen"],"vs/editor/contrib/indentation/indentation":["Einzug in Leerzeichen konvertieren","Einzug in Tabstopps konvertieren","Konfigurierte Tabulatorgröße","Tabulatorgröße für aktuelle Datei auswählen","Einzug mithilfe von Tabstopps","Einzug mithilfe von Leerzeichen","Einzug aus Inhalt erkennen","Neuen Einzug für Zeilen festlegen","Gewählte Zeilen zurückziehen"],
"vs/editor/contrib/linesOperations/linesOperations":["Zeile nach oben kopieren","Zeile nach oben &&kopieren","Zeile nach unten kopieren","Zeile nach unten ko&&pieren","Auswahl duplizieren","&&Auswahl duplizieren","Zeile nach oben verschieben","Zeile nach oben &&verschieben","Zeile nach unten verschieben","Zeile nach &&unten verschieben","Zeilen aufsteigend sortieren","Zeilen absteigend sortieren","Nachgestelltes Leerzeichen kürzen","Zeile löschen","Zeileneinzug","Zeile ausrücken","Zeile oben einfügen","Zeile unten einfügen","Alle übrigen löschen","Alle rechts löschen","Zeilen verknüpfen","Zeichen um den Cursor herum transponieren","In Großbuchstaben umwandeln","In Kleinbuchstaben umwandeln","In große Anfangsbuchstaben umwandeln"],"vs/editor/contrib/links/links":["Befehl ausführen","Link folgen","BEFEHL + Klicken","STRG + Klicken","OPTION + Klicken","alt + klicken","Fehler beim Öffnen dieses Links, weil er nicht wohlgeformt ist: {0}","Fehler beim Öffnen dieses Links, weil das Ziel fehlt.","Link öffnen"],
"vs/editor/contrib/message/messageController":["Ein Bearbeiten ist im schreibgeschützten Editor nicht möglich"],"vs/editor/contrib/multicursor/multicursor":["Cursor oberhalb hinzufügen","Cursor oberh&&alb hinzufügen","Cursor unterhalb hinzufügen","Cursor unterhal&&b hinzufügen","Cursor an Zeilenenden hinzufügen","C&&ursor an Zeilenenden hinzufügen","Cursor am Ende hinzufügen","Cursor am Anfang hinzufügen","Auswahl zur nächsten Übereinstimmungssuche hinzufügen","&&Nächstes Vorkommen hinzufügen","Letzte Auswahl zu vorheriger Übereinstimmungssuche hinzufügen","Vo&&rheriges Vorkommen hinzufügen","Letzte Auswahl in nächste Übereinstimmungssuche verschieben","Letzte Auswahl in vorherige Übereinstimmungssuche verschieben","Alle Vorkommen auswählen und Übereinstimmung suchen","Alle V&&orkommen auswählen","Alle Vorkommen ändern"],"vs/editor/contrib/parameterHints/parameterHints":["Parameterhinweise auslösen"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, Hinweis"],
"vs/editor/contrib/peekView/peekView":["Schließen","Hintergrundfarbe des Titelbereichs der Peek-Ansicht.","Farbe des Titels in der Peek-Ansicht.","Farbe der Titelinformationen in der Peek-Ansicht.","Farbe der Peek-Ansichtsränder und des Pfeils.","Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.","Vordergrundfarbe für Zeilenknoten in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe für Dateiknoten in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des Peek-Editors.","Hintergrundfarbe der Leiste im Peek-Editor.","Farbe für Übereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.","Farbe für Übereinstimmungsmarkierungen im Peek-Editor.","Rahmen für Übereinstimmungsmarkierungen im Peek-Editor."],
"vs/editor/contrib/quickAccess/gotoLineQuickAccess":["Öffnen Sie zuerst einen Text-Editor, um zu einer Zeile zu wechseln.","Wechseln Sie zu Zeile {0} und Spalte {1}.","Zu Zeile {0} wechseln.","Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer zwischen 1 und {2} ein, zu der Sie navigieren möchten.","Aktuelle Zeile: {0}, Zeichen: {1}. Geben Sie eine Zeilennummer ein, zu der Sie navigieren möchten."],
"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess":["Öffnen Sie zunächst einen Text-Editor mit Symbolinformationen, um zu einem Symbol zu navigieren.","Der aktive Text-Editor stellt keine Symbolinformationen bereit.","Keine übereinstimmenden Editorsymbole.","Keine Editorsymbole.","An der Seite öffnen","Unten öffnen","Symbole ({0})","Eigenschaften ({0})","Methoden ({0})","Funktionen ({0})","Konstruktoren ({0})","Variablen ({0})","Klassen ({0})","Strukturen ({0})","Ereignisse ({0})","Operatoren ({0})","Schnittstellen ({0})","Namespaces ({0})","Pakete ({0})","Typparameter ({0})","Module ({0})","Eigenschaften ({0})","Enumerationen ({0})","Enumerationsmember ({0})","Zeichenfolgen ({0})","Dateien ({0})","Arrays ({0})","Zahlen ({0})","Boolesche Werte ({0})","Objekte ({0})","Schlüssel ({0})","Felder ({0})","Konstanten ({0})"],"vs/editor/contrib/rename/onTypeRename":["Symbol bei Eingabe umbenennen","Hintergrundfarbe, wenn der Editor automatisch nach Typ umbenennt."],
"vs/editor/contrib/rename/rename":["Kein Ergebnis.","Ein unbekannter Fehler ist beim Auflösen der Umbenennung eines Ortes aufgetreten.",'"{0}" wird umbenannt.',"{0} wird umbenannt.",'"{0}" erfolgreich in "{1}" umbenannt. Zusammenfassung: {2}',"Die rename-Funktion konnte die Änderungen nicht anwenden.","Die rename-Funktion konnte die Änderungen nicht berechnen.","Symbol umbenennen","Möglichkeit aktivieren/deaktivieren, Änderungen vor dem Umbenennen als Vorschau anzeigen zu lassen"],"vs/editor/contrib/rename/renameInputField":["Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und drücken Sie die EINGABETASTE, um den Commit auszuführen.","{0} zur Umbenennung, {1} zur Vorschau"],"vs/editor/contrib/smartSelect/smartSelect":["Auswahl aufklappen","Auswahl &&erweitern","Markierung verkleinern","Au&&swahl verkleinern"],
"vs/editor/contrib/snippet/snippetVariables":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag","So","Mo","Di","Mi","Do","Fr","Sa","Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember","Jan","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],"vs/editor/contrib/suggest/suggestController":['Das Akzeptieren von "{0}" ergab {1} zusätzliche Bearbeitungen.',"Vorschlag auslösen","{0} zum Einfügen","{0} zum Einfügen","{0} zum Ersetzen","{0} zum Ersetzen","{0} zum Einfügen","weniger anzeigen","mehr anzeigen"],
"vs/editor/contrib/suggest/suggestWidget":["Hintergrundfarbe des Vorschlagswidgets.","Rahmenfarbe des Vorschlagswidgets.","Vordergrundfarbe des Vorschlagswidgets.","Hintergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.","Farbe der Trefferhervorhebung im Vorschlagswidget.","Weitere Informationen ({0})","Weniger lesen ({0})","Wird geladen...","Wird geladen...","Keine Vorschläge.","{0}, Dokumente: {1}","Vorschlagen"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["TAB-Umschalttaste verschiebt Fokus","Beim Drücken auf Tab wird der Fokus jetzt auf das nächste fokussierbare Element verschoben","Beim Drücken von Tab wird jetzt das Tabulator-Zeichen eingefügt"],"vs/editor/contrib/tokenization/tokenization":["Entwickler: Force Retokenize"],
"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators":["Ungewöhnliche Zeilentrennzeichen","Ungewöhnliche Zeilentrennzeichen erkannt",'Diese Datei enthält mindestens ein ungültiges Zeilenabschlusszeichen, z. B. Zeilentrennzeichen (LS) oder Absatztrennzeichen (PS).\r\n\r\nEs wird empfohlen, diese Zeichen aus der Datei zu entfernen. Die betreffende Einstellung kann über "editor.unusualLineTerminators" konfiguriert werden.',"Diese Datei korrigieren","Problem für diese Datei ignorieren"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.","Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.","Übersichtslinealmarkerfarbd für das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Übersichtslinealmarkerfarbe für Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Gehe zur nächsten Symbolhervorhebungen","Gehe zur vorherigen Symbolhervorhebungen","Symbol-Hervorhebung ein-/ausschalten"],
"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})"],"vs/platform/configuration/common/configurationRegistry":["Außerkraftsetzungen für die Standardsprachkonfiguration","Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.","Diese Einstellung unterstützt keine sprachspezifische Konfiguration.",'"{0}" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster "\\\\[.*\\\\]$" zum Beschreiben sprachspezifischer Editor-Einstellungen überein. Verwenden Sie den Beitrag "configurationDefaults".','{0}" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.'],"vs/platform/keybinding/common/abstractKeybindingService":["({0}) wurde gedrückt. Es wird auf die zweite Taste in der Kombination gewartet...","Die Tastenkombination ({0}, {1}) ist kein Befehl."],
"vs/platform/list/browser/listService":["Workbench","Ist unter Windows und Linux der STRG-Taste und unter macOS der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOS der Wahltaste zugeordnet.",'Der Modifizierer zum Hinzufügen eines Elements in Bäumen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in geöffneten Editoren und in der SCM-Ansicht). Die Mausbewegung "Seitlich öffnen" wird – sofern unterstützt – so angepasst, dass kein Konflikt mit dem Modifizierer für Mehrfachauswahl entsteht.',"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus geöffnet werden (sofern unterstützt). Bei übergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das übergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.","Steuert, ob Listen und Strukturen ein horizontales Scrollen in der Workbench unterstützen. Warnung: Das Aktivieren dieser Einstellung kann sich auf die Leistung auswirken.","Steuert den Struktureinzug in Pixeln.","Steuert, ob die Struktur Einzugsführungslinien rendern soll.","Steuert, ob Listen und Strukturen einen optimierten Bildlauf verwenden.","Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe übereinstimmen. Die Übereinstimmungen gelten nur für Präfixe.","Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe übereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.","Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe übereinstimmen.",'Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann "simple" (einfach), "highlight" (hervorheben) und "filter" (filtern) sein.','Legt fest, ob die Tastaturnavigation in Listen und Strukturen automatisch durch Eingaben ausgelöst wird. Wenn der Wert auf "false" festgelegt ist, wird die Tastaturnavigation nur ausgelöst, wenn der Befehl "list.toggleKeyboardNavigation" ausgeführt wird. Diesem Befehl können Sie eine Tastenkombination zuweisen.'],
"vs/platform/markers/common/markers":["Fehler","Warnung","Info"],"vs/platform/quickinput/browser/commandsQuickAccess":["{0}, {1}","zuletzt verwendet","andere Befehle","Der Befehl {0} hat einen Fehler ausgelöst ({1})."],"vs/platform/quickinput/browser/helpQuickAccess":["Globale Befehle","Editor-Befehle","{0}, {1}"],
"vs/platform/theme/common/colorRegistry":["Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.","Allgemeine Vordergrundfarbe für Fehlermeldungen. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.","Die für Symbole in der Workbench verwendete Standardfarbe.","Allgemeine Rahmenfarbe für fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.","Ein zusätzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.","Ein zusätzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.","Vordergrundfarbe für Links im Text.","Hintergrundfarbe für Codeblöcke im Text.","Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.","Hintergrund für Eingabefeld.","Vordergrund für Eingabefeld.","Rahmen für Eingabefeld.","Rahmenfarbe für aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe für aktivierte Optionen in Eingabefeldern.","Vordergrundfarbe für aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.","Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.","Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Information.","Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.","Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.","Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.","Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.","Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.","Rahmenfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.","Hintergrund für Dropdown.","Vordergrund für Dropdown.","Vordergrundfarbe der Schaltfläche.","Hintergrundfarbe der Schaltfläche.","Hintergrundfarbe der Schaltfläche, wenn darauf gezeigt wird.","Hintergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.","Vordergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.","Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.","Hintergrundfarbe vom Scrollbar-Schieber","Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.","Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.","Hintergrundfarbe des Fortschrittbalkens, der für zeitintensive Vorgänge angezeigt werden kann.","Vordergrundfarbe von Fehlerunterstreichungen im Editor.","Randfarbe von Fehlerfeldern im Editor.","Vordergrundfarbe von Warnungsunterstreichungen im Editor.","Randfarbe der Warnfelder im Editor.","Vordergrundfarbe von Informationsunterstreichungen im Editor.","Randfarbe der Infofelder im Editor.","Vordergrundfarbe der Hinweisunterstreichungen im Editor.","Randfarbe der Hinweisfelder im Editor.","Hintergrundfarbe des Editors.","Standardvordergrundfarbe des Editors.","Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.","Vordergrundfarbe für Editorwidgets wie Suchen/Ersetzen.","Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget überschrieben wird.","Rahmenfarbe der Größenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Größenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget außer Kraft gesetzt wird.","Schnellauswahl der Hintergrundfarbe. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Vordergrundfarbe der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Hintergrundfarbe für den Titel der Schnellauswahl. Im Widget für die Schnellauswahl sind Auswahlelemente wie die Befehlspalette enthalten.","Schnellauswahlfarbe für das Gruppieren von Bezeichnungen.","Schnellauswahlfarbe für das Gruppieren von Rahmen.","Farbe der Editor-Auswahl.","Farbe des gewählten Text für einen hohen Kontrast","Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.","Farbe für Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe für Bereiche, deren Inhalt der Auswahl entspricht.","Farbe des aktuellen Suchergebnisses.","Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Randfarbe des aktuellen Suchergebnisses.","Randfarbe der anderen Suchtreffer.","Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hervorhebung unterhalb des Worts, für das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe des Editor-Mauszeigers.","Vordergrundfarbe des Editor-Mauszeigers","Rahmenfarbe des Editor-Mauszeigers.","Hintergrundfarbe der Hoverstatusleiste des Editors.","Farbe der aktiven Links.",'Die für das Aktionssymbol "Glühbirne" verwendete Farbe.','Die für das Aktionssymbol "Automatische Glühbirnenkorrektur" verwendete Farbe.',"Hintergrundfarbe für eingefügten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe für Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Konturfarbe für eingefügten Text.","Konturfarbe für entfernten Text.","Die Rahmenfarbe zwischen zwei Text-Editoren.","Farbe der diagonalen Füllung des Vergleichs-Editors. Die diagonale Füllung wird in Ansichten mit parallelem Vergleich verwendet.","Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Drag & Drop-Hintergrund der Liste/Struktur, wenn Elemente mithilfe der Maus verschoben werden.","Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.","Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine Übereinstimmungen gibt.","Strukturstrichfarbe für die Einzugsführungslinien.","Rahmenfarbe von Menüs.","Vordergrundfarbe von Menüelementen.","Hintergrundfarbe von Menüelementen.","Vordergrundfarbe des ausgewählten Menüelements im Menü.","Hintergrundfarbe des ausgewählten Menüelements im Menü.","Rahmenfarbe des ausgewählten Menüelements im Menü.","Farbe eines Trenner-Menüelements in Menüs.","Hervorhebungs-Hintergrundfarbe eines Codeausschnitt-Tabstopps.","Hervorhebungs-Rahmenfarbe eines Codeausschnitt-Tabstopps.","Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeausschnitts.","Rahmenfarbe zur Hervorhebung des letzten Tabstopps eines Codeausschnitts.","Übersichtslinealmarkerfarbe für das Suchen von Übereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Übersichtslinealmarkerfarbe für das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Minimap-Markerfarbe für gefundene Übereinstimmungen.","Minimap-Markerfarbe für die Editorauswahl.","Minimapmarkerfarbe für Fehler","Minimapmarkerfarbe für Warnungen","Hintergrundfarbe der Minimap.","Hintergrundfarbe des Minimap-Schiebereglers.","Hintergrundfarbe des Minimap-Schiebereglers beim Daraufzeigen.","Hintergrundfarbe des Minimap-Schiebereglers, wenn darauf geklickt wird.","Die Farbe, die für das Problemfehlersymbol verwendet wird.","Die Farbe, die für das Problemwarnsymbol verwendet wird.","Die Farbe, die für das Probleminfosymbol verwendet wird."],
"vs/platform/undoRedo/common/undoRedoService":["Die folgenden Dateien wurden geschlossen und auf dem Datenträger geändert: {0}.","Die folgenden Dateien wurden auf inkompatible Weise geändert: {0}.",'"{0}" konnte nicht für alle Dateien rückgängig gemacht werden. {1}','"{0}" konnte nicht für alle Dateien rückgängig gemacht werden. {1}','"{0}" konnte nicht für alle Dateien rückgängig gemacht werden, da Änderungen an {1} vorgenommen wurden.','"{0}" konnte nicht für alle Dateien rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für "{1}" durchgeführt wird.','"{0}" konnte nicht für alle Dateien rückgängig gemacht werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.','Möchten Sie "{0}" für alle Dateien rückgängig machen?',"In {0} Dateien rückgängig machen","Datei rückgängig machen","Abbrechen",'"{0}" konnte nicht rückgängig gemacht werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.','"{0}" konnte nicht in allen Dateien wiederholt werden. {1}','"{0}" konnte nicht in allen Dateien wiederholt werden. {1}','"{0}" konnte nicht in allen Dateien wiederholt werden, da Änderungen an {1} vorgenommen wurden.','"{0}" konnte nicht für alle Dateien wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen für "{1}" durchgeführt wird.','"{0}" konnte nicht für alle Dateien wiederholt werden, weil in der Zwischenzeit bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wurde.','"{0}" konnte nicht wiederholt werden, weil bereits ein Vorgang zum Rückgängigmachen oder Wiederholen durchgeführt wird.']
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.de.js.map