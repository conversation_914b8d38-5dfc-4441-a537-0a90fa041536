<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF 预览</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        background-color: #f4f4f9;
      }
      #viewerContainer {
        width: 80%;
        height: 80vh;
        border: 1px solid #ccc;
      }
    </style>
  </head>
  <body>
    <div id="viewerContainer"></div>

    <!-- 引入PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf_viewer.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/event_utils.min.js"></script>

    <script>
      // PDF.js Web Worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js"

      // 获取PDF文件路径
      const url = "http://**********:10004/ewis/WebAnnexFile/67d3cecfbe567b2e61d2d4ee.pdf"

      // 获取PDF.js Viewer容器
      const pdfViewerContainer = document.getElementById("viewerContainer")

      // 创建PDF.js Viewer实例
      const eventBus = new pdfjsViewer.EventBus()
      const pdfViewer = new pdfjsViewer.PDFViewer({
        container: pdfViewerContainer,
        eventBus: eventBus,
        enhanceTextSelection: true
      })

      // 设置PDF.js Viewer实例
      document.getElementById("viewerContainer").appendChild(pdfViewer.viewer)

      // 加载PDF文件
      const loadingTask = pdfjsLib.getDocument(url)
      loadingTask.promise
        .then((pdf) => {
          pdfViewer.setDocument(pdf)
          eventBus.dispatch("pagesinit", { source: pdfViewer })
        })
        .catch((error) => {
          console.error("无法加载PDF文件", error)
        })
    </script>
  </body>
</html>
