import request from '@/utils/request'

/**
 * @name 创建产品型号
 * @param {*} param0.modelId 产品id
 * @param {*} param0.modelCode 产品型号编码
 * @param {*} param0.modelName 产品型号名称
 * @param {*} param0.modelStatus 产品状态
 * @param {*} param0.modelType 产品型号
 * @param {*} param0.demandCode 需求单code
 * @param {*} param0.demandMajor 需求单大版本
 * @param {*} param0.demandVersion 需求单小版本
 * @param {*} param0.modelVersion 产品型号版本号
 * @param {*} param0.delFlag 删除 delFlag = 1
 * @param {*} param0.materialType 物料类型
 * @param {*} param0.materialTypeStr  物料数组
 * @param {*} param0.category 知识库目录
 * @param {*} param0.scopeStatement 适用范围说明
 * @param {*} param0.baselineType 创建的基线
 * @param {*} param0.fileJsons 相关文件
 * @param {*} param0.projectCode 项目code
 * @returns 
 */
export function saveProduct({
  modelId,
  modelCode,
  modelName,
  modelStatus,
  modelType,
  demandCode,
  demandMajor,
  demandVersion,
  modelVersion,
  delFlag,
  materialType,
  materialTypeStr,
  innerId,
  innerName,
  objType,
  category,
  scopeStatement,
  baselineType,
  fileJsons,
  projectCode,
  typeCode
}) {
  return request({
    url: '/api/productModel/saveOrUpdateProduct',
    method: 'post',
    data: {
      modelId,
      modelCode,
      modelName,
      modelStatus,
      modelType,
      demandCode,
      demandMajor,
      demandVersion,
      modelVersion,
      delFlag,
      materialType,
      materialTypeStr,
      innerId,
      innerName,
      objType,
      category,
      scopeStatement,
      baselineType,
      fileJsons,
      projectCode,
      typeCode
    }
  })
}

/**
 * @name 编辑产品型号
 * @param {*} param0.modelId 产品id
 * @param {*} param0.modelCode 产品型号编码
 * @param {*} param0.modelName 产品型号名称
 * @param {*} param0.modelStatus 产品状态
 * @param {*} param0.modelType 产品型号
 * @param {*} param0.demandCode 需求单code
 * @param {*} param0.demandMajor 需求单大版本
 * @param {*} param0.demandVersion 需求单小版本
 * @param {*} param0.modelVersion 产品型号版本号
 * @param {*} param0.delFlag 删除 delFlag = 1
 * @param {*} param0.materialType 物料类型
 * @param {*} param0.materialTypeStr  物料数组
 * @param {*} param0.category 知识库目录
 * @returns 
 */
export function updateProduct({
  modelId,
  modelCode,
  modelName,
  modelStatus,
  modelType,
  demandCode,
  demandMajor,
  demandVersion,
  modelVersion,
  delFlag,
  materialType,
  materialTypeStr,
  innerId,
  innerName,
  objType,
  category,
  scopeStatement,
  baselineType,
  fileJsons,
  projectCode,
  typeCode
}) {
  return request({
    url: '/api/productModel/saveOrUpdateProduct',
    method: 'PUT',
    data: {
      modelId,
      modelCode,
      modelName,
      modelStatus,
      modelType,
      demandCode,
      demandMajor,
      demandVersion,
      modelVersion,
      delFlag,
      materialType,
      materialTypeStr,
      innerId,
      innerName,
      objType,
      category,
      scopeStatement,
      baselineType,
      fileJsons,
      projectCode,
      typeCode
    }
  })
}

/**
 * @name 需求单列表
 * @param {*} demandName 需求单名字
 * @returns 
 */
export function getDemandList({
  demandName,
  pageSize,
  currentPage,
}) {
  return request({
    url: `/api/productModel/getDemandList`,
    method: 'GET',
    data: {
      demandName,
      pageSize,
      currentPage,
    }
  })
}

/**
 * @name 产品型号档案列表
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.modelCode 产品型号编码
 * @param {*} param0.modelName 产品型号名称
 * @returns 
 */
export const getProductModelList = ({
  pageSize,
  currentPage,
  modelCode,
  modelName,
  auditStatus,
  sidx,
  sort
}) => request({
  url: `/api/productModel/getProductModelList`,
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    modelCode,
    modelName,
    auditStatus,
    sidx,
    sort
  }
})

/**
 * @name 通过id查看详情
 * @param {*} param0.modelId ID
 * @returns 
 */
export const getProductInfoById = ({
  modelId
}) => request({
  url: `/api/productModel/getProductInfoById/?modelId=${modelId}`,
  method: 'GET',
  data: {

  }
})

/**
 * @name 展示配置左侧树形
 * @param {*} param0.modelCode 产品型号编码
 * @returns 
 */
export const getCfgTree = ({
  modelCode
}) => request({
  url: '/api/productModelConfig/getConfigTree',
  method: 'GET',
  data: {
    modelCode
  }
})

/**
 * @name 创建保存产品型号配置
 * @param {*} param0.configId 产品型号id
 * @param {*} param0.configCode 产品型号code
 * @param {*} param0.parentId 父级id 
 * @param {*} param0.modelCode 产品code
 * @param {*} param0.configName 名称
 * @param {*} param0.model 型号
 * @param {*} param0.unit 单位
 * @param {*} param0.businessType 业务类型
 * @param {*} param0.materialCode
 * @param {*} param0.materialVersion
 * @param {*} param0.modelVersion
 * @param {*} param0.materialName 物料名称
 * @param {*} param0.delFlag 1删除
 * @param {*} param0.dynamicFiledList 扩展属性
 * @param {*} param0.materialType 物料类型-值
 * @param {*} param0.materialTypeStr 物料类型-父级拼接
 * @param {*} param0.batchOrSortie 批次 货号有效性
 * @param {*} param0.startDay 开始时间
 * @param {*} param0.endDay 结束时间
 * @param {*} param0.optionsValue 选型有效性
 * @returns 
 */
export const addProModelCfg = ({
  configCode,
  configId,
  parentId,
  modelCode,
  configName,
  model,
  unit,
  businessType,
  materialCode,
  materialVersion,
  modelVersion,
  materialName,
  delFlag,
  dynamicFiledList,
  materialType,
  materialTypeStr,
  innerId,
  innerName,
  objType,
  batchOrSortie,
  startDay,
  endDay,
  optionsValue,
  isLogicalCIParent,
  effectiveness,
  typeCode
}) => request({
  url: '/api/productModelConfig/saveOrUpdateConfig',
  method: 'POST',
  data: {
    configCode,
    configId,
    parentId,
    modelCode,
    configName,
    model,
    unit,
    businessType,
    materialCode,
    materialVersion,
    modelVersion,
    materialName,
    delFlag,
    dynamicFiledList,
    materialType,
    materialTypeStr,
    innerId,
    innerName,
    objType,
    batchOrSortie,
    startDay,
    endDay,
    optionsValue,
    isLogicalCIParent,
    effectiveness,
    typeCode
  }
})

/**
 * @name 查看产品型号配置详情
 * @param {*} param0.configId id
 * @returns 
 */
export const getConfigInfo = ({
  configId
}) => request({
  url: '/api/productModelConfig/getConfigInfo',
  method: 'GET',
  data: {
    configId
  }
})

/**
 * @name 新增技术状态文件及数据
 * @param {*} param0.configCode 配置code
 * @param {*} param0.dataName 数据名称
 * @param {*} param0.objType 对象类型
 * @param {*} param0.baseline 所属基线，逗号分隔
 * @param {*} param0.delFlag 1删除
 * @param {*} param0.dataId 删除用
 * @param {*} param0.innerName 
 * @param {*} param0.objVersion 版本
 * @returns 
 */
export const addNewDefine = ({
  configCode,
  dataName,
  objType,
  baseline,
  delFlag,
  dataId,
  objCode,
  objName,
  innerName,
  innerId,
  objVersion,
  types,
  typeCode
}) => request({
  url: '/api/productModelConfigData/saveOrUpdateConfigData',
  method: 'POST',
  data: {
    configCode,
    dataName,
    objType,
    baseline,
    delFlag,
    dataId,
    objCode,
    objName,
    innerName,
    innerId,
    objVersion,
    types,
    typeCode
  }
})

// export const delDefinition

/**
 * @name 技术状态文集及数据列表
 * @param {*} param0.configCode 配置ID
 * @returns 
 */
export const getCfgDataList = ({
  configCode,
  currentPage,
  pageSize
}) => request({
  url: '/api/productModelConfigData/getConfigDataList',
  method: 'GET',
  data: {
    configCode,
    currentPage,
    pageSize
  }
})

/**
 * @name 获取基线列表
 * @param {*} param0.baselineName 基线名称 
 * @param {*} param0.baselineType 基线类型 
 * @param {*} param0.pageSize 条数 
 * @param {*} param0.currentPage 当前页 
 * @param {*} param0.modelVersion 版本 
 * @param {*} param0.modelCode code 
 * @param {*} param0.isFrozen 冻结状态 
 * @param {*} param0.createBy 创建人 
 * @param {*} param0.effectiveness 有效性类型 
 * @returns 
 */
export const getBaseLineList = ({
  baselineName,
  baselineType,
  pageSize,
  currentPage,
  modelVersion,
  modelCode,
  isFrozen,
  createBy,
  effectiveness
}) => request({
  url: '/api/productModelBaseline/getBaselineList',
  method: 'GET',
  data: {
    baselineName,
    baselineType,
    pageSize,
    currentPage,
    modelVersion,
    modelCode,
    isFrozen,
    createBy,
    effectiveness
  }
})

/**
 * @name 创建|删除基线
 * @param {*} param0.baselineId 基线id
 * @param {*} param0.modelCode 产品code
 * @param {*} param0.modelVersion 产品版本
 * @param {*} param0.baselineName 基线名称
 * @param {*} param0.effectiveness 有效性
 * @param {*} param0.baselineType 基线类型
 * @param {*} param0.describes 描述
 * @param {*} param0.delFlag 删除 delFlag = 1
 * @param {*} param0.batchOrSortie 批次 货号有效性
 * @param {*} param0.startDay 开始时间
 * @param {*} param0.endDay 结束时间
 * @returns 
 */
export const saveOrUpdateBaseline = ({
  baselineId,
  modelCode,
  modelVersion,
  baselineName,
  effectiveness,
  baselineType,
  describes,
  delFlag,
  batchOrSortie,
  startDay,
  endDay,
  category,
  optionsValue,
  optionsValueStr
}) => request({
  url: '/api/productModelBaseline/saveOrUpdateBaseline',
  method: 'POST',
  data: {
    baselineId,
    modelCode,
    modelVersion,
    baselineName,
    effectiveness,
    baselineType,
    describes,
    delFlag,
    batchOrSortie,
    startDay,
    endDay,
    category,
    optionsValue,
    optionsValueStr
  }
})

/**
 * @name 编辑基线
 * @param {*} param0.baselineId 基线id
 * @param {*} param0.modelCode 产品code
 * @param {*} param0.modelVersion 产品版本
 * @param {*} param0.baselineName 基线名称
 * @param {*} param0.effectiveness 有效性
 * @param {*} param0.baselineType 基线类型
 * @param {*} param0.describes 描述
 * @param {*} param0.delFlag 删除 delFlag = 1
 * @param {*} param0.batchOrSortie 批次 货号有效性
 * @param {*} param0.startDay 开始时间
 * @param {*} param0.endDay 结束时间
 * @returns 
 */
export const editBaseline = ({
  baselineId,
  modelCode,
  modelVersion,
  baselineName,
  effectiveness,
  baselineType,
  describes,
  delFlag,
  batchOrSortie,
  startDay,
  endDay,
  category, optionsValue, optionsValueStr
}) => request({
  url: '/api/productModelBaseline/saveOrUpdateBaseline',
  method: 'PUT',
  data: {
    baselineId,
    modelCode,
    modelVersion,
    baselineName,
    effectiveness,
    baselineType,
    describes,
    delFlag,
    batchOrSortie,
    startDay,
    endDay,
    category,
    optionsValue, optionsValueStr
  }
})

/**
 * @name 查看基线
 * @param {*} param0.baselineId 基线id
 * @param {*} param0.modelCode 产品code
 * @param {*} param0.modelVersion 产品版本
 * @param {*} param0.baselineType 基线类型
 * @returns 
 */
export const getBaselineInfo = ({
  baselineId,
  modelCode,
  modelVersion,
  baselineType
}) => request({
  url: '/api/productModelBaseline/getBaselineInfo',
  method: 'GET',
  data: {
    baselineId,
    modelCode,
    modelVersion,
    baselineType
  }
})

/**
 * @name 查询物料库数据
 * @param {*} param0.materialName 物料名
 * @returns 
 */
export const getgetMaterialList = ({
  materialName
}) => request({
  url: 'api/productModelConfig/getMaterialList',
  method: 'GET',
  data: {
    materialName
  }
})

/**
 * @name 冻结|取消冻结
 * @param {*} param0.isFrozen 是否冻结标识 0：正常 1：冻结
 * @param {*} param0.baselineId 基线id
 * @returns 
 */
export const updateBaselineFrozen = ({
  isFrozen,
  baselineId
}) => request({
  url: '/api/productModelBaseline/updateBaselineFrozen',
  method: 'PUT',
  data: {
    isFrozen,
    baselineId
  }
})

/**
 * @name 验证是否能够冻结
 * @param {*} param0.modelCode 产品code
 * @param {*} param0.baselineType 基线类型
 * @returns 
 */
export const isBaselineFrozen = ({
  modelCode,
  baselineType
}) => request({
  url: '/api/productModelBaseline/isBaselineFrozen',
  method: 'GET',
  data: {
    modelCode,
    baselineType
  }
})

/**
 * @name 根据innerName获取当前对象数据
 * @param {*} param0.innerName innerName
 * @param {*} param0.formCode 业务code
 * @param {*} param0.formName 业务名称
 * @param {*} param0.typeCode 类型编码
 * @param {*} param0.innerId innerId
 * @returns 
 */
export const getObjDataByInnerName = ({
  innerName,
  formCode,
  formName,
  innerId,
  typeCode
}) => request({
  url: '/api/businessCommon/chose/page',
  method: 'GET',
  data: {
    isQueryAll: true,
    innerName,
    formCode,
    formName,
    innerId,
    typeCode
  }
})

/**
 * @name 查询版本
 * @param {*} param0.formCode code
 * @returns 
 */
export const getVersionsByCode = ({ formCode, innerName, innerId }) => request({
  url: '/api/businessCommon/chose/versions',
  method: 'GET',
  data: {
    currentPage: 1,
    pageSize: 10,
    isQueryAll: false,
    formCode,
    innerName,
    innerId
  }
})

/**
 * @name 删除基线
 * @param {*} param0.modelCode 产品型号code
 * @param {*} param0.baselineId 基线id
 * @param {*} param0.delFlag 1=删除
 * @param {*} param0.baselineType 基线类型
 * @returns 
 */
export const delBaseline = ({
  modelCode,
  baselineId,
  delFlag,
  baselineType
}) => request({
  url: '/api/productModelBaseline/delBaseline',
  method: 'DELETE',
  data: {
    modelCode,
    baselineId,
    delFlag,
    baselineType
  }
})

/**
 * @name 配置删除
 * @param {*} param0.configId 配置id
 * @param {*} param0.delFlag 1=删除 
 * @returns 
 */
export const delConfig = ({
  configId,
  delFlag
}) => request({
  url: '/api/productModelConfig/delConfig',
  method: 'DELETE',
  data: {
    configId,
    delFlag
  }
})

/**
 * @name 获取产品型号下拉框
 * @returns 
 */
export const getModelOptions = () => request({
  url: '/api/productModel/getModelList',
  method: 'GET'
})

/**获取项目列表 */
export const getProduct = ({
  currentPage,
  pageSize,
  projectStatus,
  serviceType,
  projectName,
  projectLeader,
}) => request({
  url: '/api/project/list/product',
  method: 'GET',
  data: {
    currentPage,
    pageSize,
    projectStatus,
    serviceType,
    projectName,
    projectLeader,
  }
})

/**
 * @name 编辑有效性
 * @param {*} param0.modelCode 产品型号编码
 * @param {*} param0.effectiveness 选中有效性
 * @param {*} param0.batchOrSortie 批次或者架次
 * @param {*} param0.startDay 时间有效性开始时间
 * @param {*} param0.endDay 时间有效性结束时间
 * @param {*} param0.optionsValue 选项有效性值集合
 * @returns 
 */
export const updateEffectiveness = ({
  modelCode,
  effectiveness,
  batchOrSortie,
  startDay,
  endDay,
  optionsValue
}) => request({
  url: '/api/productModel/updateEffectiveness',
  method: 'PUT',
  data: {
    modelCode,
    effectiveness,
    batchOrSortie,
    startDay,
    endDay,
    optionsValue
  }
})

/**
 * @name 查看产品型号有效性
 * @param {*} param0.modelCode 产品code
 * @returns 
 */
export const getEffectivenessInfo = ({
  modelCode
}) => request({
  url: '/api/productModel/getEffectivenessInfo',
  method: 'GET',
  data: {
    modelCode
  }
})

/**
 * @name 设置逻辑CI
 * @param {*} param0 
 * @returns 
 */
export const updateLogicalCI = ({
  configCode,
  isLogicalCI,
  modelCode,
  configId
}) => request({
  url: '/api/productModelConfig/updateLogicalCI',
  method: 'PUT',
  data: {
    configCode,
    isLogicalCI,
    modelCode,
    configId
  }
})

/**
 * @name 获取变更设计列表
 * @param {*} param0.pageSize 页码数
 * @param {*} param0.currentPage 当前页
 * @returns 
 */
export const getChangeUnifiedInfo = ({
  pageSize,
  currentPage,
  typeCodeList
}) => request({
  url: '/api/changeContact/getChangeUnifiedInfo',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    typeCodeList
  }
})

/**
 * @name 分类型获取所有基线
 * @param {*} param0.modelCode 产品型号档案code
 * @param {*} param0.baselineType 基线类型
 * @returns 
 */
export const getBaselineFile = ({
  modelCode,
  baselineType
}) => request({
  url: '/api/productModelBaseline/getBaselineFile',
  method: 'GET',
  data: {
    modelCode,
    baselineType
  }
})

/**
 * @name 根据产品型号获取基线类型
 * @param {*} param0.modelCode 产品型号档案code
 * @returns 
 */
export const getModelBaselineType = ({
  modelCode
}) => request({
  url: '/api/productModel/getModelBaselineType',
  method: 'GET',
  data: {
    modelCode
  }
})

/**
 * @name 引入项目
 * @param {*} data 
 * @returns 
 */
export const importConfig = (data) => request({
  url: '/api/productModelConfig/importConfig',
  method: 'POST',
  data
})

/**
 * @name 编辑定义
 * @param {*} param0.configCode 配置code
 * @param {*} param0.dataName 数据名称
 * @param {*} param0.objType 对象类型
 * @param {*} param0.baseline 所属基线，逗号分隔
 * @param {*} param0.delFlag 1删除
 * @param {*} param0.dataId 删除用
 * @param {*} param0.innerName 
 * @param {*} param0.objVersion 版本
 * @returns 
 */
export const updateConfigData = ({
  configCode,
  dataName,
  objType,
  baseline,
  delFlag,
  dataId,
  objCode,
  objName,
  innerName,
  innerId,
  objVersion,
  types,
  typeCode
}) => request({
  url: '/api/productModelConfigData/updateConfigData',
  method: 'PUT',
  data: {
    dataId,
    configCode,
    dataName,
    objType,
    baseline,
    delFlag,
    objCode,
    objName,
    innerName,
    innerId,
    objVersion,
    types,
    typeCode
  }
})

/**
 * @name 查看定义详情
 * @param {*} dataId 
 * @returns 
 */
export const getConfigDataInfo = (dataId) => request({
  url: `/api/productModelConfigData/getConfigDataInfo/${dataId}`,
  method: 'GET'
})

/**
 * @name 新增产品型号档案模版
 * @returns 
 */
export const insetProductModel = ({
  modelName,
  remark
}) => request({
  url: '/api/productModel/template/insetProductModel',
  method: 'POST',
  data: {
    modelName,
    remark,
  }
})

/**
 * @name 编辑产品型号模版
 * @returns 
 */
export const updateProductModel = ({
  modelId,
  modelName,
  remark,
}) => request({
  url: '/api/productModel/template/updateProductModel',
  method: 'PUT',
  data: {
    modelId,
    modelName,
    remark,
  }
})

/**
 * @name 产品型号模版列表
 * @param {*} param0 
 * @returns 
 */
export const getTemplateList = ({
  currentPage,
  pageSize,
  modelName,
  modelStatus,
  modeCode,
  sidx,
  sort
}) => request({
  url: '/api/productModel/template/getTemplateList',
  method: 'GET',
  data: {
    currentPage,
    pageSize,
    modelName,
    modelStatus,
    modeCode,
    sidx,
    sort
  }
})

/**
 * @name 启/停用模版-产品型号
 * @param {*} param0 
 * @returns 
 */
export const offOnProductModel = ({
  modelId, modelStatus
}) => request({
  url: `/api/productModel/template/offOnProductModel/${modelId}/${modelStatus}`,
  method: 'PUT'
})

/**
 * @name 删除模版-产品型号
 * @param {*} param0 
 * @returns 
 */
export const delProductModel = ({
  modelId
}) => request({
  url: `/api/productModel/template/delProductModel/${modelId}`,
  method: 'DELETE'
})

/**
 * @name 导入产品型号档案模版
 * @param {*} param0.modelCode
 * @param {*} param0.modelName
 * @param {*} param0.category 知识库目录
 * @param {*} param0.projectCode 项目code
 * @param {*} param0.materialType 物料类型
 * @param {*} param0.materialTypeStr 物料数组
 * @returns 
 */
export const importProduct = ({
  modelCode,
  modelName,
  category,
  projectCode,
  materialType,
  materialTypeStr
}) => request({
  url: '/api/productModel/importProduct',
  method: 'POST',
  data: {
    modelCode,
    modelName,
    category,
    projectCode,
    materialType,
    materialTypeStr
  }
})