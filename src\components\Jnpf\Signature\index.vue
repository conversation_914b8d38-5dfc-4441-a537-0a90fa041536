<template>
  <div>
    <div class="signature-main">
      <el-image :src="define.comUrl+innerValue" alt="" v-if="innerValue" class="signature-img"
        :preview-src-list="previewSrcList" />
      <div @click="openDialog" class="signature-style" :class="{ 'disabled-btn': disabled }"
        v-if="!detailed">
        <i class="icon-ym icon-ym-signature1 add-signature"></i>
        <span class="signature-title" v-if="!innerValue">{{signTip}}</span>
      </div>
    </div>
    <el-dialog title="选择签章" :close-on-click-modal="false" append-to-body
      :visible.sync="dialogVisible" lock-scroll width="600px" destroy-on-close>
      <el-form ref="dataForm" :model="dataForm" v-loading="formLoading" label-width="100px">
        <el-form-item label="电子签章" prop="fullName">
          <jnpf-select v-model="dataForm.innerValue" :options="options" filterable :props="props"
            style="width:100%" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible= false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getListByIds } from '@/api/system/signature'
export default {
  name: 'JnpfSignature',
  props: {
    value: { type: String, default: '' },
    signTip: { type: String, default: '电子签章' },
    disabled: { type: Boolean, default: false },
    detailed: { type: Boolean, default: false },
    ableIds: { type: Array, default: () => [] }
  },
  data() {
    return {
      dialogVisible: false,
      innerValue: '',
      formLoading: false,
      options: [],
      dataForm: {
        innerValue: ''
      },
      props: {
        label: "fullName",
        value: "icon"
      },
      previewSrcList: []
    }
  },
  watch: {
    value: {
      handler(val) {
        this.innerValue = val || ''
        this.dataForm.innerValue = val || ''
        if (this.innerValue) this.previewSrcList = [this.define.comUrl + this.innerValue]
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    openDialog() {
      if (this.disabled) return
      this.dialogVisible = true;
      if (this.ableIds.length) {
        getListByIds({ ids: this.ableIds }).then(res => {
          this.options = res.data.list
        })
      }
    },
    submit() {
      this.innerValue = this.dataForm.innerValue
      this.$emit('input', this.innerValue)
      this.$emit('change', this.innerValue)
      this.previewSrcList = [this.define.comUrl + this.innerValue]
      this.dialogVisible = false
    },
  }
}
</script>
<style lang="scss" scoped>
.signature-img {
  width: 80px;
  height: 40px;
  object-fit: contain;
  cursor: pointer;
}
.add-signature {
  font-size: 28px !important;
}
.signature-style {
  height: 40px;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #2188ff;
  cursor: pointer;
  &.disabled-btn {
    cursor: no-drop !important;
  }
  .signature-title {
    height: 30px;
    font-size: 14px;
    margin-top: 4px;
  }
}
</style>
