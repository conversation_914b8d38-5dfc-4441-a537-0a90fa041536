import request from '@/utils/request'
import qs from 'qs'

// 获取项目模板列表
export function getProjectTemplateList(data) {
    return request({
        url: `/api/project/template`,
        method: 'GET',
        data,
    })
}

// 新建项目模板
export function addProjectTemplate(data) {
    return request({
        url: `/api/project/template`,
        method: 'POST',
        data
    })
}

// 修改项目模板
export function updateProjectTemplate(data) {
    return request({
        url: `/api/project/template`,
        method: 'PUT',
        data
    })
}

// 项目模板生效不生效
export function SetProjectTemplateStatus(data) {
    return request({
        url: `/api/project/template/effect`,
        method: 'PUT',
        data
    })
}

// 删除项目模板
export function deleteProjectTemplate(id) {
    return request({
        url: `/api/project/template/${id}`,
        method: 'DELETE',
    })
}


// 获取项目任务列表
export function getProjectTaskList(data) {
    return request({
        url: `/api/project/template/task`,
        method: 'GET',
        data,
    })
}

// 新建项目模板任务
export function addProjectTask(data) {
    return request({
        url: `/api/project/template/task`,
        method: 'POST',
        data
    })
}

// 修改项目模板任务
export function updateProjectTask(data) {
    return request({
        url: `/api/project/template/task`,
        method: 'PUT',
        data
    })
}

// 删除项目模板任务
export function deleteProjectTask(id) {
    return request({
        url: `/api/project/template/task/${id}`,
        method: 'DELETE',
    })
}

// 项目模板任务详情
export function getProjectTaskDetail(id) {
    return request({
        url: `/api/project/template/task/${id}`,
        method: 'GET',
    })
}


// 项目模板任务树形列表
export function getProjectTaskTree(id) {
    return request({
        url: `/api/project/template/task/tree/${id}`,
        method: 'GET',
    })
}


//获取甘特图数据
export function getGanttData(data) {
    return request({
        url: `/api/project/task/list`,
        method: 'GET',
        data
    })
}