import request from '@/utils/request'
// 分页查询角色项目权限
export function getProjectRuleList(data) {
  return request({
    url: `/api/project/role/power`,
    method: 'GET',
    data
  })
}

// 新增项目角色
export function addProjectRule(data) {
  return request({
    url: `/api/project/role/power`,
    method: 'POST',
    data
  })
}

// 修改项目角色
export function updateProjectRule(data) {
  return request({
    url: `/api/project/role/power`,
    method: 'PUT',
    data
  })
}

// 删除项目角色
export function deleteProjectRule(id) {
  return request({
    url: `/api/project/role/power/${id}`,
    method: 'DELETE',
  })
}

// 角色授权
export function setProjectRule(data) {
  return request({
    url: `/api/project/role/power/author`,
    method: 'POST',
    data
  })
}

// 获取权限列表
export function getProjectRule(roleId) {
  return request({
    url: `/api/project/role/power/${roleId}`,
    method: 'GET',
  })
}