<template>
  <div class="category-select-demo">
    <div class="demo-container">
      <h2>分类号选择组件演示</h2>
      
      <el-form :model="form" label-width="120px" style="max-width: 600px;">
        <el-form-item label="选择分类号：">
          <CategorySelect 
            v-model="form.categoryCode" 
            placeholder="请选择分类号"
            :category-data="categoryData"
            @change="handleCategoryChange"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="当前选中值：">
          <el-input v-model="form.categoryCode" readonly />
        </el-form-item>
        
        <el-form-item label="选中的分类信息：">
          <el-input 
            type="textarea" 
            :rows="4" 
            v-model="selectedCategoryInfo" 
            readonly 
          />
        </el-form-item>
      </el-form>
      
      <div class="demo-description">
        <h3>使用说明：</h3>
        <ul>
          <li>点击输入框打开分类选择弹窗</li>
          <li>可以通过分类号/名称搜索过滤数据</li>
          <li>双击表格行选择分类（只有最后一层才能选择）</li>
          <li>支持清空选择</li>
          <li>选择后会触发change事件，返回分类代码和完整分类信息</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import CategorySelect from '@/components/CategorySelect/index.vue'

export default {
  name: 'CategorySelectDemo',
  components: {
    CategorySelect
  },
  data() {
    return {
      form: {
        categoryCode: ''
      },
      selectedCategoryInfo: '',
      // 自定义分类数据示例
      categoryData: [
        {
          id: '0',
          categoryCode: '0',
          categoryName: '文件',
          remark: '文件类别',
          children: [
            {
              id: '01',
              categoryCode: '01',
              categoryName: '技术文件',
              remark: '技术相关文件',
              children: [
                {
                  id: '011',
                  categoryCode: '011',
                  categoryName: '设计图纸',
                  remark: '工程设计图纸',
                  children: []
                },
                {
                  id: '012',
                  categoryCode: '012',
                  categoryName: '技术规范',
                  remark: '技术标准规范',
                  children: []
                }
              ]
            },
            {
              id: '02',
              categoryCode: '02',
              categoryName: '管理文件',
              remark: '管理相关文件',
              children: [
                {
                  id: '021',
                  categoryCode: '021',
                  categoryName: '制度文件',
                  remark: '公司制度文件',
                  children: []
                }
              ]
            }
          ]
        },
        {
          id: '1',
          categoryCode: '1',
          categoryName: '成套设备',
          remark: '完整的设备系统',
          children: [
            {
              id: '11',
              categoryCode: '11',
              categoryName: '生产设备',
              remark: '用于生产的设备',
              children: [
                {
                  id: '111',
                  categoryCode: '111',
                  categoryName: '加工设备',
                  remark: '机械加工设备',
                  children: []
                },
                {
                  id: '112',
                  categoryCode: '112',
                  categoryName: '检测设备',
                  remark: '质量检测设备',
                  children: []
                }
              ]
            }
          ]
        },
        {
          id: '2',
          categoryCode: '2',
          categoryName: '整件',
          remark: '完整的零部件',
          children: []
        },
        {
          id: '3',
          categoryCode: '3',
          categoryName: '部件',
          remark: '设备的组成部分',
          children: []
        },
        {
          id: '4',
          categoryCode: '4',
          categoryName: '零件',
          remark: '最小的组成单元',
          children: []
        }
      ]
    }
  },
  methods: {
    handleCategoryChange(value, categoryInfo) {
      console.log('分类选择变化:', value, categoryInfo)
      if (categoryInfo) {
        this.selectedCategoryInfo = JSON.stringify(categoryInfo, null, 2)
      } else {
        this.selectedCategoryInfo = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.category-select-demo {
  padding: 20px;
  
  .demo-container {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .demo-description {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    h3 {
      margin-top: 0;
      color: #303133;
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        color: #606266;
        line-height: 1.5;
      }
    }
  }
}
</style>
