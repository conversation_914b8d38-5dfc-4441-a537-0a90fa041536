# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=上一頁
previous_label=上一頁
next.title=下一頁
next_label=下一頁

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=第
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=頁，共 {{pagesCount}} 頁
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=（第 {{pageNumber}} 頁，共 {{pagesCount}} 頁）

zoom_out.title=縮小
zoom_out_label=縮小
zoom_in.title=放大
zoom_in_label=放大
zoom.title=縮放
presentation_mode.title=切換至簡報模式
presentation_mode_label=簡報模式
open_file.title=開啟檔案
open_file_label=開啟
print.title=列印
print_label=列印
download.title=下載
download_label=下載
bookmark.title=目前檢視的內容（複製或開啟於新視窗）
bookmark_label=目前檢視

# Secondary toolbar and context menu
tools.title=工具
tools_label=工具
first_page.title=跳到第一頁
first_page.label=跳到第一頁
first_page_label=跳到第一頁
last_page.title=跳到最後一頁
last_page.label=跳到最後一頁
last_page_label=跳到最後一頁
page_rotate_cw.title=順時針旋轉
page_rotate_cw.label=順時針旋轉
page_rotate_cw_label=順時針旋轉
page_rotate_ccw.title=逆時針旋轉
page_rotate_ccw.label=逆時針旋轉
page_rotate_ccw_label=逆時針旋轉

cursor_text_select_tool.title=開啟文字選擇工具
cursor_text_select_tool_label=文字選擇工具
cursor_hand_tool.title=開啟頁面移動工具
cursor_hand_tool_label=頁面移動工具

scroll_vertical.title=使用垂直捲動版面
scroll_vertical_label=垂直捲動
scroll_horizontal.title=使用水平捲動版面
scroll_horizontal_label=水平捲動
scroll_wrapped.title=使用多頁捲動版面
scroll_wrapped_label=多頁捲動

spread_none.title=不要進行跨頁顯示
spread_none_label=不跨頁
spread_odd.title=從奇數頁開始跨頁
spread_odd_label=奇數跨頁
spread_even.title=從偶數頁開始跨頁
spread_even_label=偶數跨頁

# Document properties dialog box
document_properties.title=文件內容…
document_properties_label=文件內容…
document_properties_file_name=檔案名稱:
document_properties_file_size=檔案大小:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB（{{size_b}} 位元組）
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB（{{size_b}} 位元組）
document_properties_title=標題:
document_properties_author=作者:
document_properties_subject=主旨:
document_properties_keywords=關鍵字:
document_properties_creation_date=建立日期:
document_properties_modification_date=修改日期:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}} {{time}}
document_properties_creator=建立者:
document_properties_producer=PDF 產生器:
document_properties_version=PDF 版本:
document_properties_page_count=頁數:
document_properties_page_size=頁面大小:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=垂直
document_properties_page_size_orientation_landscape=水平
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}}（{{orientation}}）
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}}（{{name}}，{{orientation}}）
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=快速 Web 檢視:
document_properties_linearized_yes=是
document_properties_linearized_no=否
document_properties_close=關閉

print_progress_message=正在準備列印文件…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=取消

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=切換側邊欄
toggle_sidebar_notification.title=切換側邊攔（文件包含大綱或附件）
toggle_sidebar_label=切換側邊欄
document_outline.title=顯示文件大綱（雙擊展開/摺疊所有項目）
document_outline_label=文件大綱
attachments.title=顯示附件
attachments_label=附件
thumbs.title=顯示縮圖
thumbs_label=縮圖
findbar.title=在文件中尋找
findbar_label=尋找

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=第 {{page}} 頁
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=第 {{page}} 頁
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=頁 {{page}} 的縮圖

# Find panel button title and messages
find_input.title=尋找
find_input.placeholder=在文件中搜尋…
find_previous.title=尋找文字前次出現的位置
find_previous_label=上一個
find_next.title=尋找文字下次出現的位置
find_next_label=下一個
find_highlight=全部強調標示
find_match_case_label=區分大小寫
find_entire_word_label=符合整個字
find_reached_top=已搜尋至文件頂端，自底端繼續搜尋
find_reached_bottom=已搜尋至文件底端，自頂端繼續搜尋
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]=第 {{current}} 筆，共找到 {{total}} 筆
find_match_count[two]=第 {{current}} 筆，共找到 {{total}} 筆
find_match_count[few]=第 {{current}} 筆，共找到 {{total}} 筆
find_match_count[many]=第 {{current}} 筆，共找到 {{total}} 筆
find_match_count[other]=第 {{current}} 筆，共找到 {{total}} 筆
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=找到超過 {{limit}} 筆
find_match_count_limit[one]=找到超過 {{limit}} 筆
find_match_count_limit[two]=找到超過 {{limit}} 筆
find_match_count_limit[few]=找到超過 {{limit}} 筆
find_match_count_limit[many]=找到超過 {{limit}} 筆
find_match_count_limit[other]=找到超過 {{limit}} 筆
find_not_found=找不到指定文字

# Error panel labels
error_more_info=更多資訊
error_less_info=更少資訊
error_close=關閉
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=訊息: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=堆疊: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=檔案: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=行: {{line}}
rendering_error=描繪頁面時發生錯誤。

# Predefined zoom values
page_scale_width=頁面寬度
page_scale_fit=縮放至頁面大小
page_scale_auto=自動縮放
page_scale_actual=實際大小
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=錯誤
loading_error=載入 PDF 時發生錯誤。
invalid_file_error=無效或毀損的 PDF 檔案。
missing_file_error=找不到 PDF 檔案。
unexpected_response_error=伺服器回應未預期的內容。

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}} {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 註解]
password_label=請輸入用來開啟此 PDF 檔案的密碼。
password_invalid=密碼不正確，請再試一次。
password_ok=確定
password_cancel=取消

printing_not_supported=警告: 此瀏覽器未完整支援列印功能。
printing_not_ready=警告: 此 PDF 未完成下載以供列印。
web_fonts_disabled=已停用網路字型 (Web fonts): 無法使用 PDF 內嵌字型。
document_colors_not_allowed=瀏覽器的「優先使用網頁指定的色彩」未被勾選，PDF 文件無法使用自己的色彩。
