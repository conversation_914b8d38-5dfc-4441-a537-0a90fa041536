import request from '@/utils/request'
import qs from 'qs'
// 获取所有项目任务列表（树形）
export function getTudoProjectTaskList(data) {
  return request({
    url: `/api/todo/project/task/list`,
    method: 'GET',
    data
  })
}

// 接受？不接受任务
export function isAgreeTask(data) {
  return request({
    url: `/api/todo/project/task/pbs/handle`,
    method: 'POST',
    data
  })
}

// 撤销任务
export function cancelTask(data) {
  return request({
    url: `/api/todo/project/task/repeal`,
    method: 'PUT',
    data
  })
}

// 更改实际开始时间和结束时间
export function changeTaskTime(data) {
  return request({
    url: `/api/project/task/complete/change`,
    method: 'PUT',
    data
  })
}

//是否允许拆分
export function getSplitCheck(data) {
  return request({
    url: `/api/project/task/split/check`,
    method: 'GET',
    data
  })
}

//查询待办任务列表
export function getTodoList(data) {
  return request({
    url: `/api/todo/project/task/index`,
    method: 'GET',
    data
  })
}