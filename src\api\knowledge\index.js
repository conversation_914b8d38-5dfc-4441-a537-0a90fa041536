import request from '@/utils/request'
import qs from 'qs'

// 查询是否纳入知识库管理
export function isCategoryFlag(innerName) {
  return request({
    url: `/api/system/serviceConfiguration/type/getInfo/${innerName}`,
    method: 'GET',
  })
}

// 知识库目录列表
export function getKnowledgeObjTree() {
  return request({
    url: `/api/repository/catalog`,
    method: 'GET',
  })
}
// 获取已绑定的id
export function getObjLinkUser(data) {
  return request({
    url: `/api/repository/obj/link/user`,
    method: 'GET',
    data
  })
}

// 保存绑定的用户
export function saveObjLinkUser(data) {
  return request({
    url: `/api/repository/obj/link/user`,
    method: 'POST',
    data
  })
}

// 查询详情页的请求参数
export function getObjDetailParams(data) {
  return request({
    url: `/api/repository/obj/detail/params`,
    method: 'GET',
    data
  })
}