/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.21.2(67b5a8116f3c0bace36b180e524e05bb750a16d8)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.fr",{"vs/base/browser/ui/actionbar/actionViewItems":["{0} ({1})"],"vs/base/browser/ui/findinput/findInput":["entrée"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Re<PERSON><PERSON> la casse","Mo<PERSON> entier","Utiliser une expression régulière"],"vs/base/browser/ui/findinput/replaceInput":["entrée","Préserver la casse"],"vs/base/browser/ui/inputbox/inputBox":["Erreur : {0}","Avertissement : {0}","Info : {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Indépendant"],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Effacer","Désactiver le filtre sur le type","Activer le filtre sur le type","Aucun élément","{0} éléments sur {1} correspondants"],
"vs/base/common/errorMessage":["{0}: {1}","Une erreur système s'est produite ({0})","Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.","Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.","{0} ({1} erreurs au total)","Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails."],"vs/base/common/keybindingLabels":["Ctrl","Maj","Alt","Windows","Ctrl","Maj","Alt","Super","Contrôle","Maj","Alt","Commande","Contrôle","Maj","Alt","Windows","Contrôle","Maj","Alt","Super"],"vs/base/parts/quickinput/browser/quickInput":["Précédent","{0}/{1}","Taper pour affiner les résultats.","{0} résultats","{0} Sélectionnés","OK","Personnalisé","Précédent ({0})","Précédent"],"vs/base/parts/quickinput/browser/quickInputList":["Entrée rapide"],
"vs/editor/browser/controller/coreCommands":["Aligner par rapport à la fin même en cas de passage à des lignes plus longues","Aligner par rapport à la fin même en cas de passage à des lignes plus longues"],"vs/editor/browser/controller/textAreaHandler":["éditeur","L'éditeur n'est pas accessible pour le moment. Appuyez sur {0} pour voir les options."],"vs/editor/browser/editorExtensions":["Ann&&uler","Annuler","&&Rétablir","Rétablir","&&Sélectionner tout","Tout sélectionner"],"vs/editor/browser/widget/codeEditorWidget":["Le nombre de curseurs a été limité à {0}."],"vs/editor/browser/widget/diffEditorWidget":["Impossible de comparer les fichiers car l'un d'eux est trop volumineux."],
"vs/editor/browser/widget/diffReview":["Fermer","aucune ligne changée","1 ligne changée","{0} lignes changées","Différence {0} sur {1} : ligne d'origine {2}, {3}, ligne modifiée {4}, {5}","vide","{0} ligne inchangée {1}","{0} ligne d'origine {1} ligne modifiée {2}","+ {0} ligne modifiée {1}","- {0} ligne d'origine {1}","Accéder à la différence suivante","Accéder la différence précédente"],"vs/editor/browser/widget/inlineDiffMargin":["Copier les lignes supprimées","Copier la ligne supprimée","Copier la ligne supprimée ({0})","Annuler la modification","Copier la ligne supprimée ({0})"],
"vs/editor/common/config/commonEditorConfig":["Éditeur","Le nombre d'espaces auxquels une tabulation est égale. Ce paramètre est substitué basé sur le contenu du fichier lorsque `#editor.detectIndentation#` est à 'on'.","Espaces insérés quand vous appuyez sur la touche Tab. Ce paramètre est remplacé en fonction du contenu du fichier quand '#editor.detectIndentation#' est activé.","Contrôle si '#editor.tabSize#' et '#editor.insertSpaces#' sont automatiquement détectés lors de l’ouverture d’un fichier en fonction de son contenu.","Supprimer l'espace blanc de fin inséré automatiquement.","Traitement spécial des fichiers volumineux pour désactiver certaines fonctionnalités utilisant beaucoup de mémoire.","Contrôle si la saisie semi-automatique doit être calculée en fonction des mots présents dans le document.","Coloration sémantique activée pour tous les thèmes de couleur.","Coloration sémantique désactivée pour tous les thèmes de couleur.","La coloration sémantique est configurée par le paramètre 'semanticHighlighting' du thème de couleur actuel.","Contrôle si semanticHighlighting est affiché pour les langages qui le prennent en charge.","Garder les éditeurs d'aperçu ouverts même si l'utilisateur double-clique sur son contenu ou appuie sur la touche Échap. ","Les lignes plus longues que cette valeur ne sont pas tokenisées pour des raisons de performances","Délai d'expiration en millisecondes avant annulation du calcul de diff. Utilisez 0 pour supprimer le délai d'expiration.","Contrôle si l'éditeur de différences affiche les différences en mode côte à côte ou inline.","Quand il est activé, l'éditeur de différences ignore les changements d'espace blanc de début ou de fin.","Contrôle si l'éditeur de différences affiche les indicateurs +/- pour les changements ajoutés/supprimés .","Contrôle si l'éditeur affiche CodeLens."],
"vs/editor/common/config/editorOptions":["L'éditeur utilise les API de la plateforme pour détecter si un lecteur d'écran est attaché.","L'éditeur est optimisé en permanence pour une utilisation avec un lecteur d'écran.","L'éditeur n'est jamais optimisé pour une utilisation avec un lecteur d'écran.","Contrôle si l'éditeur doit s'exécuter dans un mode optimisé pour les lecteurs d'écran.","Contrôle si un espace est inséré pour les commentaires.","Contrôle si les lignes vides doivent être ignorées avec des actions d'activation/de désactivation, d'ajout ou de suppression des commentaires de ligne.","Contrôle si la copie sans sélection permet de copier la ligne actuelle.","Contrôle si le curseur doit sauter pour rechercher les correspondances lors de la saisie.","Détermine si la chaîne de recherche dans le Widget Recherche est initialisée avec la sélection de l’éditeur.","Ne jamais activer Rechercher automatiquement dans la sélection (par défaut)","Toujours activer Rechercher automatiquement dans la sélection","Activez Rechercher automatiquement dans la sélection quand plusieurs lignes de contenu sont sélectionnées.","Contrôle la condition d'activation automatique de la recherche dans la sélection.","Détermine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partagé sur macOS.","Contrôle si le widget Recherche doit ajouter des lignes supplémentaires en haut de l'éditeur. Quand la valeur est true, vous pouvez faire défiler au-delà de la première ligne si le widget Recherche est visible.","Contrôle si la recherche redémarre automatiquement depuis le début (ou la fin) quand il n'existe aucune autre correspondance.","Active/désactive les ligatures de police.","Paramètres de fonctionnalité de police explicites.","Configure les ligatures de police ou les fonctionnalités de police.","Contrôle la taille de police en pixels.",'Seuls les mots clés "normal" et "bold", ou les nombres compris entre 1 et 1 000 sont autorisés.','Contrôle l\'épaisseur de police. Accepte les mots clés "normal" et "bold", ou les nombres compris entre 1 et 1 000.',"Montrer l'aperçu des résultats (par défaut)","Accéder au résultat principal et montrer un aperçu","Accéder au résultat principal et activer l'accès sans aperçu pour les autres","Ce paramètre est déprécié, utilisez des paramètres distincts comme 'editor.editor.gotoLocation.multipleDefinitions' ou 'editor.editor.gotoLocation.multipleImplementations' à la place.","Contrôle le comportement de la commande 'Atteindre la définition' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre la définition de type' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre la déclaration' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre les implémentations' quand plusieurs emplacements cibles existent.","Contrôle le comportement de la commande 'Atteindre les références' quand plusieurs emplacements cibles existent.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la définition' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la définition de type' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la déclaration' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre l'implémentation' est l'emplacement actuel.","ID de commande alternatif exécuté quand le résultat de 'Atteindre la référence' est l'emplacement actuel.","Contrôle si le pointage est affiché.","Contrôle le délai en millisecondes, après lequel le survol est affiché.","Contrôle si le pointage doit rester visible quand la souris est déplacée au-dessus.","Active l’ampoule d’action de code dans l’éditeur.","Contrôle la hauteur de ligne. Utilisez 0 pour calculer la hauteur de ligne de la taille de la police.","Contrôle si la minimap est affichée.","Le minimap a la même taille que le contenu de l'éditeur (défilement possible).","Le minimap s'agrandit ou se réduit selon les besoins pour remplir la hauteur de l'éditeur (pas de défilement).","Le minimap est réduit si nécessaire pour ne jamais dépasser la taille de l'éditeur (pas de défilement).","Contrôle la taille du minimap.","Contrôle le côté où afficher la minimap.","Contrôle quand afficher le curseur du minimap.","Échelle du contenu dessiné dans le minimap : 1, 2 ou 3.","Afficher les caractères réels sur une ligne par opposition aux blocs de couleur.","Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.","Contrôle la quantité d’espace entre le bord supérieur de l’éditeur et la première ligne.","Contrôle la quantité d'espace entre le bord inférieur de l'éditeur et la dernière ligne.","Active une fenêtre contextuelle qui affiche de la documentation sur les paramètres et des informations sur les types à mesure que vous tapez.","Détermine si le menu de suggestions de paramètres se ferme ou reviens au début lorsque la fin de la liste est atteinte.","Activez les suggestions rapides dans les chaînes.","Activez les suggestions rapides dans les commentaires.","Activez les suggestions rapides en dehors des chaînes et des commentaires.","Contrôle si les suggestions doivent apparaître automatiquement pendant la saisie.","Les numéros de ligne ne sont pas affichés.","Les numéros de ligne sont affichés en nombre absolu.","Les numéros de ligne sont affichés sous la forme de distance en lignes à la position du curseur.","Les numéros de ligne sont affichés toutes les 10 lignes.","Contrôle l'affichage des numéros de ligne.","Nombre de caractères monospace auxquels cette règle d'éditeur effectue le rendu.","Couleur de cette règle d'éditeur.","Rendre les règles verticales après un certain nombre de caractères à espacement fixe. Utiliser plusieurs valeurs pour plusieurs règles. Aucune règle n'est dessinée si le tableau est vide.","Insérez une suggestion sans remplacer le texte à droite du curseur.","Insérez une suggestion et remplacez le texte à droite du curseur.","Contrôle si les mots sont remplacés en cas d'acceptation de la saisie semi-automatique. Notez que cela dépend des extensions adhérant à cette fonctionnalité.","Détermine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.","Contrôle si le tri favorise trier les mots qui apparaissent près du curseur.","Contrôle si les sélections de suggestion mémorisées sont partagées entre plusieurs espaces de travail et fenêtres (nécessite '#editor.suggestSelection#').","Contrôle si un extrait de code actif empêche les suggestions rapides.","Contrôle s'il faut montrer ou masquer les icônes dans les suggestions.","Contrôle le nombre de suggestions IntelliSense affichées avant de montrer une barre de défilement (15 maximum).","Ce paramètre est déprécié, veuillez utiliser des paramètres distincts comme 'editor.suggest.showKeywords' ou 'editor.suggest.showSnippets' à la place.","Si activé, IntelliSense montre des suggestions de type 'method'.","Si activé, IntelliSense montre des suggestions de type 'function'.","Si activé, IntelliSense montre des suggestions de type 'constructor'.","Si activé, IntelliSense montre des suggestions de type 'field'.","Si activé, IntelliSense montre des suggestions de type 'variable'.","Si activé, IntelliSense montre des suggestions de type 'class'.","Si activé, IntelliSense montre des suggestions de type 'struct'.","Si activé, IntelliSense montre des suggestions de type 'interface'.","Si activé, IntelliSense montre des suggestions de type 'module'.","Si activé, IntelliSense montre des suggestions de type 'property'.","Si activé, IntelliSense montre des suggestions de type 'event'.","Si activé, IntelliSense montre des suggestions de type 'operator'.","Si activé, IntelliSense montre des suggestions de type 'unit'.","Si activé, IntelliSense montre des suggestions de type 'value'.","Si activé, IntelliSense montre des suggestions de type 'constant'.","Si activé, IntelliSense montre des suggestions de type 'enum'.","Si activé, IntelliSense montre des suggestions de type 'enumMember'.","Si activé, IntelliSense montre des suggestions de type 'keyword'.","Si activé, IntelliSense montre des suggestions de type 'text'.","Si activé, IntelliSense montre des suggestions de type 'color'.","Si activé, IntelliSense montre des suggestions de type 'file'.","Si activé, IntelliSense montre des suggestions de type 'reference'.","Si activé, IntelliSense montre des suggestions de type 'customcolor'.","Si activé, IntelliSense montre des suggestions de type 'folder'.","Si activé, IntelliSense montre des suggestions de type 'typeParameter'.","Si activé, IntelliSense montre des suggestions de type 'snippet'.","Si activé, IntelliSense montre des suggestions de type 'utilisateur'.","Si activé, IntelliSense montre des suggestions de type 'problèmes'.","Contrôle la visibilité de la barre d'état en bas du widget de suggestion.","Contrôle si les suggestions doivent être acceptées sur les caractères de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut être un caractère de validation qui accepte une suggestion et tape ce caractère.","Accepter uniquement une suggestion avec 'Entrée' quand elle effectue une modification textuelle.","Contrôle si les suggestions sont acceptées après appui sur 'Entrée', en plus de 'Tab'. Permet d’éviter toute ambiguïté entre l’insertion de nouvelles lignes et l'acceptation de suggestions.","Contrôle le nombre de lignes dans l'éditeur qui peuvent être lues par un lecteur d'écran. Avertissement : Ce paramètre a une incidence sur les performances quand le nombre est supérieur à la valeur par défaut.","Contenu de l'éditeur","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les parenthèses.","Fermer automatiquement les parenthèses uniquement lorsque le curseur est à gauche de l’espace.","Contrôle si l’éditeur doit fermer automatiquement les parenthèses quand l’utilisateur ajoute une parenthèse ouvrante.","Tapez avant les guillemets ou les crochets fermants uniquement s'ils sont automatiquement insérés.","Contrôle si l'éditeur doit taper avant les guillemets ou crochets fermants.","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les guillemets.","Fermer automatiquement les guillemets uniquement lorsque le curseur est à gauche de l’espace.","Contrôle si l’éditeur doit fermer automatiquement les guillemets après que l’utilisateur ajoute un guillemet ouvrant.","L'éditeur n'insère pas de retrait automatiquement.","L'éditeur conserve le retrait de la ligne actuelle.","L'éditeur conserve le retrait de la ligne actuelle et honore les crochets définis par le langage.","L'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage et appelle des objets onEnterRules spéciaux définis par les langages.","L'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage, appelle des objets onEnterRules spéciaux définis par les langages et honore les objets indentationRules définis par les langages.","Contrôle si l'éditeur doit ajuster automatiquement le retrait quand les utilisateurs tapent, collent, déplacent ou mettent en retrait des lignes.","Utilisez les configurations de langue pour déterminer quand entourer automatiquement les sélections.","Entourez avec des guillemets et non des crochets.","Entourez avec des crochets et non des guillemets.","Détermine si l'éditeur doit automatiquement entourer les sélections.","Contrôle si l'éditeur affiche CodeLens.","Contrôle si l'éditeur doit afficher les éléments décoratifs de couleurs inline et le sélecteur de couleurs.","Autoriser l'utilisation de la souris et des touches pour sélectionner des colonnes.","Contrôle si la coloration syntaxique doit être copiée dans le presse-papiers.","Contrôler le style d’animation du curseur.","Contrôle si l'animation du point d'insertion doit être activée.","Contrôle le style du curseur.","Contrôle le nombre minimum de lignes visibles avant et après le curseur. Appelé 'scrollOff' ou 'scrollOffset' dans d'autres éditeurs.","'cursorSurroundingLines' est appliqué seulement s'il est déclenché via le clavier ou une API.","'cursorSurroundingLines' est toujours appliqué.","Contrôle quand 'cursorSurroundingLines' doit être appliqué.","Détermine la largeur du curseur lorsque `#editor.cursorStyle#` est à `line`.","Contrôle si l’éditeur autorise le déplacement de sélections par glisser-déplacer.","Multiplicateur de vitesse de défilement quand vous appuyez sur 'Alt'.","Contrôle si l'éditeur a le pliage de code activé.","Utilisez une stratégie de pliage propre à la langue, si disponible, sinon utilisez la stratégie basée sur le retrait.","Utilisez la stratégie de pliage basée sur le retrait.","Contrôle la stratégie de calcul des plages de pliage.","Contrôle si l'éditeur doit mettre en évidence les plages pliées.","Contrôle si le fait de cliquer sur le contenu vide après une ligne pliée déplie la ligne.","Contrôle la famille de polices.","Détermine si l’éditeur doit automatiquement mettre en forme le contenu collé. Un formateur doit être disponible et être capable de mettre en forme une plage dans un document.","Contrôle si l’éditeur doit mettre automatiquement en forme la ligne après la saisie.","Contrôle si l'éditeur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au débogage.","Contrôle si le curseur doit être masqué dans la règle de la vue d’ensemble.","Contrôle si l’éditeur doit mettre en surbrillance le guide de mise en retrait actif.","Contrôle l'espacement des lettres en pixels.","Contrôle si l’éditeur doit détecter les liens et les rendre cliquables.","Mettez en surbrillance les crochets correspondants.","Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.","Faire un zoom sur la police de l'éditeur quand l'utilisateur fait tourner la roulette de la souris tout en maintenant la touche 'Ctrl' enfoncée.","Fusionnez plusieurs curseurs quand ils se chevauchent.","Mappe vers 'Contrôle' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur à utiliser pour ajouter plusieurs curseurs avec la souris. Les gestes de souris Atteindre la définition et Ouvrir le lien s'adapteront tels qu’ils n’entrent pas en conflit avec le modificateur multicursor. [Lire la suite] (https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Chaque curseur colle une seule ligne de texte.","Chaque curseur colle le texte en entier.","Contrôle le collage quand le nombre de lignes du texte collé correspond au nombre de curseurs.","Contrôle si l'éditeur doit mettre en surbrillance les occurrences de symboles sémantiques.","Contrôle si une bordure doit être dessinée autour de la règle de la vue d'ensemble.","Focus sur l'arborescence à l'ouverture de l'aperçu","Placer le focus sur l'éditeur à l'ouverture de l'aperçu","Contrôle s'il faut mettre le focus sur l'éditeur inline ou sur l'arborescence dans le widget d'aperçu.","Contrôle si le geste de souris Accéder à la définition ouvre toujours le widget d'aperçu.","Contrôle le délai en millisecondes après lequel des suggestions rapides sont affichées.","Contrôle si l'éditeur renomme automatiquement selon le type.","Contrôle si l’éditeur doit afficher les caractères de contrôle.","Contrôle si l’éditeur doit afficher les guides de mise en retrait.","Affichez le dernier numéro de ligne quand le fichier se termine par un saut de ligne.","Met en surbrillance la gouttière et la ligne actuelle.","Contrôle la façon dont l’éditeur doit afficher la mise en surbrillance de la ligne actuelle.","Contrôle si l'éditeur doit afficher la mise en surbrillance de la ligne actuelle seulement quand l'éditeur a le focus","Affiche les espaces blancs à l'exception des espaces uniques entre les mots.","Afficher les espaces blancs uniquement sur le texte sélectionné.","Afficher uniquement les caractères correspondant aux espaces blancs de fin","Contrôle la façon dont l’éditeur doit restituer les caractères espaces.","Contrôle si les sélections doivent avoir des angles arrondis.","Contrôle le nombre de caractères supplémentaires, au-delà duquel l’éditeur défile horizontalement.","Contrôle si l’éditeur défile au-delà de la dernière ligne.","Faites défiler uniquement le long de l'axe prédominant quand le défilement est à la fois vertical et horizontal. Empêche la dérive horizontale en cas de défilement vertical sur un pavé tactile.","Contrôle si le presse-papiers principal Linux doit être pris en charge.","Contrôle si l'éditeur doit mettre en surbrillance les correspondances similaires à la sélection.","Affichez toujours les contrôles de pliage.","Affichez uniquement les contrôles de pliage quand la souris est au-dessus de la reliure.","Contrôle quand afficher les contrôles de pliage sur la reliure.","Contrôle la disparition du code inutile.","Contrôle les variables dépréciées barrées.","Afficher des suggestions d’extraits au-dessus d’autres suggestions.","Afficher des suggestions d’extraits en-dessous d’autres suggestions.","Afficher des suggestions d’extraits avec d’autres suggestions.","Ne pas afficher de suggestions d’extrait de code.","Contrôle si les extraits de code s'affichent en même temps que d'autres suggestions, ainsi que leur mode de tri.","Contrôle si l'éditeur défile en utilisant une animation.","Taille de la police pour le widget de suggestion. Lorsque la valeur est à `0`, la valeur de `#editor.fontSize` est utilisée.","Hauteur de ligne pour le widget de suggestion. Lorsque la valeur est à `0`, la valeur de `#editor.lineHeight#` est utilisée.","Contrôle si les suggestions devraient automatiquement s’afficher lorsque vous tapez les caractères de déclencheur.","Sélectionnez toujours la première suggestion.","Sélectionnez les suggestions récentes sauf si une entrée ultérieure en a sélectionné une, par ex., 'console.| -> console.log', car 'log' a été effectué récemment.","Sélectionnez des suggestions en fonction des préfixes précédents qui ont complété ces suggestions, par ex., 'co -> console' et 'con -> const'.","Contrôle comment les suggestions sont pré-sélectionnés lors de l’affichage de la liste de suggestion.","La complétion par tabulation insérera la meilleure suggestion lorsque vous appuyez sur tab.","Désactiver les complétions par tabulation.","Compléter les extraits de code par tabulation lorsque leur préfixe correspond. Fonctionne mieux quand les 'quickSuggestions' ne sont pas activées.","Active les complétions par tabulation","Les marques de fin de ligne inhabituelles sont ignorées.","Les marques de fin de ligne inhabituelles demandent à être supprimées.","Les marques de fin de ligne inhabituelles sont automatiquement supprimées.","Supprimez les marques de fin de ligne inhabituelles susceptibles de causer des problèmes.","L'insertion et la suppression des espaces blancs suit les taquets de tabulation.","Caractères utilisés comme séparateurs de mots durant la navigation ou les opérations basées sur les mots","Le retour automatique à la ligne n'est jamais effectué.","Le retour automatique à la ligne s'effectue en fonction de la largeur de la fenêtre d'affichage.","Les lignes seront terminées à `#editor.wordWrapColumn#`.","Les lignes seront terminées au minimum du viewport et `#editor.wordWrapColumn#`.","Contrôle comment les lignes doivent être limitées.","Contrôle la colonne de terminaison de l’éditeur lorsque `#editor.wordWrap#` est à `wordWrapColumn` ou `bounded`.","Aucune mise en retrait. Les lignes enveloppées commencent à la colonne 1.","Les lignes enveloppées obtiennent la même mise en retrait que le parent.","Les lignes justifiées obtiennent une mise en retrait +1 vers le parent.","Les lignes justifiées obtiennent une mise en retrait +2 vers le parent. ","Contrôle la mise en retrait des lignes justifiées.","Suppose que tous les caractères ont la même largeur. Il s'agit d'un algorithme rapide qui fonctionne correctement pour les polices à espacement fixe et certains scripts (comme les caractères latins) où les glyphes ont la même largeur.","Délègue le calcul des points de wrapping au navigateur. Il s'agit d'un algorithme lent qui peut provoquer le gel des grands fichiers, mais qui fonctionne correctement dans tous les cas.","Contrôle l'algorithme qui calcule les points de wrapping."],
"vs/editor/common/model/editStack":["Frappe en cours"],"vs/editor/common/modes/modesRegistry":["Texte brut"],
"vs/editor/common/standaloneStrings":["Aucune sélection","Ligne {0}, colonne {1} ({2} sélectionné)","Ligne {0}, colonne {1}","{0} sélections ({1} caractères sélectionnés)","{0} sélections","Remplacement du paramètre 'accessibilitySupport' par 'on'.","Ouverture de la page de documentation sur l'accessibilité de l'éditeur.","dans un volet en lecture seule d'un éditeur de différences.","dans un volet d'un éditeur de différences."," dans un éditeur de code en lecture seule"," dans un éditeur de code","Pour configurer l'éditeur de manière à être optimisé en cas d'utilisation d'un lecteur d'écran, appuyez sur Commande+E maintenant.","Pour configurer l'éditeur de manière à être optimisé en cas d'utilisation d'un lecteur d'écran, appuyez sur Contrôle+E maintenant.","L'éditeur est configuré pour être optimisé en cas d'utilisation avec un lecteur d'écran.","L'éditeur est configuré pour ne jamais être optimisé en cas d'utilisation avec un lecteur d'écran, ce qui n'est pas le cas pour le moment.","Appuyez sur Tab dans l'éditeur pour déplacer le focus vers le prochain élément pouvant être désigné comme élément actif. Activez ou désactivez ce comportement en appuyant sur {0}.","Appuyez sur Tab dans l'éditeur pour déplacer le focus vers le prochain élément pouvant être désigné comme élément actif. La commande {0} ne peut pas être déclenchée par une combinaison de touches.","Appuyez sur Tab dans l'éditeur pour insérer le caractère de tabulation. Activez ou désactivez ce comportement en appuyant sur {0}.","Appuyez sur Tab dans l'éditeur pour insérer le caractère de tabulation. La commande {0} ne peut pas être déclenchée par une combinaison de touches.","Appuyez sur Commande+H maintenant pour ouvrir une fenêtre de navigateur avec plus d'informations sur l'accessibilité de l'éditeur.","Appuyez sur Contrôle+H maintenant pour ouvrir une fenêtre de navigateur avec plus d'informations sur l'accessibilité de l'éditeur.","Vous pouvez masquer cette info-bulle et revenir à l'éditeur en appuyant sur Échap ou Maj+Échap.","Afficher l'aide sur l'accessibilité","Développeur : Inspecter les jetons","Accéder à la ligne/colonne...","Afficher tous les fournisseurs d'accès rapide","Palette de commandes","Commandes d'affichage et d'exécution","Accéder au symbole...","Accéder au symbole par catégorie...","Contenu de l'éditeur","Appuyez sur Alt+F1 pour voir les options d'accessibilité.","Activer/désactiver le thème à contraste élevé","{0} modifications dans {1} fichiers"],
"vs/editor/common/view/editorColorRegistry":["Couleur d'arrière-plan de la mise en surbrillance de la ligne à la position du curseur.","Couleur d'arrière-plan de la bordure autour de la ligne à la position du curseur.","Couleur d'arrière-plan des plages mises en surbrillance, comme par les fonctionnalités de recherche et Quick Open. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan de la bordure autour des plages mises en surbrillance.","Couleur d'arrière-plan du symbole mis en surbrillance, comme le symbole Atteindre la définition ou Suivant/Précédent. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.","Couleur d'arrière-plan de la bordure autour des symboles mis en surbrillance.","Couleur du curseur de l'éditeur.","La couleur de fond du curseur de l'éditeur. Permet de personnaliser la couleur d'un caractère survolé par un curseur de bloc.","Couleur des espaces blancs dans l'éditeur.","Couleur des repères de retrait de l'éditeur.","Couleur des guides d'indentation de l'éditeur actif","Couleur des numéros de ligne de l'éditeur.","Couleur des numéros de lignes actives de l'éditeur","L’ID est déprécié. Utilisez à la place 'editorLineNumber.activeForeground'.","Couleur des numéros de lignes actives de l'éditeur","Couleur des règles de l'éditeur","Couleur pour les indicateurs CodeLens","Couleur d'arrière-plan pour les accolades associées","Couleur pour le contour des accolades associées","Couleur de la bordure de la règle d'aperçu.","Couleur d'arrière-plan de la règle d'aperçu de l'éditeur. Utilisée uniquement quand la minimap est activée et placée sur le côté droit de l'éditeur.","Couleur de fond pour la bordure de l'éditeur. La bordure contient les marges pour les symboles et les numéros de ligne.","Couleur de bordure du code source inutile (non utilisé) dans l'éditeur.","Opacité du code source inutile (non utilisé) dans l'éditeur. Par exemple, '#000000c0' affiche le code avec une opacité de 75 %. Pour les thèmes à fort contraste, utilisez la couleur de thème 'editorUnnecessaryCode.border' pour souligner le code inutile au lieu d'utiliser la transparence.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des plages. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur du marqueur de la règle d'aperçu pour les erreurs.","Couleur du marqueur de la règle d'aperçu pour les avertissements.","Couleur du marqueur de la règle d'aperçu pour les informations."],
"vs/editor/contrib/anchorSelect/anchorSelect":["Ancre de sélection","Ancre définie sur {0}:{1}","Définir l'ancre de sélection","Atteindre l'ancre de sélection","Sélectionner de l'ancre au curseur","Annuler l'ancre de sélection"],"vs/editor/contrib/bracketMatching/bracketMatching":["Couleur du marqueur de la règle d'aperçu pour rechercher des parenthèses.","Atteindre le crochet","Sélectionner jusqu'au crochet","Accéder au &&crochet"],"vs/editor/contrib/caretOperations/caretOperations":["Déplacer le texte sélectionné à gauche","Déplacer le texte sélectionné à droite"],"vs/editor/contrib/caretOperations/transpose":["Transposer les lettres"],"vs/editor/contrib/clipboard/clipboard":["Co&&uper","Couper","Couper","&&Copier","Copier","Copier","Co&&ller","Coller","Coller","Copier avec la coloration syntaxique"],
"vs/editor/contrib/codeAction/codeActionCommands":["Type d'action de code à exécuter.","Contrôle quand les actions retournées sont appliquées.","Appliquez toujours la première action de code retournée.","Appliquez la première action de code retournée si elle est la seule.","N'appliquez pas les actions de code retournées.","Contrôle si seules les actions de code par défaut doivent être retournées.","Une erreur inconnue s'est produite à l'application de l'action du code","Correction rapide...","Aucune action de code disponible","Aucune action de code préférée n'est disponible pour '{0}'","Aucune action de code disponible pour '{0}'","Aucune action de code par défaut disponible","Aucune action de code disponible","Remanier...","Aucune refactorisation par défaut disponible pour '{0}'","Aucune refactorisation disponible pour '{0}'","Aucune refactorisation par défaut disponible","Aucune refactorisation disponible","Action de la source","Aucune action source par défaut disponible pour '{0}'","Aucune action source disponible pour '{0}'","Aucune action source par défaut disponible","Aucune action n'est disponible","Organiser les importations","Aucune action organiser les imports disponible","Tout corriger","Aucune action Tout corriger disponible","Corriger automatiquement...","Aucun correctif automatique disponible"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Affichez les corrections. Correction préférée disponible ({0})","Afficher les correctifs ({0})","Afficher les correctifs"],"vs/editor/contrib/codelens/codelensController":["Afficher les commandes Code Lens de la ligne actuelle"],"vs/editor/contrib/comment/comment":["Activer/désactiver le commentaire de ligne","Afficher/masquer le commen&&taire de ligne","Ajouter le commentaire de ligne","Supprimer le commentaire de ligne","Activer/désactiver le commentaire de bloc","Afficher/masquer le commentaire de &&bloc"],"vs/editor/contrib/contextmenu/contextmenu":["Afficher le menu contextuel de l'éditeur"],"vs/editor/contrib/cursorUndo/cursorUndo":["Annulation du curseur","Restauration du curseur"],
"vs/editor/contrib/documentSymbols/outlineTree":["Couleur de premier plan des symboles de tableau. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles booléens. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de classe. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de couleur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan pour les symboles de constante. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de constructeur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de membre d'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'événement. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de champ. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fichier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de dossier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fonction. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'interface. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de mot clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de méthode. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de module. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'espace de noms. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles null. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de nombre. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'objet. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'opérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de package. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de propriété. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de référence. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'extrait de code. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de chaîne. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de struct. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de texte. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de paramètre de type. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'unité. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de variable. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion."],
"vs/editor/contrib/find/findController":["Rechercher","&&Rechercher","Rechercher dans la sélection","Rechercher suivant","Rechercher suivant","Rechercher précédent","Rechercher précédent","Sélection suivante","Sélection précédente","Remplacer","&&Remplacer"],"vs/editor/contrib/find/findWidget":["Rechercher","Rechercher","Correspondance précédente","Prochaine correspondance","Rechercher dans la sélection","Fermer","Remplacer","Remplacer","Remplacer","Tout remplacer","Changer le mode de remplacement","Seuls les {0} premiers résultats sont mis en évidence, mais toutes les opérations de recherche fonctionnent sur l’ensemble du texte.","{0} sur {1}","Aucun résultat","{0} trouvé(s)","{0} trouvé pour '{1}'","{0} trouvé pour '{1}', sur {2}","{0} trouvé pour '{1}'","La combinaison Ctrl+Entrée permet désormais d'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour redéfinir le comportement."],
"vs/editor/contrib/folding/folding":["Déplier","Déplier de manière récursive","Plier","Activer/désactiver le pliage","Plier de manière récursive","Replier tous les commentaires de bloc","Replier toutes les régions","Déplier toutes les régions","Plier tout","Déplier tout","Niveau de pliage {0}","Couleur d'arrière-plan des gammes pliées. La couleur ne doit pas être opaque pour ne pas cacher les décorations sous-jacentes.","Couleur du contrôle de pliage dans la marge de l'éditeur."],"vs/editor/contrib/fontZoom/fontZoom":["Agrandissement de l'éditeur de polices de caractères","Rétrécissement de l'éditeur de polices de caractères","Remise à niveau du zoom de l'éditeur de polices de caractères"],"vs/editor/contrib/format/format":["1 modification de format effectuée à la ligne {0}","{0} modifications de format effectuées à la ligne {1}","1 modification de format effectuée entre les lignes {0} et {1}","{0} modifications de format effectuées entre les lignes {1} et {2}"],
"vs/editor/contrib/format/formatActions":["Mettre le document en forme","Mettre la sélection en forme"],"vs/editor/contrib/gotoError/gotoError":["Aller au problème suivant (Erreur, Avertissement, Info)","Aller au problème précédent (Erreur, Avertissement, Info)","Aller au problème suivant dans Fichiers (Erreur, Avertissement, Info)","&&Problème suivant","Aller au problème précédent dans Fichiers (Erreur, Avertissement, Info)","&&Problème précédent"],"vs/editor/contrib/gotoError/gotoErrorWidget":["Erreur","Avertissement","Info","Conseil","{0} à {1}. ","{0} problèmes sur {1}","{0} problème(s) sur {1}","Couleur d'erreur du widget de navigation dans les marqueurs de l'éditeur.","Couleur d'avertissement du widget de navigation dans les marqueurs de l'éditeur.","Couleur d’information du widget de navigation du marqueur de l'éditeur.","Arrière-plan du widget de navigation dans les marqueurs de l'éditeur."],
"vs/editor/contrib/gotoSymbol/goToCommands":["Aperçu","Définitions","Définition introuvable pour '{0}'","Définition introuvable","Atteindre la définition","Atteindre la &&définition","Ouvrir la définition sur le côté","Faire un Peek de la Définition","Déclarations","Aucune déclaration pour '{0}'","Aucune déclaration","Accéder à la déclaration","Atteindre la &&déclaration","Aucune déclaration pour '{0}'","Aucune déclaration","Aperçu de la déclaration","Définitions de type","Définition de type introuvable pour '{0}'","Définition de type introuvable","Atteindre la définition de type","Accéder à la définition de &&type","Aperçu de la définition du type","Implémentations","Implémentation introuvable pour '{0}'","Implémentation introuvable","Atteindre les implémentations","Atteindre les &&implémentations","Implémentations d'aperçu","Aucune référence pour '{0}'","Aucune référence","Atteindre les références","Atteindre les &&références","Références","Aperçu des références","Références","Atteindre un symbole","Emplacements","Aucun résultat pour « {0} »","Références"],
"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition":["Cliquez pour afficher {0} définitions."],"vs/editor/contrib/gotoSymbol/peek/referencesController":["Chargement en cours...","{0} ({1})"],"vs/editor/contrib/gotoSymbol/peek/referencesTree":["{0} références","{0} référence","Références"],"vs/editor/contrib/gotoSymbol/peek/referencesWidget":["aperçu non disponible","Aucun résultat","Références"],"vs/editor/contrib/gotoSymbol/referencesModel":["symbole dans {0} sur la ligne {1}, colonne {2}","1 symbole dans {0}, chemin complet {1}","{0} symboles dans {1}, chemin complet {2}","Résultats introuvables","1 symbole dans {0}","{0} symboles dans {1}","{0} symboles dans {1} fichiers"],"vs/editor/contrib/gotoSymbol/symbolNavigation":["Symbole {0} sur {1}, {2} pour le suivant","Symbole {0} sur {1}"],"vs/editor/contrib/hover/hover":["Afficher par pointage","Afficher le pointeur de l'aperçu de définition"],
"vs/editor/contrib/hover/modesContentHover":["Chargement en cours...","Aperçu du problème","Recherche de correctifs rapides...","Aucune solution disponible dans l'immédiat","Correction rapide..."],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Remplacer par la valeur précédente","Remplacer par la valeur suivante"],"vs/editor/contrib/indentation/indentation":["Convertir les retraits en espaces","Convertir les retraits en tabulations","Taille des tabulations configurée","Sélectionner la taille des tabulations pour le fichier actuel","Mettre en retrait avec des tabulations","Mettre en retrait avec des espaces","Détecter la mise en retrait à partir du contenu","Remettre en retrait les lignes","Réindenter les lignes sélectionnées"],
"vs/editor/contrib/linesOperations/linesOperations":["Copier la ligne en haut","&&Copier la ligne en haut","Copier la ligne en bas","Co&&pier la ligne en bas","Dupliquer la sélection","&&Dupliquer la sélection","Déplacer la ligne vers le haut","Déplacer la ligne &&vers le haut","Déplacer la ligne vers le bas","Déplacer la &&ligne vers le bas","Trier les lignes dans l'ordre croissant","Trier les lignes dans l'ordre décroissant","Découper l'espace blanc de fin","Supprimer la ligne","Mettre en retrait la ligne","Ajouter un retrait négatif à la ligne","Insérer une ligne au-dessus","Insérer une ligne sous","Supprimer tout ce qui est à gauche","Supprimer tout ce qui est à droite","Joindre les lignes","Transposer les caractères autour du curseur","Transformer en majuscule","Transformer en minuscule",'Appliquer la casse "1re lettre des mots en majuscule"'],
"vs/editor/contrib/links/links":["Exécuter la commande","suivre le lien","cmd + clic","ctrl + clic","option + clic","alt + clic","Échec de l'ouverture de ce lien, car il n'est pas bien formé : {0}","Échec de l'ouverture de ce lien, car sa cible est manquante.","Ouvrir le lien"],"vs/editor/contrib/message/messageController":["Impossible de modifier dans l’éditeur en lecture seule"],
"vs/editor/contrib/multicursor/multicursor":["Ajouter un curseur au-dessus","&&Ajouter un curseur au-dessus","Ajouter un curseur en dessous","Aj&&outer un curseur en dessous","Ajouter des curseurs à la fin des lignes","Ajouter des c&&urseurs à la fin des lignes","Ajouter des curseurs en bas","Ajouter des curseurs en haut","Ajouter la sélection à la correspondance de recherche suivante","Ajouter l'occurrence suiva&&nte","Ajouter la sélection à la correspondance de recherche précédente","Ajouter l'occurrence p&&récédente","Déplacer la dernière sélection vers la correspondance de recherche suivante","Déplacer la dernière sélection à la correspondance de recherche précédente","Sélectionner toutes les occurrences des correspondances de la recherche","Sélectionner toutes les &&occurrences","Modifier toutes les occurrences"],"vs/editor/contrib/parameterHints/parameterHints":["Indicateurs des paramètres Trigger"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, conseil"],
"vs/editor/contrib/peekView/peekView":["Fermer","Couleur d'arrière-plan de la zone de titre de l'affichage d'aperçu.","Couleur du titre de l'affichage d'aperçu.","Couleur des informations sur le titre de l'affichage d'aperçu.","Couleur des bordures et de la flèche de l'affichage d'aperçu.","Couleur d'arrière-plan de la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan des noeuds de lignes dans la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan des noeuds de fichiers dans la liste des résultats de l'affichage d'aperçu.","Couleur d'arrière-plan de l'entrée sélectionnée dans la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan de l'entrée sélectionnée dans la liste des résultats de l'affichage d'aperçu.","Couleur d'arrière-plan de l'éditeur d'affichage d'aperçu.","Couleur d'arrière-plan de la bordure de l'éditeur d'affichage d'aperçu.","Couleur de mise en surbrillance d'une correspondance dans la liste des résultats de l'affichage d'aperçu.","Couleur de mise en surbrillance d'une correspondance dans l'éditeur de l'affichage d'aperçu.","Bordure de mise en surbrillance d'une correspondance dans l'éditeur de l'affichage d'aperçu."],
"vs/editor/contrib/quickAccess/gotoLineQuickAccess":["Ouvrez d'abord un éditeur de texte pour accéder à une ligne.","Allez à la ligne {0}, colonne {1}.","Accédez à la ligne {0}.","Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne entre 1 et {2} auquel accéder.","Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne auquel accéder."],
"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess":["Pour accéder à un symbole, ouvrez d'abord un éditeur de texte avec des informations de symbole.","L'éditeur de texte actif ne fournit pas les informations de symbole.","Aucun symbole d'éditeur correspondant","Aucun symbole d'éditeur","Ouvrir sur le côté","Ouvrir en bas","symboles ({0})","propriétés ({0})","méthodes ({0})","fonctions ({0})","constructeurs ({0})","variables ({0})","classes ({0})","structs ({0})","événements ({0})","opérateurs ({0})","interfaces ({0})","espaces de noms ({0})","packages ({0})","paramètres de type ({0})","modules ({0})","propriétés ({0})","énumérations ({0})","membres d'énumération ({0})","chaînes ({0})","fichiers ({0})","tableaux ({0})","nombres ({0})","booléens ({0})","objets ({0})","clés ({0})","champs ({0})","constantes ({0})"],"vs/editor/contrib/rename/onTypeRename":["Symbole de renommage selon le type","Couleur d'arrière-plan quand l'éditeur renomme automatiquement le type."],
"vs/editor/contrib/rename/rename":["Aucun résultat.","Une erreur inconnue s’est produite lors de la résolution de l'emplacement de renommage : {0}","Renommage de '{0}'","Changement du nom de {0}","'{0}' renommé en '{1}'. Récapitulatif : {2}","Le renommage n'a pas pu appliquer les modifications","Le renommage n'a pas pu calculer les modifications","Renommer le symbole","Activer/désactiver la possibilité d'afficher un aperçu des changements avant le renommage"],"vs/editor/contrib/rename/renameInputField":["Renommez l'entrée. Tapez le nouveau nom et appuyez sur Entrée pour valider.","{0} pour renommer, {1} pour afficher un aperçu"],"vs/editor/contrib/smartSelect/smartSelect":["Étendre la sélection","Dév&&elopper la sélection","Réduire la sélection","&&Réduire la sélection"],
"vs/editor/contrib/snippet/snippetVariables":["Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi","Dim","Lun","Mar","Mer","Jeu","Ven","Sam","Janvier","Février","Mars","Avril","Mai","Juin","Juillet","Août","Septembre","Octobre","Novembre","Décembre","Jan","Fév","Mar","Avr","Mai","Juin","Jul","Aoû","Sept","Oct","Nov","Déc"],"vs/editor/contrib/suggest/suggestController":["L'acceptation de '{0}' a entraîné {1} modifications supplémentaires","Suggestions pour Trigger","{0} pour insérer","{0} pour insérer","{0} pour remplacer","{0} pour remplacer","{0} pour insérer","afficher moins","afficher plus"],
"vs/editor/contrib/suggest/suggestWidget":["Couleur d'arrière-plan du widget de suggestion.","Couleur de bordure du widget de suggestion.","Couleur de premier plan du widget de suggestion.","Couleur d'arrière-plan de l'entrée sélectionnée dans le widget de suggestion.","Couleur de la surbrillance des correspondances dans le widget de suggestion.","En savoir plus ({0})","Lire moins ({0})","Chargement en cours...","Chargement en cours...","Pas de suggestions.","{0}, documents : {1}","Suggérer"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Activer/désactiver l'utilisation de la touche Tab pour déplacer le focus","Appuyer sur Tab déplacera le focus vers le prochain élément pouvant être désigné comme élément actif","Appuyer sur Tab insérera le caractère de tabulation"],"vs/editor/contrib/tokenization/tokenization":["Développeur : forcer la retokenisation"],
"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators":["Marques de fin de ligne inhabituelles","Marques de fin de ligne inhabituelles détectées","Ce fichier contient un ou plusieurs caractères de fin de ligne inhabituels, par exemple le séparateur de ligne (LS) ou le séparateur de paragraphe (PS).\r\n\r\nIl est recommandé de les supprimer du fichier. Vous pouvez le configurer via 'editor.unusualLineTerminators'.","Corriger ce fichier","Ignorer le problème pour ce fichier"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Couleur d'arrière-plan d'un symbole pendant l'accès en lecture, comme la lecture d'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan d'un symbole pendant l'accès en écriture, comme l'écriture d'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure d'un symbole durant l'accès en lecture, par exemple la lecture d'une variable.","Couleur de bordure d'un symbole durant l'accès en écriture, par exemple l'écriture dans une variable.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des symboles. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des symboles d'accès en écriture. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Aller à la prochaine mise en évidence de symbole","Aller à la mise en évidence de symbole précédente","Déclencher la mise en évidence de symbole"],
"vs/platform/actions/browser/menuEntryActionViewItem":["{0} ({1})"],"vs/platform/configuration/common/configurationRegistry":["Substitutions de configuration du langage par défaut","Configurez les paramètres d'éditeur à remplacer pour un langage.","Ce paramètre ne prend pas en charge la configuration par langage.","Impossible d'inscrire '{0}'. Ceci correspond au modèle de propriété '\\\\[.*\\\\]$' permettant de décrire les paramètres d'éditeur spécifiques à un langage. Utilisez la contribution 'configurationDefaults'.","Impossible d'inscrire '{0}'. Cette propriété est déjà inscrite."],"vs/platform/keybinding/common/abstractKeybindingService":["Touche ({0}) utilisée. En attente d'une seconde touche...","La combinaison de touches ({0}, {1}) n’est pas une commande."],
"vs/platform/list/browser/listService":["Banc d'essai","Mappe vers 'Contrôle' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur à utiliser pour ajouter un élément dans les arbres et listes pour une sélection multiple avec la souris (par exemple dans l’Explorateur, les éditeurs ouverts et la vue scm). Les mouvements de la souris 'Ouvrir à côté' (si pris en charge) s'adapteront tels qu’ils n'entrent pas en conflit avec le modificateur multiselect.","Contrôle l’ouverture des éléments dans les arbres et listes à l’aide de la souris (si pris en charge). Pour les parents ayant des enfants dans les arbres, ce paramètre contrôlera si un simple clic déploie le parent ou un double-clic. Notez que certains arbres et listes peuvent choisir d’ignorer ce paramètre, si ce n’est pas applicable. ","Contrôle si les listes et les arborescences prennent en charge le défilement horizontal dans le banc d'essai. Avertissement : L'activation de ce paramètre a un impact sur les performances.","Contrôle la mise en retrait de l'arborescence, en pixels.","Contrôle si l'arborescence doit afficher les repères de mise en retrait.","Détermine si les listes et les arborescences ont un défilement fluide.","La navigation au clavier Simple place le focus sur les éléments qui correspondent à l'entrée de clavier. La mise en correspondance est effectuée sur les préfixes uniquement.","La navigation de mise en surbrillance au clavier met en surbrillance les éléments qui correspondent à l'entrée de clavier. La navigation ultérieure vers le haut ou vers le bas parcourt uniquement les éléments mis en surbrillance.","La navigation au clavier Filtrer filtre et masque tous les éléments qui ne correspondent pas à l'entrée de clavier.","Contrôle le style de navigation au clavier pour les listes et les arborescences dans le banc d'essai. Les options sont Simple, Mise en surbrillance et Filtrer.","Contrôle si la navigation au clavier dans les listes et les arborescences est automatiquement déclenchée simplement par la frappe. Si défini sur 'false', la navigation au clavier est seulement déclenchée avec l'exécution de la commande 'list.toggleKeyboardNavigation', à laquelle vous pouvez attribuer un raccourci clavier."],
"vs/platform/markers/common/markers":["Erreur","Avertissement","Info"],"vs/platform/quickinput/browser/commandsQuickAccess":["{0}, {1}","récemment utilisées","autres commandes","La commande '{0}' a entraîné une erreur ({1})"],"vs/platform/quickinput/browser/helpQuickAccess":["commandes globales","commandes de l'éditeur","{0}, {1}"],
"vs/platform/theme/common/colorRegistry":["Couleur de premier plan globale. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Couleur principale de premier plan pour les messages d'erreur. Cette couleur est utilisée uniquement si elle n'est pas redéfinie par un composant.","Couleur par défaut des icônes du banc d'essai.","Couleur de bordure globale des éléments ayant le focus. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Bordure supplémentaire autour des éléments pour les séparer des autres et obtenir un meilleur contraste.","Bordure supplémentaire autour des éléments actifs pour les séparer des autres et obtenir un meilleur contraste.","Couleur des liens dans le texte.","Couleur d'arrière-plan des blocs de code dans le texte.","Couleur de l'ombre des widgets, comme rechercher/remplacer, au sein de l'éditeur.","Arrière-plan de la zone d'entrée.","Premier plan de la zone d'entrée.","Bordure de la zone d'entrée.","Couleur de la bordure des options activées dans les champs d'entrée.","Couleur d'arrière-plan des options activées dans les champs d'entrée.","Couleur de premier plan des options activées dans les champs d'entrée.","Couleur d'arrière-plan de la validation d'entrée pour la gravité des informations.","Couleur de premier plan de validation de saisie pour la sévérité Information.","Couleur de bordure de la validation d'entrée pour la gravité des informations.","Couleur d'arrière-plan de la validation d'entrée pour la gravité de l'avertissement.","Couleur de premier plan de la validation de la saisie pour la sévérité Avertissement.","Couleur de bordure de la validation d'entrée pour la gravité de l'avertissement.","Couleur d'arrière-plan de la validation d'entrée pour la gravité de l'erreur.","Couleur de premier plan de la validation de saisie pour la sévérité Erreur.","Couleur de bordure de la validation d'entrée pour la gravité de l'erreur. ","Arrière-plan de la liste déroulante.","Premier plan de la liste déroulante.","Couleur de premier plan du bouton.","Couleur d'arrière-plan du bouton.","Couleur d'arrière-plan du bouton pendant le pointage.","Couleur de fond des badges. Les badges sont de courts libellés d'information, ex. le nombre de résultats de recherche.","Couleur des badges. Les badges sont de courts libellés d'information, ex. le nombre de résultats de recherche.","Ombre de la barre de défilement pour indiquer que la vue défile.","Couleur de fond du curseur de la barre de défilement.","Couleur de fond du curseur de la barre de défilement lors du survol.","Couleur d’arrière-plan de la barre de défilement lorsqu'on clique dessus.","Couleur de fond pour la barre de progression qui peut s'afficher lors d'opérations longues.","Couleur de premier plan de la ligne ondulée marquant les erreurs dans l'éditeur.","Couleur de bordure des zones d'erreur dans l'éditeur.","Couleur de premier plan de la ligne ondulée marquant les avertissements dans l'éditeur.","Couleur de bordure des zones d'avertissement dans l'éditeur.","Couleur de premier plan de la ligne ondulée marquant les informations dans l'éditeur.","Couleur de bordure des zones d'informations dans l'éditeur.","Couleur de premier plan de la ligne ondulée d'indication dans l'éditeur.","Couleur de bordure des zones d'indication dans l'éditeur.","Couleur d'arrière-plan de l'éditeur.","Couleur de premier plan par défaut de l'éditeur.","Couleur d'arrière-plan des gadgets de l'éditeur tels que rechercher/remplacer.","Couleur de premier plan des widgets de l'éditeur, notamment Rechercher/remplacer.","Couleur de bordure des widgets de l'éditeur. La couleur est utilisée uniquement si le widget choisit d'avoir une bordure et si la couleur n'est pas remplacée par un widget.","Couleur de bordure de la barre de redimensionnement des widgets de l'éditeur. La couleur est utilisée uniquement si le widget choisit une bordure de redimensionnement et si la couleur n'est pas remplacée par un widget.","Couleur d'arrière-plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.","Couleur de premier plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.","Couleur d'arrière-plan du titre du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.","Couleur du sélecteur rapide pour les étiquettes de regroupement.","Couleur du sélecteur rapide pour les bordures de regroupement.","Couleur de la sélection de l'éditeur.","Couleur du texte sélectionné pour le contraste élevé.","Couleur de la sélection dans un éditeur inactif. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur des régions dont le contenu est le même que celui de la sélection. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des régions dont le contenu est identique à la sélection.","Couleur du résultat de recherche actif.","Couleur des autres correspondances de recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure du résultat de recherche actif.","Couleur de bordure des autres résultats de recherche.","Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Surlignage sous le mot sélectionné par pointage. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan du pointage de l'éditeur.","Couleur de premier plan du pointage de l'éditeur.","Couleur de bordure du pointage de l'éditeur.","Couleur d'arrière-plan de la barre d'état du pointage de l'éditeur.","Couleur des liens actifs.","Couleur utilisée pour l'icône d'ampoule suggérant des actions.","Couleur utilisée pour l'icône d'ampoule suggérant des actions de correction automatique.","Couleur d'arrière-plan du texte inséré. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan du texte supprimé. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de contour du texte inséré.","Couleur de contour du texte supprimé.","Couleur de bordure entre les deux éditeurs de texte.","Couleur du remplissage diagonal de l'éditeur de différences. Le remplissage diagonal est utilisé dans les vues de différences côte à côte.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence de l'élément sélectionné quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier (elle ne l'est pas quand elle est inactive).","Arrière-plan de la liste/l'arborescence pendant le pointage sur des éléments avec la souris.","Premier plan de la liste/l'arborescence pendant le pointage sur des éléments avec la souris.","Arrière-plan de l'opération de glisser-déplacer dans une liste/arborescence pendant le déplacement d'éléments avec la souris.","Couleur de premier plan dans la liste/l'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.","Couleur d'arrière-plan du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l'absence de correspondance.","Couleur de trait de l'arborescence pour les repères de mise en retrait.","Couleur de bordure des menus.","Couleur de premier plan des éléments de menu.","Couleur d'arrière-plan des éléments de menu.","Couleur de premier plan de l'élément de menu sélectionné dans les menus.","Couleur d'arrière-plan de l'élément de menu sélectionné dans les menus.","Couleur de bordure de l'élément de menu sélectionné dans les menus.","Couleur d'un élément de menu séparateur dans les menus.","Couleur d’arrière-plan de mise en surbrillance d’un extrait tabstop.","Couleur de bordure de mise en surbrillance d’un extrait tabstop.","Couleur d’arrière-plan de mise en surbrillance du tabstop final d’un extrait.","Mettez en surbrillance la couleur de bordure du dernier taquet de tabulation d'un extrait de code.","Couleur de marqueur de la règle d'aperçu pour rechercher les correspondances. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des sélections. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la minimap pour les correspondances.","Couleur de marqueur du minimap pour la sélection de l'éditeur.","Couleur de marqueur de minimap pour les erreurs.","Couleur de marqueur de minimap pour les avertissements.","Couleur d'arrière-plan du minimap.","Couleur d'arrière-plan du curseur de minimap.","Couleur d'arrière-plan du curseur de minimap pendant le survol.","Couleur d'arrière-plan du curseur de minimap pendant un clic.","Couleur utilisée pour l'icône d'erreur des problèmes.","Couleur utilisée pour l'icône d'avertissement des problèmes.","Couleur utilisée pour l'icône d'informations des problèmes."],
"vs/platform/undoRedo/common/undoRedoService":["Les fichiers suivants ont été fermés et modifiés sur le disque : {0}.","Les fichiers suivants ont été modifiés de manière incompatible : {0}.","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers, car des modifications ont été apportées à {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement est déjà en cours d'exécution sur {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement s'est produite dans l'intervalle","Souhaitez-vous annuler '{0}' dans tous les fichiers ?","Annuler dans {0} fichiers","Annuler ce fichier","Annuler","Impossible d'annuler '{0}', car une opération d'annulation ou de rétablissement est déjà en cours d'exécution.","Impossible de répéter '{0}' dans tous les fichiers. {1}","Impossible de répéter '{0}' dans tous les fichiers. {1}","Impossible de répéter '{0}' dans tous les fichiers, car des modifications ont été apportées à {1}","Impossible de rétablir '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement est déjà en cours d'exécution sur {1}","Impossible de rétablir '{0}' dans tous les fichiers, car une opération d'annulation ou de rétablissement s'est produite dans l'intervalle","Impossible de rétablir '{0}', car une opération d'annulation ou de rétablissement est déjà en cours d'exécution."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.fr.js.map