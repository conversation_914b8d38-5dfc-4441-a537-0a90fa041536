.drop-element, .drop-element:after, .drop-element:before, .drop-element *, .drop-element *:after, .drop-element *:before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.drop-element {
  position: absolute;
  display: none; }
  .drop-element.drop-open {
    display: block; }

.drop-element.drop-theme-arrows-bounce {
  max-width: 100%;
  max-height: 100%; }
  .drop-element.drop-theme-arrows-bounce .drop-content {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    position: relative;
    font-family: inherit;
    background: white;
    color: #444444;
    padding: 1em;
    font-size: 1.1em;
    line-height: 1.5em;
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    -moz-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2)); }
    .drop-element.drop-theme-arrows-bounce .drop-content:before {
      content: "";
      display: block;
      position: absolute;
      width: 0;
      height: 0;
      border-color: transparent;
      border-width: 12px;
      border-style: solid; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-center .drop-content {
    margin-bottom: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-center .drop-content:before {
      top: 100%;
      left: 50%;
      margin-left: -12px;
      border-top-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-center .drop-content {
    margin-top: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-center .drop-content:before {
      bottom: 100%;
      left: 50%;
      margin-left: -12px;
      border-bottom-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-right.drop-element-attached-middle .drop-content {
    margin-right: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-right.drop-element-attached-middle .drop-content:before {
      left: 100%;
      top: 50%;
      margin-top: -12px;
      border-left-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-left.drop-element-attached-middle .drop-content {
    margin-left: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-left.drop-element-attached-middle .drop-content:before {
      right: 100%;
      top: 50%;
      margin-top: -12px;
      border-right-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-left.drop-target-attached-bottom .drop-content {
    margin-top: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-left.drop-target-attached-bottom .drop-content:before {
      bottom: 100%;
      left: 12px;
      border-bottom-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-right.drop-target-attached-bottom .drop-content {
    margin-top: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-right.drop-target-attached-bottom .drop-content:before {
      bottom: 100%;
      right: 12px;
      border-bottom-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-left.drop-target-attached-top .drop-content {
    margin-bottom: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-left.drop-target-attached-top .drop-content:before {
      top: 100%;
      left: 12px;
      border-top-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-right.drop-target-attached-top .drop-content {
    margin-bottom: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-right.drop-target-attached-top .drop-content:before {
      top: 100%;
      right: 12px;
      border-top-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-right.drop-target-attached-left .drop-content {
    margin-right: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-right.drop-target-attached-left .drop-content:before {
      top: 12px;
      left: 100%;
      border-left-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-left.drop-target-attached-right .drop-content {
    margin-left: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-left.drop-target-attached-right .drop-content:before {
      top: 12px;
      right: 100%;
      border-right-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-right.drop-target-attached-left .drop-content {
    margin-right: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-right.drop-target-attached-left .drop-content:before {
      bottom: 12px;
      left: 100%;
      border-left-color: white; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-left.drop-target-attached-right .drop-content {
    margin-left: 12px; }
    .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-left.drop-target-attached-right .drop-content:before {
      bottom: 12px;
      right: 100%;
      border-right-color: white; }

.drop-element.drop-theme-arrows-bounce {
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-transition: opacity 0.1s;
  -moz-transition: opacity 0.1s;
  -o-transition: opacity 0.1s;
  transition: opacity 0.1s;
  opacity: 0; }
  .drop-element.drop-theme-arrows-bounce .drop-content {
    -webkit-transition: -webkit-transform 0.3s cubic-bezier(0, 0, 0.265, 1.55);
    -moz-transition: -moz-transform 0.3s cubic-bezier(0, 0, 0.265, 1.55);
    -o-transition: -o-transform 0.3s cubic-bezier(0, 0, 0.265, 1.55);
    transition: transform 0.3s cubic-bezier(0, 0, 0.265, 1.55);
    -webkit-transform: scale(0) translateZ(0);
    -moz-transform: scale(0) translateZ(0);
    -ms-transform: scale(0) translateZ(0);
    -o-transform: scale(0) translateZ(0);
    transform: scale(0) translateZ(0); }
  .drop-element.drop-theme-arrows-bounce.drop-open {
    display: none; }
  .drop-element.drop-theme-arrows-bounce.drop-open-transitionend {
    display: block; }
  .drop-element.drop-theme-arrows-bounce.drop-after-open {
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
    opacity: 1; }
    .drop-element.drop-theme-arrows-bounce.drop-after-open .drop-content {
      -webkit-transform: scale(1) translateZ(0);
      -moz-transform: scale(1) translateZ(0);
      -ms-transform: scale(1) translateZ(0);
      -o-transform: scale(1) translateZ(0);
      transform: scale(1) translateZ(0); }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-center .drop-content {
    -webkit-transform-origin: 50% calc(100% + 12px);
    -moz-transform-origin: 50% calc(100% + 12px);
    -ms-transform-origin: 50% calc(100% + 12px);
    -o-transform-origin: 50% calc(100% + 12px);
    transform-origin: 50% calc(100% + 12px); }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-center .drop-content {
    -webkit-transform-origin: 50% -12px;
    -moz-transform-origin: 50% -12px;
    -ms-transform-origin: 50% -12px;
    -o-transform-origin: 50% -12px;
    transform-origin: 50% -12px; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-right.drop-element-attached-middle .drop-content {
    -webkit-transform-origin: calc(100% + 12px) 50%;
    -moz-transform-origin: calc(100% + 12px) 50%;
    -ms-transform-origin: calc(100% + 12px) 50%;
    -o-transform-origin: calc(100% + 12px) 50%;
    transform-origin: calc(100% + 12px) 50%; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-left.drop-element-attached-middle .drop-content {
    -webkit-transform-origin: -12px 50%;
    -moz-transform-origin: -12px 50%;
    -ms-transform-origin: -12px 50%;
    -o-transform-origin: -12px 50%;
    transform-origin: -12px 50%; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-left.drop-target-attached-bottom .drop-content {
    -webkit-transform-origin: 0 -12px;
    -moz-transform-origin: 0 -12px;
    -ms-transform-origin: 0 -12px;
    -o-transform-origin: 0 -12px;
    transform-origin: 0 -12px; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-right.drop-target-attached-bottom .drop-content {
    -webkit-transform-origin: 100% -12px;
    -moz-transform-origin: 100% -12px;
    -ms-transform-origin: 100% -12px;
    -o-transform-origin: 100% -12px;
    transform-origin: 100% -12px; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-left.drop-target-attached-top .drop-content {
    -webkit-transform-origin: 0 calc(100% + 12px);
    -moz-transform-origin: 0 calc(100% + 12px);
    -ms-transform-origin: 0 calc(100% + 12px);
    -o-transform-origin: 0 calc(100% + 12px);
    transform-origin: 0 calc(100% + 12px); }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-right.drop-target-attached-top .drop-content {
    -webkit-transform-origin: 100% calc(100% + 12px);
    -moz-transform-origin: 100% calc(100% + 12px);
    -ms-transform-origin: 100% calc(100% + 12px);
    -o-transform-origin: 100% calc(100% + 12px);
    transform-origin: 100% calc(100% + 12px); }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-right.drop-target-attached-left .drop-content {
    -webkit-transform-origin: calc(100% + 12px) 0;
    -moz-transform-origin: calc(100% + 12px) 0;
    -ms-transform-origin: calc(100% + 12px) 0;
    -o-transform-origin: calc(100% + 12px) 0;
    transform-origin: calc(100% + 12px) 0; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-top.drop-element-attached-left.drop-target-attached-right .drop-content {
    -webkit-transform-origin: -12px 0;
    -moz-transform-origin: -12px 0;
    -ms-transform-origin: -12px 0;
    -o-transform-origin: -12px 0;
    transform-origin: -12px 0; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-right.drop-target-attached-left .drop-content {
    -webkit-transform-origin: calc(100% + 12px) 100%;
    -moz-transform-origin: calc(100% + 12px) 100%;
    -ms-transform-origin: calc(100% + 12px) 100%;
    -o-transform-origin: calc(100% + 12px) 100%;
    transform-origin: calc(100% + 12px) 100%; }
  .drop-element.drop-theme-arrows-bounce.drop-element-attached-bottom.drop-element-attached-left.drop-target-attached-right .drop-content {
    -webkit-transform-origin: -12px 100%;
    -moz-transform-origin: -12px 100%;
    -ms-transform-origin: -12px 100%;
    -o-transform-origin: -12px 100%;
    transform-origin: -12px 100%; }
