import request from '@/utils/request'


// 工序库 查询表格数据
export function processList(params) {
  return request({
    url: `/api/ProductProcessController/queryByLike`,
    method: 'GET',
    data: params
  })
}


// 工序库 新增
export function addProcess(data) {
  return request({
    url: `/api/ProductProcessController/save`,
    method: 'POST',
    data
  })
}


// 工序库 删除表格中的数据
export function deleteProcess(id) {
  return request({
    url: `/api/ProductProcessController/${id}`,
    method: 'DELETE',
  })
}

// 工序库 查询
export function queryProcess(id) {
  return request({
    url: `/api/ProductProcessController/detail/${id}`,
    method: 'GET',
  })
}


// 工序库 提交
export function sumbitProcess(data) {
  return request({
    url: `/api/ProductProcessController/submit`,
    method: 'POST',
    data
  })
}


// 工序库  分类接口

// 获取分类
export function getClassification() {
  return request({
    url: `/api/productProcessCategoryController/getList`,
    method: 'GET',
  })
}

/**
 * @name 工序库新增分类
 * @param {*}  categoryName  分类名称
 * @returns 
 */
// 新增分类
export function addClassification({
  categoryName
}) {
  return request({
    url: `/api/productProcessCategoryController/save`,
    method: 'POST',
    data: {
      categoryName
    }
  })
}


/**
 * @name 工序库编辑分类
 * @param {*} categoryName   分类名称
 * @param {*} id   分类id
 * @returns 
 */
// 编辑分类
export function editClassification({
  id,
  categoryName
}) {
  return request({
    url: `/api/productProcessCategoryController/edit`,
    method: 'PUT',
    data: {
      id,
      categoryName
    }
  })
}

// 删除分类
export function deleteClassification(id) {
  return request({
    url: `/api/productProcessCategoryController/${id}`,
    method: 'DELETE',

  })
}