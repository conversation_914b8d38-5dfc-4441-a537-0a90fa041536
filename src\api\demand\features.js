import request from '@/utils/request'

// 获取需求特征库树数据
export function getFeaturesTree(innerName) {
  return request({
    url: `/api/system/serviceConfiguration/type/select/tree/${innerName}`,
    method: 'GET'
  })
}

// 获取特征项列表
export function getFeaturesList(data) {
  return request({
    url: `/api/demand/featureItem`,
    method: 'GET',
    data
  })
}

// 添加特征
export function addFeatures(data) {
  return request({
    url: `/api/demand/featureItem`,
    method: 'PUT',
    data
  })
}

// 获取枚举字典
export function getFeatureItemEnum(typeCode) {
  return request({
    url: `/api/system/DictionaryType/getChildByCode/${typeCode}`,
    method: 'GET',
  })
}

// 修改特征
export function updateFeatures(data) {
  return request({
    url: `/api/demand/featureItem`,
    method: 'POST',
    data
  })
}

// 删除特征
export function deleteFeatures(data) {
  return request({
    url: `/api/demand/featureItem`,
    method: 'DELETE',
    data
  })
}

// 审核通过
export function passFeatures(data) {
  return request({
    url: `/api/demand/featureItem/pass`,
    method: 'POST',
    data
  })
}
// 取消审核
export function cancelAuditFeatures(data) {
  return request({
    url: `/api/demand/featureItem/cancel`,
    method: 'POST',
    data
  })
}
