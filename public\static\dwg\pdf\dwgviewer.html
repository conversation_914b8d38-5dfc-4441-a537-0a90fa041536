﻿<!DOCTYPE html>
<html lang="en" height="100%">
  <head>
    <title>文件预览</title>
    <meta charset="utf-8" />
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />

    <link rel="stylesheet" href="assets/style.css" />
    <link rel="stylesheet" href="assets/colorbox/colorbox.css" />
    <link rel="stylesheet" href="../css/Bf.css" />

    <style>
      #viewerElement {
        width: 100%;
        height: 100%;
      }

      body {
        height: 100%;
      }

      html {
        height: 100%;
      }
    </style>
    <script type="text/javascript" src="../js/jquery-3.2.1.min.js"></script>
    <script type="text/javascript" src="lib/WebViewer.js"></script>
    <script type="text/javascript" src="../bimjs/qmodel.2d.min.js"></script>
    <script src="../js/jquery.cookie.js"></script>
    <script src="../bimjs/QmodelServer.js"></script>
    <script src="../bimjs/net.min.js"></script>
  </head>

  <body>
    <div id="viewerElement"></div>

    <!-- <div id="versionChange" class="qm-toolbar qm-toolbar-bottom">
      <div style="display: none">
        <div class="qm-button qmd-arrow  bf-checked" title="箭头"></div>
        <div class="qm-button qmd-cloud" title="云线框"></div>
        <div class="qm-button qmd-rectangle" title="矩形"></div>
        <div class="qm-button qmd-circle" title="圆"></div>
        <div class="qm-button qmd-text" title="文字"></div>

        <div class="qm-button qmd-save" title="保存"></div>
        <div class="qm-button qmd-cancle" title="取消"></div>
        <select id="tzselect" style="margin-top: 5px;width: 150px;height: 30px;">
        </select>
      </div>
      <div style="display: inline-block;background-color: white;margin: 0 150px 0 10px">
        <span id="fileName"></span>
      </div>
      <select onchange="changeVersion(this.value)" id="tzselectx" style="margin-top: 5px;width: 150px;height: 30px;">
      </select>
    </div> -->

    <script>
      var view2d
      function decodeBase64(str) {
        // 解码 Base64 并处理 Unicode
        return decodeURIComponent(escape(atob(str)))
      }
      function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)")
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
      }
      $(function() {
        url = getUrlParam("url")
        console.log("url", decodeBase64(url))
        const u = decodeBase64(url)
        token = getUrlParam("token")
        var net = new NetHandle(token)
        view2d = new Viewer2D(net, u)
        view2d.init()
      })

      // var view2d;
      // var fileId;
      // var productFileId;
      // function getUrlParam(name) {
      //   var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      //   var r = window.location.search.substr(1).match(reg);
      //   if (r != null) return unescape(r[2]); return null;
      // }

      // $(function() {
      //   fileId = getUrlParam("model")
      //   productFileId = getUrlParam("productFileId")
      //   if (productFileId > 0) {
      //     console.log(fileId)
      //     $("#tzselectx").val(fileId)
      //     getVersion();
      //     $("#versionChange").show()
      //   }else {
      //     $("#versionChange").hide()
      //   }
      //     var net = new NetHandle();
      //     var thefile = _0xBC("model");
      //     var thepdf = _0xBC("files");
      //     view2d = new Viewer2D(net, thefile, thepdf);
      //     view2d.init();
      // });
      //  function changeVersion(value) {
      //    if (value > 0) {
      //      window.location.href = "/static/dwg/pdf/dwgviewer.html?model=" + value + "&files=Model.pdf" + "&productFileId=" + productFileId
      //    } else {
      //      $("#tzselectx").val(fileId)
      //      alert("你选择的版本没有可预览的文件！")
      //    }
      //  }
      // function getVersion() {
      //   $.ajax({ url: "/api/search/queryFileVersions?productFileId=" + productFileId, success: function(res){
      //       $("#fileName").html(res.result.prodFile.code)
      //       for (i = 0; i < res.result.versions.length; i++) {
      //         if(res.result.versions[i].pdfFileId > 0){
      //           if (res.result.versions[i].pdfFileId == fileId) {
      //             $("#tzselectx").append(`<option selected value=${res.result.versions[i].pdfFileId}>${res.result.versions[i].version}</option>`);
      //           }else {
      //             $("#tzselectx").append(`<option value=${res.result.versions[i].pdfFileId}>${res.result.versions[i].version}</option>`);
      //           }
      //         }else {
      //           $("#tzselectx").append(`<option value=${res.result.versions[i].pdfFileId}>${res.result.versions[i].version}</option>`);
      //         }
      //       }
      //       console.log(res)
      //     }});
      // }
    </script>
  </body>
</html>
