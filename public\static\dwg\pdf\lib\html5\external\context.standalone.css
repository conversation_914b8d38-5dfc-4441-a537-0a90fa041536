/**
 * ContextJS Styles
 * For use WITHOUT Twitters Bootstrap CSS
 */

.nav-header {
	display: block;
	padding: 3px 15px;
	font-size: 11px;
	font-weight: bold;
	line-height: 20px;
	color: #999;
	text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
	text-transform: uppercase;
}
.dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	z-index: 1000;
	display: none;
	float: left;
	min-width: 160px;
	padding: 5px 0;
	margin: 2px 0 0;
	list-style: none;
	background-color: #ffffff;
	border: 1px solid #ccc;
	border: 1px solid rgba(0, 0, 0, 0.2);
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 14px;
	*border-right-width: 2px;
	*border-bottom-width: 2px;
	-webkit-border-radius: 6px;
	-moz-border-radius: 6px;
	border-radius: 6px;
	-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-webkit-background-clip: padding-box;
	-moz-background-clip: padding;
	background-clip: padding-box;
	text-align:left;
	opacity: 0.01;
	visibility: hidden;
}
.dropdown-menu.pull-right {
	right: 0;
	left: auto;
}
.dropdown-menu .divider {
	*width: 100%;
	height: 1px;
	margin: 9px 1px;
	*margin: -5px 0 5px;
	overflow: hidden;
	background-color: #e5e5e5;
	border-bottom: 1px solid #ffffff;
}
.dropdown-menu a {
	display: block;
	padding: 3px 20px;
	clear: both;
	font-weight: normal;
	line-height: 20px;
	color: #333333;
	white-space: nowrap;
	text-decoration: none;
}
.dropdown-menu li > a:hover, .dropdown-menu li > a:focus, .dropdown-submenu:hover > a {
	color: #ffffff;
	text-decoration: none;
	background-color: #0088cc;
	background-color: #0081c2;
	background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
	background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
	background-image: -o-linear-gradient(top, #0088cc, #0077b3);
	background-image: linear-gradient(to bottom, #0088cc, #0077b3);
	background-repeat: repeat-x;
	filter: progid: dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}
.dropdown-menu .active > a, .dropdown-menu .active > a:hover {
	color: #ffffff;
	text-decoration: none;
	background-color: #0088cc;
	background-color: #0081c2;
	background-image: linear-gradient(to bottom, #0088cc, #0077b3);
	background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
	background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
	background-image: -o-linear-gradient(top, #0088cc, #0077b3);
	background-repeat: repeat-x;
	outline: 0;
	filter: progid
	: dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}
.dropdown-menu .disabled > a, .dropdown-menu .disabled > a:hover {
	color: #999999;
}
.dropdown-menu .disabled > a:hover {
	text-decoration: none;
	cursor: default;
	background-color: transparent;
}
.open {
	*z-index: 1000;
}
.open > .dropdown-menu {
	display: block;
}
.pull-right > .dropdown-menu {
	right: 0;
	left: auto;
}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
	border-top: 0;
	border-bottom: 4px solid #000000;
	content: "\2191";
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
	top: auto;
	bottom: 100%;
	margin-bottom: 1px;
}
.dropdown-submenu {
	position: relative;
}
.dropdown-submenu > .dropdown-menu {
	top: 0;
	left: 100%;
	margin-top: -6px;
	margin-left: -1px;
	-webkit-border-radius: 0 6px 6px 6px;
	-moz-border-radius: 0 6px 6px 6px;
	border-radius: 0 6px 6px 6px;
}
.dropdown-submenu > .dropdown-menu.drop-left{
	left:-100%;
}
.dropdown-submenu:hover .dropdown-menu {
	display: block;
}
.dropdown-submenu > a:after {
	display: block;
	float: right;
	width: 0;
	height: 0;
	margin-top: 5px;
	margin-right: -10px;
	border-color: transparent;
	border-left-color: #cccccc;
	border-style: solid;
	border-width: 5px 0 5px 5px;
	content: " ";
}
.dropdown-submenu:hover > a:after {
	border-left-color: #ffffff;
}
.dropdown .dropdown-menu .nav-header {
	padding-right: 20px;
	padding-left: 20px;
}
/**
 * 	Context Styles
 */

.dropdown-context .nav-header {
	cursor: default;
}
.dropdown-context:before, .dropdown-context-up:before {
	position: absolute;
	top: -7px;
	left: 9px;
	display: inline-block;
	border-right: 7px solid transparent;
	border-bottom: 7px solid #ccc;
	border-left: 7px solid transparent;
	border-bottom-color: rgba(0, 0, 0, 0.2);
	content: '';
}
.dropdown-context:after, .dropdown-context-up:after {
	position: absolute;
	top: -6px;
	left: 10px;
	display: inline-block;
	border-right: 6px solid transparent;
	border-bottom: 6px solid #ffffff;
	border-left: 6px solid transparent;
	content: '';
}
.dropdown-context-up:before, .dropdown-context-up:after {
	top: auto;
	bottom: -7px;
	z-index: 9999;
}
.dropdown-context-up:before {
	border-right: 7px solid transparent;
	border-top: 7px solid #ccc;
	border-bottom: none;
	border-left: 7px solid transparent;
}
.dropdown-context-up:after {
	border-right: 6px solid transparent;
	border-top: 6px solid #ffffff;
	border-left: 6px solid transparent;
	border-bottom: none;
}
.dropdown-context-sub:before, .dropdown-context-sub:after {
	display: none;
}
.dropdown-context .dropdown-submenu:hover .dropdown-menu {
	display: none;
}
.dropdown-context .dropdown-submenu:hover > .dropdown-menu {
	display: block;
}

.compressed-context a{
	padding-left: 14px;
	padding-top: 0;
	padding-bottom: 0;
	font-size: 13px;
	}
.compressed-context .divider{
	margin: 5px 1px;
	}
.compressed-context .nav-header{
	padding:1px 13px;
	}
