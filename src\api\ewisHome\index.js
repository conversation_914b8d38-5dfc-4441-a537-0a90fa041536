import request from '@/utils/request'
import qs from 'qs'

// 获取常用功能
export function getCommonly() {
  return request({
    url: `/api/system/index/common/function`,
    method: 'GET',
  })
}

// 添加常用功能
export function setCommonly(data) {
  return request({
    url: `/api/system/index/common/function`,
    method: 'POST',
    data
  })
}

// 菜单树

export function getMenuTree(systemId, enabledMark) {
  return request({
    url: `/api/system/Menu/ModuleBySystem/${systemId}?enabledMark=${enabledMark}`,
    method: 'GET',
  })
}

// 获取待办任务
export function getTodoTask(data) {
  return request({
    url: `/api/todo/project/task/index`,
    method: 'GET',
    data
  })
}

export function unreadCount() {
  return request({
    url: `/api/message/unread/count`,
    method: 'GET'
  })
}