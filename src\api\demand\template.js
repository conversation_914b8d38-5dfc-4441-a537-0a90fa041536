import request from '@/utils/request'
import qs from 'qs'

// 获取需求模板列表
export function getTemplateList(data) {
  return request({
    url: `/api/demand/demandTemplate`,
    method: 'GET',
    data,
    paramsSerializer: params => {
      return qs.stringify(params, { indices: false })
    }
  })
}
// 获取特征项下拉列表
export function getFeaturesSelector() {
  return request({
    url: `/api/demand/featureItem/selectorList`,
    method: 'GET',
  })
}


// 查看
export function getTemplateItem(id) {
  return request({
    url: `/api/demand/demandTemplate/${id}`,
    method: 'GET',
  })
}

// 添加
export function addTemplate(data) {
  return request({
    url: `/api/demand/demandTemplate`,
    method: 'PUT',
    data
  })
}

// 修改
export function updateTemplate(data) {
  return request({
    url: `/api/demand/demandTemplate`,
    method: 'POST',
    data
  })
}

// 删除
export function deleteTemplate(data) {
  return request({
    url: `/api/demand/demandTemplate`,
    method: 'DELETE',
    data
  })
}

// 审核
export function passTemplate(data) {
  return request({
    url: `/api/demand/demandTemplate/pass`,
    method: 'POST',
    data
  })
}

// 需求模板下拉
export function getTemplateSelectList() {
  return request({
    url: `/api/demand/demandTemplate/selector`,
    method: 'GET',
  })
}