<template>
  <el-row>
    <el-form-item label="控件标题">
      <el-input v-model="activeData.__config__.label" placeholder="请输入控件标题" />
    </el-form-item>
    <el-form-item label="显示标题">
      <el-switch v-model="activeData.__config__.showLabel" />
    </el-form-item>
    <el-form-item label="标题提示">
      <el-input v-model="activeData.__config__.tipLabel" placeholder="请输入标题提示" />
    </el-form-item>
    <el-form-item label="选择签章">
      <jnpf-select v-model="activeData.ableIds" :options="options" multiple filterable
        placeholder="请选择签章" :props="props" />
    </el-form-item>
    <el-form-item label="是否禁用">
      <el-switch v-model="activeData.disabled" />
    </el-form-item>
    <el-form-item label="是否隐藏">
      <el-switch v-model="activeData.__config__.noShow" />
    </el-form-item>
    <el-form-item label="是否必填">
      <el-switch v-model="activeData.__config__.required" />
    </el-form-item>
  </el-row>
</template>
<script>
import { getSignatureSelector } from '@/api/system/signature'
export default {
  props: ['activeData'],
  data() {
    return {
      props: {
        label: "fullName",
        value: "id"
      },
      show: false,
      options: []
    }
  },
  created() {
    this.getSignatureSelector()
  },
  methods: {
    getSignatureSelector() {
      getSignatureSelector().then(res => {
        this.options = res.data.list || []
      })
    }
  }
}

</script>