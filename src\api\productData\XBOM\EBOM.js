import request from '@/utils/request'

// E-bom --- 列表查询
export function queryEBomList(params) {
  return request({
    url: `/api/ProductEbomController/searchAll`,
    method: 'GET',
    data: params
  })
}


// E-bom --- 列表修改
export function editEBomList(data) {
  return request({
    url: `/api/ProductEbomController/update`,
    method: 'POST',
    data
  })
}


// E-bom --- 列表删除
export function deleteEBomList(id) {
  return request({
    url: `/api/ProductEbomController/${id}`,
    method: 'DELETE',
  })
}


/**
 * @name E-bom列表新增
 * @param {*} param0.ebomName bom名称
 * @param {*} param0.materialCode 物料编号
 * @param {*} param0.materialVersion 物料版本
 * @param {*} param0.businessType 业务类型
 * @param {*} param0.remark 备注
 * @param {*} param0.materialName 物料名
 * @param {*} param0.econstructs 结构数据
 * @returns 
 */
export function addEBomList({
  ebomName,
  materialCode,
  materialVersion,
  businessType,
  remark,
  category,
  econstructs,
  materialName
}) {
  return request({
    url: `/api/ProductEbomController/save`,
    method: 'POST',
    data: {
      ebomName,
      materialCode,
      materialVersion,
      businessType,
      remark,
      category,
      econstructs,
      materialName
    }
  })
}

// E-bom --- 列表单挑数据详情
export function queryEBomDetails(id) {
  return request({
    url: `/api/ProductEbomController/getById/${id}`,
    method: 'GET',

  })
}



// E-bom --- 获取物料编码
export function getMaterialCode(data) {
  return request({
    url: `/api/ProductMaterialLibraryDataHisController/queryByLike`,
    method: 'POST',
    data
  })
}


// E-BOM 结构 表格数据新增
export function addStructureList(data) {
  return request({
    url: `/api/ProductEbomConstructController/save`,
    method: 'POST',
    data
  })
}

// E-BOM 结构 表格数据查询
export function queryStructureList(data) {
  return request({
    url: `/api/ProductEbomConstructController/search`,
    method: 'POST',
    data
  })
}

export function queryStructureListVersion(data) {
  return request({
    url: `/api/ProductEbomConstructController/searchHis`,
    method: 'POST',
    data
  })
}

// E-BOM 结构 表格数据修改
export function editStructureList(data) {
  return request({
    url: `/api/ProductEbomConstructController/update`,
    method: 'POST',
    data
  })
}


// E-BOM 结构 表格数据删除
export function deleteStructureList(id) {
  return request({
    url: `/api/ProductEbomConstructController/${id}`,
    method: 'DELETE',

  })
}


// E-BOM 结构 表格数据导入
export function importFile(data) {
  return request({
    url: `/api/ProductEbomConstructController/import`,
    method: 'POST',
    data
  })
}

export function importFileBeta(data) {
  return request({
    url: `/api/ProductEbomConstructController/importBeta`,
    method: 'POST',
    data
  })
}


// E-BOM 图纸 表格数据查询
export function queryDrawList(data) {
  return request({
    url: `/api/ProductDocumentLibraryFileController/getInfoByEbom`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构
export function searchTree(data) {
  return request({
    url: `/api/ProductEbomController/searchTree`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构 点击子级展示关联子级
export function searchTreeSon(data) {
  return request({
    url: `/api/ProductEbomController/searchTreeSon`,
    method: 'POST',
    data
  })
}

// E-BOM 查看 左侧的树形结构 反查
export function peggingTree(data) {
  return request({
    url: `/api/ProductEbomController/peggingTree`,
    method: 'POST',
    data
  })
}

// BOM 比较
// BOM 比较 获取 BOM 的版本号
export function ebomVersion(code) {
  return request({
    url: `/api/ProductEbomConstructController/getVersion/${code}`,
    method: 'GET',
  })
}


// BOM 比较 获取 BOM 的版本号
export function ebomCompare(data) {
  return request({
    url: `/api/ProductEbomConstructController/contrast/${data.codeOne}/${data.versionOne}/${data.codeTwo}/${data.versionTwo}`,
    method: 'GET',
  })
}


/**
 * @name E-bom变更
 * @param {*} bomcode  bom 编码
 * @param {*} version  bom 版本
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function getBomInfo(data) {
  return request({
    url: `/api/ProductEbomController/getInfoByCode`,
    method: 'POST',
    data
  })
}



/**
 * @name E-bom变更
 * @param {*} constructs  修订的表格数据
 * @param {*} id  主键ID
 * @param {*} code  bom编码
 * @param {*} version  bom版本
 * @param {*} materialCode  物料编码
 * @param {*} materialVersion  物料版本
 * @param {*} materialName  物料名称
 * @param {*} category  归属目录
 * @param {*} materialSpecs  物料规格型号
 * @param {*} preChangeContent  变更前内容
 * @param {*} revisedContent  变更后内容
 * @param {*} specificReasons  具体内容
 * @returns 
 */
// 变更
// 通过 bom 的编号和版本获取数据
export function saveRevision({
  constructs,
  id,
  code,
  version,
  materialCode,
  materialVersion,
  materialName,
  category,
  materialSpecs,
  preChangeContent,
  revisedContent,
  specificReasons
}) {
  return request({
    url: `/api/ProductEbomController/updateInfoByCodeAndVersion`,
    method: 'POST',
    data: {
      constructs,
      id,
      code,
      version,
      materialCode,
      materialVersion,
      materialName,
      category,
      materialSpecs,
      preChangeContent,
      revisedContent,
      specificReasons
    }
  })
}

/**
 * @name 签出
 * @param {*} objCode bom 编码
 * @returns 
 */
// 签出
export function checkInToOut({
  objCode
}) {
  return request({
    url: `/api/repository/obj/checkInToOut/${objCode}`,
    method: 'POST',
  })
}


/**
 * @name 校验是否有权限修改
 * @param {*} objCode  bom 编码
 * @returns 
 */
export function checkIsCheckOutBySelf({
  objCode
}) {
  return request({
    url: `/api/repository/obj/checkIsCheckOutBySelf/${objCode}`,
    method: 'POST',
  })
}


/**
 * @name  获取当前对象签出信息
 * @param {*} objCode  bom 编码
 * @returns 
 */
export function getCheckOutObj({
  objCode
}) {
  return request({
    url: `/api/repository/obj/getCheckOutObj/${objCode}`,
    method: 'GET',
  })
}


// E-bom 比较 查询归档的数据
export function queryCompareEBomList(data) {
  return request({
    url: `/api/ProductEbomController/searchHisPage`,
    method: 'POST',
    data
  })
}


export function downloadTemplateForAll() {
  return request({
    method: 'get',
    url: `/api/ProductEbomController/downloadTemplateForAll`,
    responseType: 'blob',
  })
}

export function importData(data) {
  return request({
    url: `/api/ProductEbomController/importData`,
    method: 'POST',
    data
  })
}