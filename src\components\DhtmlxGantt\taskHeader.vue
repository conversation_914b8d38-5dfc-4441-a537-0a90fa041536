<template>
	<div class="taskHeader">
		<div class="el-select" @click.stop="openDialog">
			<el-input ref="reference" v-model="task.label" type="text" placeholder="选择负责人" readonly :validate-event="false">
				<template slot="suffix">
					<i v-show="!task.label" :class="['el-select__caret', 'el-input__icon', 'el-icon-arrow-up']"></i>
					<i v-show="task.label" class="el-select__caret el-input__icon el-icon-circle-close" @click="handleClearClick"></i>
				</template>
			</el-input>
		</div>
		<el-dialog :visible="dialogVisible" title="选择负责人">
			<div class="content-wrap">
				<el-tabs v-model="activeName" @tab-click="tabClick">
					<el-tab-pane label="全部数据" name="all">
						<el-tree :data="treeData" :props="{ children: 'children',label: 'fullName',isLeaf: 'isLeaf'}" check-on-click-node
						   @node-click="handleNodeClick" node-key="id" v-loading="loading" lazy :load="loadNode" :default-expanded-keys="defaultExpandedKeys">
							<span class="custom-tree-node" slot-scope="{ node, data }" :title="node.label">
								<i :class="data.icon"></i>
								<span class="text">{{node.label}}</span>
							</span>
						</el-tree>
					</el-tab-pane>
					<el-tab-pane label="当前组织" name="org">
						<div class="single-list" v-loading="loading">
							<template v-if="orgUserList.length">
								<div v-for="(item,index) in orgUserList" :key="index" class="selected-item-user" @click="handleNodeClick2(item)">
									<div class="selected-item-main">
										<el-avatar :size="36" :src="define.comUrl+item.headIcon" class="selected-item-headIcon">
										</el-avatar>
										<div class="selected-item-text">
											<p class="name">{{item.fullName}}</p>
											<p class="organize" :title="item.organize">{{item.organize}}</p>
										</div>
									</div>
								</div>
							</template>
							<el-empty description="暂无数据" :image-size="120" v-else></el-empty>
						</div>
					</el-tab-pane>
					<el-tab-pane label="我的下属" name="subordinates">
						<div class="single-list" v-loading="loading">
							<template v-if="subUserList.length">
								<div v-for="(item,index) in subUserList" :key="index" class="selected-item-user" @click="handleNodeClick2(item)">
									<div class="selected-item-main">
										<el-avatar :size="36" :src="define.comUrl+item.headIcon" class="selected-item-headIcon">
										</el-avatar>
										<div class="selected-item-text">
											<p class="name">{{item.fullName}}</p>
											<p class="organize" :title="item.organize">{{item.organize}}</p>
										</div>
									</div>
								</div>
							</template>
							<el-empty description="暂无数据" :image-size="120" v-else></el-empty>
						</div>
					</el-tab-pane>
				</el-tabs>
				<div class="right-wrap">
					<h3>已选</h3>
					<div class="single-list">
						<template v-if="selectedData.length">
							<div v-for="(item,index) in selectedData" :key="index" class="selected-item-user">
								<div class="selected-item-main">
									<el-avatar :size="36" :src="define.comUrl+item.headIcon" class="selected-item-headIcon">
									</el-avatar>
									<div class="selected-item-text">
										<p class="name">{{item.fullName}}</p>
										<p class="organize" :title="item.organize">{{item.organize}}</p>
									</div>
									<i class="el-icon-delete" @click="removeData(index)"></i>
								</div>
							</div>
						</template>
						<el-empty description="暂无数据" :image-size="120" v-else></el-empty>
					</div>
				</div>
			</div>

			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible=false">取消</el-button>
				<el-button type="primary" @click="confirm">确定</el-button>
			</span>
		</el-dialog>
		<!-- <el-input v-model="dataForm.value" placeholder="请输入xxx"></el-input> -->
	</div>
</template>
<script>
import { getImUserSelector, getOrganization, getSubordinates } from '@/api/permission/user'
export default {
	name: 'taskHeader',
	props: ['task'],
	data() {
		return {
			dataForm: {},
			dialogVisible: false,
			// innerValue: '',
			showClose: false,
			activeName: 'all',
			loading: false,
			nodeId: '0',
			treeData: [],
			defaultExpandedKeys: [],
			orgUserList: [],
			subUserList: [],
			selectedData: []
		}
	},
	methods: {
		openDialog() {
			this.dialogVisible = true
			this.getAllList()
		},
		handleClearClick(event) {
			this.$emit('update-task-prop', {})
			event.stopPropagation();
		},
		tabClick(tab) {
			switch (tab.name) {
				case 'all':
					this.getAllList()
					break;
				case 'org':
					this.getOrgData()
					break;
				case 'subordinates':
					this.getSubUserList()
					break;
			}
		},
		handleNodeClick(data) {
			if (data.type !== 'user') return
			this.handleNodeClick2(data)
		},
		handleNodeClick2(data) {
			const boo = this.selectedData.some(o => o.id === data.id)
			if (boo) return
			this.selectedData = [data]
		},
		removeData(index) {
			this.selectedData.splice(index, 1)
		},
		confirm() {
			if (!this.selectedData.length) {
				this.$emit('update-task-prop', {})
				this.dialogVisible = false
				return
			}
			// let selectedIds = this.selectedData.map(o => o.id)
			this.$emit('update-task-prop', this.selectedData[0])
			// this.$emit('change', selectedIds[0], this.selectedData[0])
			this.dialogVisible = false
		},
		getSubUserList() {
			this.loading = true
			getSubordinates().then(res => {
				this.subUserList = res.data || []
				this.loading = false
			})
		},
		getOrgData() {
			this.loading = true
			getOrganization({ organizeId: '0' }).then(res => {
				this.orgUserList = res.data || []
				this.loading = false
			})
		},
		getAllList() {
			this.loading = true
			this.nodeId = '0'
			getImUserSelector(this.nodeId, this.pagination).then(res => {
				this.treeData = res.data.list || []
				this.loading = false
				if (this.treeData.length && this.nodeId == '0') {
					this.defaultExpandedKeys = [this.treeData[0].id]
				}
			})
		},
		loadNode(node, resolve) {
			if (this.dialogVisible === false) return []
			if (node.level === 0) {
				this.nodeId = '0'
				return resolve(this.treeData)
			}
			this.nodeId = node.data.id
			getImUserSelector(this.nodeId).then(res => {
				resolve(res.data.list)
			})
		},
	}
}
</script>
<style lang="scss" scoped>
.taskHeader {
  padding: 0 8px;
}
.el-dialog {
  .content-wrap {
    //   height: 400px;
    display: flex;
    .el-tabs {
      flex: 1;
      border: 1px solid #dcdfe6;
      padding: 5px 10px;
      margin-right: 20px;
      ::v-deep .el-tabs__content {
        height: 350px;
        overflow: auto;
      }
    }
    .right-wrap {
      width: 40%;
      border: 1px solid #dcdfe6;
      padding: 10px;
      h3 {
        height: 40px;
      }
    }
    .single-list {
      .selected-item-user {
        cursor: pointer;
        padding: 0 12px;
        &:hover {
          background-color: #f4f6f9;
        }
        .selected-item-main {
          display: flex;
          border-bottom: 1px solid #f4f6f9;
          align-items: center;
          height: 50px;
          .selected-item-text {
            flex: 1;
            margin-left: 10px;
            .name,
            .organize {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .name {
              height: 20px;
              line-height: 20px;
              font-size: 14px;
              margin-bottom: 2px;
            }
            .organize {
              height: 17px;
              line-height: 17px;
              color: #999;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>