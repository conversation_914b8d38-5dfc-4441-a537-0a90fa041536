import request from '@/utils/request'

// 工作中心---分页查询
export function queryWorkList(data) {
  return request({
    url: `/api/ProductWorkCenterController/queryByLike`,
    method: 'POST',
    data
  })
}
// 工作中心---新增
export function addWorkList(data) {
  return request({
    url: `/api/ProductWorkCenterController/add`,
    method: 'POST',
    data
  })
}
// 工作中心---删除
export function deleteWorkItem(id) {
  return request({
    url: `/api/ProductWorkCenterController/${id}`,
    method: 'DELETE',
  })
}
// 工作中心---修改
export function updateWorkItem(data) {
  return request({
    url: `/api/ProductWorkCenterController/update`,
    method: 'POST',
    data
  })
}
// 工作中心---根据 id 查询
export function queryWorkItem(id) {
  return request({
    url: `/api/ProductWorkCenterController/detail/${id}`,
    method: 'GET',
  })
}