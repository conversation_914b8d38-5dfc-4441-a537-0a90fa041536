import request from '@/utils/request'

/**
 * @name 修订任务列表
 * @param {*} param0.taskCode 任务code
 * @param {*} param0.taskType 1=修订任务 2=实施方案编制任务
 * @param {*} param0.taskName 任务名称
 * @param {*} param0.pageSize 条数
 * @param {*} param0.currentPage 当前页
 * @param {*} param0.personCharge 责任人
 * @param {*} param0.isCompleted 是否已完成
 * @returns 
 */
export const getTaskListByUser = ({
  taskCode,
  taskType,
  taskName,
  pageSize,
  currentPage,
  selectAll,
  personCharge,
  isCompleted
}) => request({
  url: '/api/instructionTask/getTaskListByUser',
  method: 'GET',
  data: {
    taskCode,
    taskType,
    taskName,
    pageSize,
    currentPage,
    selectAll,
    personCharge,
    isCompleted
  }
})

/**
 * @name 获取变更实施计划/任务
 * @param {*} param0.pageSize
 * @param {*} param0.currentPage
 * @param {*} param0.taskCode
 * @param {*} param0.taskName
 * @param {*} param0.taskStatus
 * @param {*} param0.personResponsible 责任人
 * @returns 
 */
export const getMyPlanTasks = ({
  pageSize,
  currentPage,
  taskCode,
  taskName,
  taskStatus,
  selectAll,
  personResponsible,
  planName
}) => request({
  url: '/api/changePlan/task/getMyPlanTasks',
  method: 'GET',
  data: {
    pageSize,
    currentPage,
    taskCode,
    taskName,
    taskStatus,
    selectAll,
    personResponsible,
    planName
  }
})

/**
 * @name 获取进度列表
 * @param {*} param0.id ID
 * @returns 
 */
export const getProgressInfo = ({ id }) => request({
  url: `/api/changePlan/task/getProgressInfo/${id}`,
  data: "GET"
})