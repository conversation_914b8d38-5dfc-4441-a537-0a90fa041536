import request from '@/utils/request'

// rbom --- 新增
export function addRBOMList(data) {
  return request({
    url: `/api/ProductRbomController/save`,
    method: 'POST',
    data
  })
}

// rbom --- 编辑
export function editRBOMList(data) {
  return request({
    url: `/api/ProductRbomController/save`,
    method: 'PUT',
    data
  })
}

// rbom --- 对象归属目录
export function getCatalogue(data) {
  return request({
    url: `/api/system/serviceConfiguration/type/tree/list/byTypes`,
    method: 'POST',
    data
  })
}

// rbom --- 提交
export function sumbitRBOM(data) {
  return request({
    url: `/api/ProductRbomController/submit`,
    method: 'POST',
    data
  })
}

// rbom --- 删除单条数据
export function deleteRBOM(id) {
  return request({
    url: `/api/ProductRbomController/${id}`,
    method: 'DELETE',
  })
}

// rbom --- 分页查询
export function queryRBOM(params) {
  return request({
    url: `/api/ProductRbomController/queryByLike`,
    method: 'GET',
    data: params
  })
}

// rbom --- 查询详情
export function queryDetails(id) {
  return request({
    url: `/api/ProductRbomController/detail/${id}`,
    method: 'GET',
  })
}




// BOM 详情页面的接口


// rbom --- 导入需求单(数据模板导入)
export function dataTemplate(data) {
  return request({
    url: `/api/ProductRbomLibraryController/import?rbomCode=${data.rbomCode}&rbomVersion=${data.rbomVersion}&version=${data.version}&id=${data.id}&isNewVersion=${data.isNewVersion}&major=${data.major}`,
    method: 'GET',
  })
}

// 获取需求档案下拉列表
export function getCatalogueList(data) {
  // &startTime=${data.startTime}&endTime=${data.endTime}
  return request({
    url: `/api/demand/selector/page?demandName=${data.demandName}&startTime=${data.startTime}&endTime=${data.endTime}&demandCode=${data.demandCode}&productType=${data.productType}&createBy=${data.createBy}&effectiveness=&demandSource=${data.sourceList}&pageSize=${data.pageSize}&sort=DESC&sidx=&currentPage=${data.currentPage}&keyword=`,
    method: 'GET',
  })
}

// rbom --- 查询详情
export function queryRBOMDetails(id) {
  return request({
    url: `/api/demand/info?id=${id}&isNewVersion=false&major=1&version=0`,
    method: 'GET',
  })
}

// rbom --- BOM 左侧目录操作 （新增目录）
export function addDirectory(data) {
  return request({
    url: `/api/ProductRbomLibraryController/save`,
    method: 'POST',
    data
  })
}

// rbom --- BOM 左侧目录操作 （删除目录）
export function deleteDirectory(id) {
  return request({
    url: `/api/ProductRbomLibraryController/${id}`,
    method: 'DELETE',
  })
}

// rbom --- BOM 左侧目录操作 （修改目录）
export function modifyDirectory(data) {
  return request({
    url: `/api/ProductRbomLibraryController/update`,
    method: 'PUT',
    data
  })
}

// rbom --- BOM 左侧目录操作 （查询目录）
export function queryDirectory(data) {
  return request({
    url: `/api/ProductRbomLibraryController/queryAll?code=${data.code}&version=${data.version}&id=${data.id}`,
    method: 'GET',
  })
}


// BOM 列表数据的增删改查

/**
 * @name r-bom需求项列表新增
 * @param {*} rbomCode rbom 主表关联编码 
 * @param {*} rbomLibraryId r_bom_library目录表关联id
 * @param {*} demandName 需求名
 * @param {*} featureItemId 需求特征项
 * @param {*} featureItemName 需求特征项名
 * @param {*} featureItemUnit 需求特征单位 
 * @param {*} featureItemUnitName 需求特征单位名 
 * @param {*} calculationMethod 需求特征项范围
 * @param {*} calculationMethodName 需求特征范围名
 * @param {*} dataType 需求特征项值类型
 * @param {*} dataTypeName 需求特征项值类型名
 * @param {*} featureValue 需求特征项值
 * @param {*} demandType 需求类型 字典表--demand_type
 * @param {*} demandDesignValue 需求设计值
 * @param {*} demandDesignExplain 需求设计说明
 * @param {*} calculationExpression 计算表达式
 * @param {*} remark 备注
 * @param {*} version 版本
 * @param {*} productRbomLibraryDataFileDTOList 附件列表
 * @param {*} productRbomLibraryDataAttributeList 扩展属性列表
 * @returns 
 */
// 数据新增
export function addRequirementList({

  rbomCode,
  rbomLibraryId,
  demandName,
  featureItemId,
  featureItemName,
  featureItemCode,
  featureItemUnit,
  featureItemUnitName,
  calculationMethod,
  calculationMethodName,
  dataType,
  dataTypeName,
  featureValue,
  demandType,
  demandDesignValue,
  demandDesignExplain,
  calculationExpression,
  remark,
  version,
  productRbomLibraryDataFileDTOList,
  productRbomLibraryDataAttributeList,
}) {
  return request({
    url: `/api/ProductRbomLibraryDataController/save`,
    method: 'POST',
    data: {

      rbomCode,
      rbomLibraryId,
      demandName,
      featureItemId,
      featureItemName,
      featureItemUnit,
      featureItemCode,
      featureItemUnitName,
      calculationMethod,
      calculationMethodName,
      dataType,
      dataTypeName,
      featureValue,
      demandType,
      demandDesignValue,
      demandDesignExplain,
      calculationExpression,
      remark,
      version,
      productRbomLibraryDataFileDTOList,
      productRbomLibraryDataAttributeList,
    }
  })
}


// 删除数据
export function deleteRequirementList(id) {
  return request({
    url: `/api/ProductRbomLibraryDataController/${id}`,
    method: 'DELETE',
  })
}

// 修改数据
export function updateRequirementList(data) {
  return request({
    url: `/api/ProductRbomLibraryDataController/update`,
    method: 'PUT',
    data
  })
}

// 查询数据(分页查询)
export function queryRequirementList(data) {
  return request({
    url: `/api/ProductRbomLibraryDataController/queryByLike?demandName=${data.demandName}&demandCode=${data.demandCode}&rbomLibraryId=${data.rbomLibraryId}&pageSize=${data.pageSize}&sort=DESC&sidx=&currentPage=${data.currentPage}&keyword=`,
    method: 'GET',
  })
}

// 数据详情
export function requirementListDetails(id) {
  return request({
    url: `/api/ProductRbomLibraryDataController/${id}`,
    method: 'GET',
  })
}