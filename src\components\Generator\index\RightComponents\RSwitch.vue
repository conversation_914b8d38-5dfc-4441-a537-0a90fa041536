<template>
  <el-row>
    <el-form-item label="控件标题">
      <el-input v-model="activeData.__config__.label" placeholder="请输入控件标题" />
    </el-form-item>
    <el-form-item label="显示标题">
      <el-switch v-model="activeData.__config__.showLabel" />
    </el-form-item>
    <el-form-item label="标题提示">
      <el-input v-model="activeData.__config__.tipLabel" placeholder="请输入标题提示" />
    </el-form-item>
    <el-form-item label="开启展示值">
      <el-input v-model="activeData.activeTxt" placeholder="请输入开启展示值" />
    </el-form-item>
    <el-form-item label="关闭展示值">
      <el-input v-model="activeData.inactiveTxt" placeholder="请输入关闭展示值" />
    </el-form-item>
    <el-form-item label="默认值">
      <el-switch v-model="activeData.__config__.defaultValue" :activeValue="activeData.activeValue"
        :inactiveValue="activeData.inactiveValue" />
    </el-form-item>
    <el-form-item label="是否禁用">
      <el-switch v-model="activeData.disabled" />
    </el-form-item>
    <el-form-item label="是否隐藏">
      <el-switch v-model="activeData.__config__.noShow" />
    </el-form-item>
    <el-form-item label="是否必填">
      <el-switch v-model="activeData.__config__.required" />
    </el-form-item>
  </el-row>
</template>
<script>
import comMixin from './mixin'
export default {
  props: ['activeData'],
  mixins: [comMixin],
  data() {
    return {}
  },
  created() { },
  methods: {}
}
</script>